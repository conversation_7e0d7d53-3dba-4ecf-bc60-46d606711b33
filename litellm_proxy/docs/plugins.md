# 插件开发指南

## 概述

LiteLLM Proxy Toolkit 采用插件化架构，所有功能都作为独立的插件实现。这使得系统具有高度的可扩展性和灵活性。

## 插件架构

### 基础类 BaseAddon

所有插件都必须继承自 `BaseAddon` 类：

```python
from proxy_toolkit.core.base_addon import BaseAddon

class MyCustomAddon(BaseAddon):
    PLUGIN_NAME = "my_custom_addon"
    
    def __init__(self, name: str):
        super().__init__(name)
```

### 必需方法

每个插件必须实现以下方法：

#### 1. get_config_schema()

返回插件的配置模式：

```python
def get_config_schema(self) -> Dict[str, Any]:
    return {
        "my_option": {
            "type": "string",
            "default": "default_value",
            "description": "Description of this option"
        }
    }
```

#### 2. _should_process_request_impl()

决定是否处理特定请求：

```python
def _should_process_request_impl(self, flow: http.HTTPFlow) -> bool:
    # 实现你的匹配逻辑
    return self._match_host(flow, "*.example.com")
```

### 可选方法

#### 处理请求和响应

```python
def _process_request(self, flow: http.HTTPFlow) -> bool:
    """处理请求，返回True表示请求被修改"""
    # 修改请求
    flow.request.headers["X-Custom"] = "value"
    return True

def _process_response(self, flow: http.HTTPFlow) -> bool:
    """处理响应，返回True表示响应被修改"""
    # 修改响应
    flow.response.headers["X-Processed"] = "true"
    return True
```

#### 生命周期方法

```python
def configure(self, config: Dict[str, Any]) -> None:
    """配置插件"""
    super().configure(config)
    self.my_option = config.get('my_option', 'default')

def running(self) -> None:
    """mitmproxy启动时调用"""
    self.logger.info("My plugin started")

def done(self) -> None:
    """mitmproxy关闭时调用"""
    self.logger.info("My plugin stopped")
```

## 工具方法

BaseAddon 提供了一些有用的工具方法：

### 主机匹配

```python
if self._match_host(flow, "*.googleapis.com"):
    # 匹配所有googleapis.com子域名
    pass
```

### 路径匹配

```python
if self._match_path(flow, "/api/v1/*"):
    # 匹配所有以/api/v1/开头的路径
    pass
```

## 日志记录

每个插件都有一个专用的日志记录器：

```python
# 基础日志
self.logger.info("Processing request", url=flow.request.pretty_url)
self.logger.error("Error occurred", error=str(e))

# 专用方法
self.logger.request(flow.request.method, flow.request.pretty_url)
self.logger.response(flow.response.status_code, flow.request.pretty_url)
self.logger.intercept("action_name", flow.request.pretty_url, extra_data="value")
```

## 统计信息

插件会自动收集统计信息：

```python
# 自动统计
self.stats['requests_processed']  # 处理的请求数
self.stats['requests_modified']   # 修改的请求数
self.stats['errors']              # 错误数

# 获取统计信息
stats = self.get_stats()
```

## 完整示例

```python
from typing import Dict, Any
from mitmproxy import http
from proxy_toolkit.core.base_addon import BaseAddon

class ExampleAddon(BaseAddon):
    """示例插件：为特定域名添加自定义header"""
    
    PLUGIN_NAME = "example_addon"
    
    def __init__(self, name: str):
        super().__init__(name)
        self.target_domain = "example.com"
        self.custom_header = "X-Example"
        self.header_value = "processed"
    
    def get_config_schema(self) -> Dict[str, Any]:
        return {
            "target_domain": {
                "type": "string",
                "default": "example.com",
                "description": "Target domain to process"
            },
            "custom_header": {
                "type": "string", 
                "default": "X-Example",
                "description": "Header name to add"
            },
            "header_value": {
                "type": "string",
                "default": "processed", 
                "description": "Header value to add"
            }
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        super().configure(config)
        self.target_domain = config.get('target_domain', 'example.com')
        self.custom_header = config.get('custom_header', 'X-Example')
        self.header_value = config.get('header_value', 'processed')
    
    def _should_process_request_impl(self, flow: http.HTTPFlow) -> bool:
        return self._match_host(flow, f"*.{self.target_domain}")
    
    def _process_request(self, flow: http.HTTPFlow) -> bool:
        flow.request.headers[self.custom_header] = self.header_value
        self.logger.info("Added custom header", 
                        header=self.custom_header,
                        value=self.header_value,
                        url=flow.request.pretty_url)
        return True
```

## 插件注册

插件会被自动发现和注册，只需要：

1. 将插件文件放在 `src/proxy_toolkit/addons/` 目录
2. 在 `__init__.py` 中导入插件类
3. 在配置文件中启用插件

## 最佳实践

1. **错误处理**: 总是使用try-catch包装可能失败的操作
2. **性能考虑**: 避免在请求处理中进行耗时操作
3. **日志记录**: 提供足够的日志信息用于调试
4. **配置验证**: 在configure方法中验证配置参数
5. **统计信息**: 记录有用的统计信息用于监控
