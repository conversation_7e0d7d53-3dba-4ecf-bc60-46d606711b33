# LiteLLM Proxy Toolkit

一个可扩展的代理工具包，专为 LiteLLM 集成设计，支持多种代理功能的组合使用。

## 功能特性

### 🔌 插件化架构
- **模块化设计**: 每个功能作为独立插件实现
- **灵活配置**: 可单独启用/禁用任何功能
- **优先级控制**: 支持插件执行顺序控制
- **热重载**: 支持配置文件热重载

### 🔒 OAuth2 拦截
- 拦截 googleapis.com 的 OAuth2 认证请求
- 返回伪造的成功响应
- 支持自定义 token 和生命周期
- 专为 gemini-cli + LiteLLM 集成优化

### 🔧 Header 修改
- 基于规则的 HTTP header 修改
- 支持添加、删除、替换 header
- 支持正则表达式和通配符匹配
- 支持变量替换

### 🔀 路由重定向
- URL 路由重写和重定向
- 支持域名和路径重写
- 支持通配符和正则表达式
- 保持查询参数选项

### 📝 请求日志
- 结构化日志记录
- 可配置的日志级别和格式
- 请求/响应详细记录
- 性能统计和监控

## 安装

确保已安装 uv：

```bash
# 安装 uv（如果尚未安装）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 进入项目目录
cd litellm_proxy

# 安装依赖
uv sync

# 生成默认配置
uv run proxy-toolkit init-config
```

## 使用方法

### 1. 查看可用插件

```bash
# 查看所有插件状态
uv run proxy-toolkit plugins

# 查看配置文件
cat config.yaml
```

### 2. 启动代理

```bash
# 使用默认配置启动
uv run proxy-toolkit start

# 指定配置文件
uv run proxy-toolkit start --config custom-config.yaml

# 指定端口和详细日志
uv run proxy-toolkit start --port 8888 --verbose

# 使用独立脚本
python scripts/start_proxy.py
```

### 3. 配置 gemini-cli

设置环境变量让 gemini-cli 使用代理：

```bash
export HTTP_PROXY=http://localhost:8888
export HTTPS_PROXY=http://localhost:8888
export GOOGLE_GENAI_USE_VERTEXAI=true
export GOOGLE_VERTEX_BASE_URL="http://************:4000/vertex_ai"
export GOOGLE_CLOUD_PROJECT="program-center"
export GOOGLE_CLOUD_LOCATION="us-central1"
export GOOGLE_APPLICATION_CREDENTIALS="/home/<USER>/.gemini/credentials.json"
```

### 3. 运行 gemini-cli

正常运行 gemini-cli，OAuth2 认证请求会被自动拦截和伪造。

## 配置文件

可以通过配置文件自定义行为：

```yaml
# config.yaml
proxy:
  port: 8888
  host: "0.0.0.0"
  
oauth:
  token_lifetime: 3600  # token 有效期（秒）
  scope: "https://www.googleapis.com/auth/cloud-platform"
  
logging:
  level: "INFO"
  file: "oauth_interceptor.log"
```

## 开发

```bash
# 安装开发依赖
uv sync --group dev

# 运行测试
uv run pytest

# 代码格式化
uv run black .
uv run isort .

# 类型检查
uv run mypy .
```

## 工作原理

1. mitmproxy 启动并监听指定端口
2. gemini-cli 通过代理发送请求
3. 拦截到 `googleapis.com/oauth2/v4/token` 请求时，返回伪造的成功响应
4. 其他请求正常转发
5. gemini-cli 获得假 token 后，继续发送 API 请求到 LiteLLM

## 快速开始

### 1. 环境准备

```bash
# 进入项目目录
cd litellm_proxy

# 运行设置脚本
./scripts/setup_env.sh
```

### 2. 启动代理

```bash
# 方式1：使用CLI命令
uv run oauth-interceptor start

# 方式2：使用独立脚本
python scripts/start_proxy.py

# 方式3：指定端口和详细日志
uv run oauth-interceptor start --port 8888 --verbose
```

### 3. 配置gemini-cli环境

在新的终端窗口中设置环境变量：

```bash
export HTTP_PROXY=http://localhost:8888
export HTTPS_PROXY=http://localhost:8888
export GOOGLE_GENAI_USE_VERTEXAI=true
export GOOGLE_VERTEX_BASE_URL="http://************:4000/vertex_ai"
export GOOGLE_CLOUD_PROJECT="program-center"
export GOOGLE_CLOUD_LOCATION="us-central1"
export GOOGLE_APPLICATION_CREDENTIALS="/home/<USER>/.gemini/credentials.json"
```

### 4. 运行gemini-cli

```bash
# 现在运行gemini-cli，OAuth认证请求会被自动拦截
gemini-cli "Hello, world!"
```

### 5. 验证和调试

```bash
# 检查代理状态
uv run oauth-interceptor status

# 测试拦截功能
uv run oauth-interceptor test

# 查看详细日志（在代理运行时）
tail -f oauth_interceptor.log
```

## 工作原理

1. **启动阶段**：mitmproxy启动并加载OAuth拦截addon
2. **请求拦截**：当gemini-cli发送OAuth2认证请求到`*.googleapis.com/oauth2/*/token`时被拦截
3. **伪造响应**：返回包含假token的成功OAuth2响应
4. **API转发**：gemini-cli获得假token后，实际API请求通过LiteLLM转发到VertexAI

## 故障排除

### 代理无法启动
- 检查端口是否被占用：`lsof -i :8888`
- 确保mitmproxy已安装：`uv run mitmdump --version`

### gemini-cli仍然失败
- 确认代理环境变量已设置
- 检查代理日志中是否有拦截记录
- 验证LiteLLM配置是否正确

### 证书错误
- 对于测试环境，可以忽略SSL验证
- 或者安装mitmproxy的CA证书

## 注意事项

- 仅用于开发和测试环境
- 确保 LiteLLM 代理正确配置
- 代理会生成自签名证书，需要信任或忽略证书警告
- 假token仅用于绕过认证检查，实际API调用通过LiteLLM处理
