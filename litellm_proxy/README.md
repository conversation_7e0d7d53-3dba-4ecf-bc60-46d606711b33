# OAuth Interceptor for Gemini-CLI

这个工具使用 mitmproxy 拦截 gemini-cli 的 OAuth2 认证请求，伪造成功响应，让 gemini-cli 能够通过 LiteLLM 代理访问 VertexAI。

## 问题背景

当使用 gemini-cli 配置 VertexAI 模式时，内部的 @google/genai 库会尝试访问 `https://www.googleapis.com/oauth2/v4/token` 进行认证。在企业环境中，这个请求可能无法直接访问，导致认证失败。

## 解决方案

本工具通过 mitmproxy 拦截这些认证请求，返回伪造的成功响应，让 gemini-cli 认为认证成功，然后实际的 API 请求会通过 LiteLLM 代理转发到 VertexAI。

## 功能特性

- 🔒 拦截 OAuth2 认证请求
- 🎭 伪造认证成功响应
- 📝 详细的请求日志
- ⚙️ 灵活的配置选项
- 🚀 简单的启动/停止脚本

## 安装

确保已安装 uv：

```bash
# 安装 uv（如果尚未安装）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 进入项目目录
cd litellm_proxy

# 安装依赖
uv sync
```

## 使用方法

### 1. 启动代理

```bash
# 使用默认配置启动
uv run oauth-interceptor start

# 指定端口启动
uv run oauth-interceptor start --port 8888

# 启用详细日志
uv run oauth-interceptor start --verbose
```

### 2. 配置 gemini-cli

设置环境变量让 gemini-cli 使用代理：

```bash
export HTTP_PROXY=http://localhost:8888
export HTTPS_PROXY=http://localhost:8888
export GOOGLE_GENAI_USE_VERTEXAI=true
export GOOGLE_VERTEX_BASE_URL="http://************:4000/vertex_ai"
export GOOGLE_CLOUD_PROJECT="program-center"
export GOOGLE_CLOUD_LOCATION="us-central1"
export GOOGLE_APPLICATION_CREDENTIALS="/home/<USER>/.gemini/credentials.json"
```

### 3. 运行 gemini-cli

正常运行 gemini-cli，OAuth2 认证请求会被自动拦截和伪造。

## 配置文件

可以通过配置文件自定义行为：

```yaml
# config.yaml
proxy:
  port: 8888
  host: "0.0.0.0"
  
oauth:
  token_lifetime: 3600  # token 有效期（秒）
  scope: "https://www.googleapis.com/auth/cloud-platform"
  
logging:
  level: "INFO"
  file: "oauth_interceptor.log"
```

## 开发

```bash
# 安装开发依赖
uv sync --group dev

# 运行测试
uv run pytest

# 代码格式化
uv run black .
uv run isort .

# 类型检查
uv run mypy .
```

## 工作原理

1. mitmproxy 启动并监听指定端口
2. gemini-cli 通过代理发送请求
3. 拦截到 `googleapis.com/oauth2/v4/token` 请求时，返回伪造的成功响应
4. 其他请求正常转发
5. gemini-cli 获得假 token 后，继续发送 API 请求到 LiteLLM

## 快速开始

### 1. 环境准备

```bash
# 进入项目目录
cd litellm_proxy

# 运行设置脚本
./scripts/setup_env.sh
```

### 2. 启动代理

```bash
# 方式1：使用CLI命令
uv run oauth-interceptor start

# 方式2：使用独立脚本
python scripts/start_proxy.py

# 方式3：指定端口和详细日志
uv run oauth-interceptor start --port 8888 --verbose
```

### 3. 配置gemini-cli环境

在新的终端窗口中设置环境变量：

```bash
export HTTP_PROXY=http://localhost:8888
export HTTPS_PROXY=http://localhost:8888
export GOOGLE_GENAI_USE_VERTEXAI=true
export GOOGLE_VERTEX_BASE_URL="http://************:4000/vertex_ai"
export GOOGLE_CLOUD_PROJECT="program-center"
export GOOGLE_CLOUD_LOCATION="us-central1"
export GOOGLE_APPLICATION_CREDENTIALS="/home/<USER>/.gemini/credentials.json"
```

### 4. 运行gemini-cli

```bash
# 现在运行gemini-cli，OAuth认证请求会被自动拦截
gemini-cli "Hello, world!"
```

### 5. 验证和调试

```bash
# 检查代理状态
uv run oauth-interceptor status

# 测试拦截功能
uv run oauth-interceptor test

# 查看详细日志（在代理运行时）
tail -f oauth_interceptor.log
```

## 工作原理

1. **启动阶段**：mitmproxy启动并加载OAuth拦截addon
2. **请求拦截**：当gemini-cli发送OAuth2认证请求到`*.googleapis.com/oauth2/*/token`时被拦截
3. **伪造响应**：返回包含假token的成功OAuth2响应
4. **API转发**：gemini-cli获得假token后，实际API请求通过LiteLLM转发到VertexAI

## 故障排除

### 代理无法启动
- 检查端口是否被占用：`lsof -i :8888`
- 确保mitmproxy已安装：`uv run mitmdump --version`

### gemini-cli仍然失败
- 确认代理环境变量已设置
- 检查代理日志中是否有拦截记录
- 验证LiteLLM配置是否正确

### 证书错误
- 对于测试环境，可以忽略SSL验证
- 或者安装mitmproxy的CA证书

## 注意事项

- 仅用于开发和测试环境
- 确保 LiteLLM 代理正确配置
- 代理会生成自签名证书，需要信任或忽略证书警告
- 假token仅用于绕过认证检查，实际API调用通过LiteLLM处理
