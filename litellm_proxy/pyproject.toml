[project]
name = "oauth-interceptor"
version = "0.1.0"
description = "OAuth2 token interceptor for gemini-cli using mitmproxy"
authors = [
    {name = "User", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "mitmproxy>=9.0.0,<10.0.0",
    "click>=8.0.0",
    "pyyaml>=6.0",
    "colorama>=0.4.0",
    "requests>=2.28.0",
    "psutil>=5.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "isort>=5.0.0",
    "mypy>=1.0.0",
]

[project.scripts]
oauth-interceptor = "oauth_interceptor.main:cli"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
