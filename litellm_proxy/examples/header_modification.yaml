# Header修改功能配置示例
# 展示如何使用Header修改插件

proxy:
  host: 127.0.0.1
  port: 8888
  mode: regular

logging:
  level: DEBUG
  file: header_modifier.log
  format: structured
  console: true

plugins:
  # OAuth拦截插件 - 启用
  oauth_interceptor:
    enabled: true
    priority: 100
    config:
      token_lifetime: 3600
      scope: https://www.googleapis.com/auth/cloud-platform
      fake_token: fake_access_token_for_litellm_proxy
      token_type: Bearer
  
  # Header修改插件 - 启用并配置规则
  header_modifier:
    enabled: true
    priority: 200
    config:
      rules:
        # 规则1: 为所有googleapis.com请求添加自定义header
        - match:
            host: "*.googleapis.com"
          actions:
            add_headers:
              "X-Custom-Client": "LiteLLM-Proxy-Toolkit"
              "X-Request-ID": "{method}-{path}"
            remove_headers:
              - "User-Agent"
        
        # 规则2: 为特定API路径替换Authorization header
        - match:
            host: "*.googleapis.com"
            path: "/v1/*"
            method: "POST"
          actions:
            replace_headers:
              "Authorization": "Bearer replaced-token-for-v1-api"
        
        # 规则3: 修改Content-Type header
        - match:
            path: "*/upload"
          actions:
            modify_headers:
              "Content-Type":
                pattern: "application/json"
                replacement: "application/json; charset=utf-8"
  
  # 路由重定向插件 - 禁用
  route_redirector:
    enabled: false
    priority: 300
    config:
      rules: []
  
  # 请求日志插件 - 启用详细日志
  request_logger:
    enabled: true
    priority: 999
    config:
      log_requests: true
      log_responses: true
      log_headers: true
      log_body: false
      max_body_size: 1024
      exclude_paths:
        - /health
