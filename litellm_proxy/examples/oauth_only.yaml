# OAuth拦截专用配置
# 仅启用OAuth拦截功能，其他功能全部禁用

proxy:
  host: 127.0.0.1
  port: 8888
  mode: regular

logging:
  level: INFO
  file: oauth_interceptor.log
  format: simple
  console: true

plugins:
  # OAuth拦截插件 - 启用
  oauth_interceptor:
    enabled: true
    priority: 100
    config:
      token_lifetime: 3600
      scope: https://www.googleapis.com/auth/cloud-platform
      fake_token: fake_access_token_for_litellm_proxy
      token_type: Bearer
  
  # Header修改插件 - 禁用
  header_modifier:
    enabled: false
    priority: 200
    config:
      rules: []
  
  # 路由重定向插件 - 禁用
  route_redirector:
    enabled: false
    priority: 300
    config:
      rules: []
  
  # 请求日志插件 - 启用基础日志
  request_logger:
    enabled: true
    priority: 999
    config:
      log_requests: true
      log_responses: false
      log_headers: false
      log_body: false
      exclude_paths:
        - /health
        - /metrics
