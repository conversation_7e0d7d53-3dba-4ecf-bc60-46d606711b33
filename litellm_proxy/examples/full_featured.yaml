# 全功能配置示例
# 启用所有插件功能的完整配置

proxy:
  host: 127.0.0.1
  port: 8888
  mode: regular

logging:
  level: INFO
  file: proxy_toolkit_full.log
  format: structured
  console: true

plugins:
  # OAuth拦截插件 - 启用
  oauth_interceptor:
    enabled: true
    priority: 100
    config:
      token_lifetime: 7200  # 2小时
      scope: https://www.googleapis.com/auth/cloud-platform
      fake_token: enterprise_fake_token_12345
      token_type: Bearer
  
  # Header修改插件 - 启用
  header_modifier:
    enabled: true
    priority: 200
    config:
      rules:
        # 为所有API请求添加企业标识
        - match:
            host: "*.googleapis.com"
          actions:
            add_headers:
              "X-Enterprise-Client": "LiteLLM-Enterprise"
              "X-Proxy-Version": "2.0"
        
        # 移除可能暴露内部信息的header
        - match:
            host: "*"
          actions:
            remove_headers:
              - "X-Forwarded-For"
              - "X-Real-IP"
  
  # 路由重定向插件 - 启用
  route_redirector:
    enabled: true
    priority: 300
    config:
      rules:
        # 将旧API域名重定向到新域名
        - match:
            host: "old-api.googleapis.com"
            path: "/v1/*"
          redirect:
            host: "generativelanguage.googleapis.com"
            path: "/v1beta/{path}"
            preserve_query: true
        
        # 将HTTP请求重定向到HTTPS
        - match:
            scheme: "http"
            host: "*.googleapis.com"
          redirect:
            scheme: "https"
            preserve_query: true
  
  # 请求日志插件 - 启用完整日志
  request_logger:
    enabled: true
    priority: 999
    config:
      log_requests: true
      log_responses: true
      log_headers: true
      log_body: true
      max_body_size: 2048
      exclude_paths:
        - /health
        - /metrics
        - /favicon.ico
