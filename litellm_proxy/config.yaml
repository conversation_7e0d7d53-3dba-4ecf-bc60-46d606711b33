logging:
  console: true
  file: proxy_toolkit.log
  format: structured
  level: INFO
plugins:
  header_modifier:
    config:
      rules: []
    enabled: false
    priority: 200
  oauth_interceptor:
    config:
      fake_token: fake_access_token_for_litellm_proxy
      scope: https://www.googleapis.com/auth/cloud-platform
      token_lifetime: 3600
      token_type: Bearer
    enabled: true
    priority: 100
  request_logger:
    config:
      exclude_paths:
      - /health
      - /metrics
      log_requests: true
      log_responses: false
    enabled: true
    priority: 999
  route_redirector:
    config:
      rules: []
    enabled: false
    priority: 300
proxy:
  host: 127.0.0.1
  mode: regular
  port: 8888
