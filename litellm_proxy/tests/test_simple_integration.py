#!/usr/bin/env python3
"""Simple integration test for OAuth Interceptor."""

import sys
import time
import subprocess
import requests
from pathlib import Path

def test_oauth_interceptor():
    """Test OAuth interceptor with a simple approach."""
    print("🧪 Testing OAuth Interceptor...")
    
    # Get paths
    base_dir = Path(__file__).parent.parent
    addon_path = base_dir / "src" / "oauth_interceptor" / "addons" / "oauth_interceptor.py"
    
    if not addon_path.exists():
        print(f"❌ Addon not found at {addon_path}")
        return False
    
    # Start mitmproxy in background
    port = 18890
    cmd = [
        'mitmdump',
        '--listen-host', '127.0.0.1',
        '--listen-port', str(port),
        '--scripts', str(addon_path),
        '--set', 'oauth_fake_token=integration_test_token',
    ]
    
    print(f"🚀 Starting proxy on port {port}...")
    process = None
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for proxy to start
        print("⏳ Waiting for proxy to start...")
        time.sleep(3)
        
        # Test 1: Check if proxy is accessible
        print("🔍 Test 1: Checking proxy accessibility...")
        try:
            response = requests.get(
                'http://httpbin.org/ip',
                proxies={'http': f'http://127.0.0.1:{port}'},
                timeout=5
            )
            if response.status_code == 200:
                print("✅ Proxy is accessible")
            else:
                print(f"❌ Proxy returned status {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Cannot access proxy: {e}")
            return False
        
        # Test 2: Test OAuth interception (simulate with a mock request)
        print("🔍 Test 2: Testing OAuth request detection...")
        
        # Since we can't easily test HTTPS interception without certificates,
        # let's test the addon logic directly
        from oauth_interceptor.addons.oauth_interceptor import OAuthInterceptor
        from unittest.mock import Mock
        from urllib.parse import urlparse
        
        addon = OAuthInterceptor()
        
        # Test OAuth URL detection
        test_cases = [
            ("https://www.googleapis.com/oauth2/v4/token", "POST", True),
            ("https://oauth2.googleapis.com/token", "POST", True),
            ("https://www.googleapis.com/some/other/path", "POST", False),
            ("https://example.com/oauth2/v4/token", "POST", False),
        ]
        
        all_passed = True
        for url, method, expected in test_cases:
            parsed_url = urlparse(url)
            request = Mock()
            request.method = method
            
            result = addon._should_intercept_request(parsed_url, request)
            if result == expected:
                print(f"  ✅ {url} {method} -> {result}")
            else:
                print(f"  ❌ {url} {method} -> {result} (expected {expected})")
                all_passed = False
        
        if not all_passed:
            return False
        
        # Test 3: Test fake response generation
        print("🔍 Test 3: Testing fake response generation...")
        fake_response = addon._create_fake_oauth_response()
        
        required_fields = ['access_token', 'token_type', 'expires_in', 'scope']
        for field in required_fields:
            if field in fake_response:
                print(f"  ✅ {field}: {fake_response[field]}")
            else:
                print(f"  ❌ Missing field: {field}")
                return False
        
        print("🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
        
    finally:
        # Clean up
        if process:
            print("🧹 Cleaning up...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()


def main():
    """Main test runner."""
    print("=" * 60)
    print("OAuth Interceptor Simple Integration Test")
    print("=" * 60)
    
    success = test_oauth_interceptor()
    
    print("=" * 60)
    if success:
        print("🎉 All tests passed!")
        sys.exit(0)
    else:
        print("💥 Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
