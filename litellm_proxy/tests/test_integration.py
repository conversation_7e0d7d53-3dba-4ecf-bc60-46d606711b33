#!/usr/bin/env python3
"""Integration tests for OAuth Interceptor."""

import os
import sys
import time
import json
import signal
import subprocess
import threading
from pathlib import Path

import requests

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from oauth_interceptor.config import load_config


class OAuthInterceptorTester:
    """Integration tester for OAuth Interceptor."""
    
    def __init__(self, port=18889):
        self.port = port
        self.process = None
        self.base_dir = Path(__file__).parent.parent
        self.addon_path = self.base_dir / "src" / "oauth_interceptor" / "addons" / "oauth_interceptor.py"
        
    def start_proxy(self, timeout=10):
        """Start the OAuth interceptor proxy."""
        cmd = [
            'mitmdump',
            '--listen-host', '127.0.0.1',
            '--listen-port', str(self.port),
            '--scripts', str(self.addon_path),
            '--set', 'oauth_token_lifetime=3600',
            '--set', 'oauth_scope=https://www.googleapis.com/auth/cloud-platform',
            '--set', 'oauth_fake_token=test_fake_token_12345',
            '--set', 'oauth_token_type=Bearer',
        ]
        
        print(f"Starting proxy on port {self.port}...")
        self.process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for proxy to start
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(
                    'http://httpbin.org/ip',
                    proxies={'http': f'http://127.0.0.1:{self.port}'},
                    timeout=2
                )
                if response.status_code == 200:
                    print("✓ Proxy started successfully")
                    return True
            except:
                time.sleep(0.5)
        
        print("✗ Failed to start proxy")
        return False
    
    def stop_proxy(self):
        """Stop the OAuth interceptor proxy."""
        if self.process:
            self.process.terminate()
            try:
                self.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.process.kill()
                self.process.wait()
            self.process = None
            print("✓ Proxy stopped")
    
    def test_oauth_interception(self):
        """Test OAuth2 token request interception."""
        print("Testing OAuth2 interception...")

        # Use HTTP instead of HTTPS to avoid certificate issues in testing
        test_url = "http://www.googleapis.com/oauth2/v4/token"
        test_data = {
            'grant_type': 'client_credentials',
            'scope': 'https://www.googleapis.com/auth/cloud-platform'
        }

        proxy_config = {
            'http': f'http://127.0.0.1:{self.port}',
            'https': f'http://127.0.0.1:{self.port}'
        }

        try:
            response = requests.post(
                test_url,
                data=test_data,
                proxies=proxy_config,
                timeout=5,
                verify=False
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Check if response contains expected fake token
                if ('access_token' in data and 
                    'test_fake_token_12345' in data['access_token']):
                    print("✓ OAuth interception working correctly")
                    print(f"  Access token: {data['access_token']}")
                    print(f"  Token type: {data.get('token_type', 'N/A')}")
                    print(f"  Expires in: {data.get('expires_in', 'N/A')} seconds")
                    return True
                else:
                    print("✗ Response doesn't contain expected fake token")
                    print(f"  Response: {json.dumps(data, indent=2)}")
                    return False
            else:
                print(f"✗ Request failed with status {response.status_code}")
                print(f"  Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"✗ Test failed: {e}")
            return False
    
    def test_passthrough(self):
        """Test that non-OAuth requests pass through normally."""
        print("Testing request passthrough...")
        
        test_url = "http://httpbin.org/json"
        proxy_config = {
            'http': f'http://127.0.0.1:{self.port}',
            'https': f'http://127.0.0.1:{self.port}'
        }
        
        try:
            response = requests.get(
                test_url,
                proxies=proxy_config,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                # httpbin.org/json returns a JSON object with slideshow data
                if 'slideshow' in data:
                    print("✓ Non-OAuth requests pass through correctly")
                    return True
                else:
                    print("✗ Unexpected response from httpbin.org/json")
                    return False
            else:
                print(f"✗ Passthrough test failed with status {response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ Passthrough test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all integration tests."""
        print("=" * 60)
        print("OAuth Interceptor Integration Tests")
        print("=" * 60)
        
        success = True
        
        try:
            # Start proxy
            if not self.start_proxy():
                return False
            
            # Wait a moment for proxy to fully initialize
            time.sleep(2)
            
            # Run tests
            if not self.test_oauth_interception():
                success = False
            
            print()
            if not self.test_passthrough():
                success = False
            
        finally:
            # Always stop proxy
            self.stop_proxy()
        
        print()
        print("=" * 60)
        if success:
            print("✓ All tests passed!")
        else:
            print("✗ Some tests failed!")
        print("=" * 60)
        
        return success


def main():
    """Main test runner."""
    tester = OAuthInterceptorTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
