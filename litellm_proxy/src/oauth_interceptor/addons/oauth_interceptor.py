"""OAuth2 Token Interceptor Addon for mitmproxy.

This addon intercepts OAuth2 token requests to googleapis.com and returns
fake successful responses to enable gemini-cli to work with LiteLLM proxy.
"""

import json
import logging
import time
from typing import Optional
from urllib.parse import urlparse, parse_qs

from mitmproxy import http, ctx
from mitmproxy.addonmanager import Loader


class OAuthInterceptor:
    """Mitmproxy addon to intercept OAuth2 token requests."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Configuration options (will be set by mitmproxy)
        self.oauth_token_lifetime: int = 3600
        self.oauth_scope: str = "https://www.googleapis.com/auth/cloud-platform"
        self.oauth_fake_token: str = "fake_access_token_for_litellm_proxy"
        self.oauth_token_type: str = "Bearer"
        
        # Statistics
        self.intercepted_requests = 0
        self.start_time = time.time()
    
    def load(self, loader: Loader) -> None:
        """Load configuration options."""
        loader.add_option(
            name="oauth_token_lifetime",
            typespec=int,
            default=3600,
            help="OAuth token lifetime in seconds"
        )
        loader.add_option(
            name="oauth_scope",
            typespec=str,
            default="https://www.googleapis.com/auth/cloud-platform",
            help="OAuth token scope"
        )
        loader.add_option(
            name="oauth_fake_token",
            typespec=str,
            default="fake_access_token_for_litellm_proxy",
            help="Fake access token to return"
        )
        loader.add_option(
            name="oauth_token_type",
            typespec=str,
            default="Bearer",
            help="OAuth token type"
        )
    
    def configure(self, updates) -> None:
        """Update configuration when options change."""
        if "oauth_token_lifetime" in updates:
            self.oauth_token_lifetime = ctx.options.oauth_token_lifetime
        if "oauth_scope" in updates:
            self.oauth_scope = ctx.options.oauth_scope
        if "oauth_fake_token" in updates:
            self.oauth_fake_token = ctx.options.oauth_fake_token
        if "oauth_token_type" in updates:
            self.oauth_token_type = ctx.options.oauth_token_type
    
    def running(self) -> None:
        """Called when mitmproxy starts."""
        self.logger.info("OAuth Interceptor addon started")
        self.logger.info(f"Token lifetime: {self.oauth_token_lifetime}s")
        self.logger.info(f"Token scope: {self.oauth_scope}")
        self.logger.info(f"Fake token: {self.oauth_fake_token[:20]}...")
    
    def request(self, flow: http.HTTPFlow) -> None:
        """Intercept and handle requests."""
        # Parse the request URL
        parsed_url = urlparse(flow.request.pretty_url)
        
        # Check if this is an OAuth2 token request to googleapis.com
        if self._should_intercept_request(parsed_url, flow.request):
            self._handle_oauth_request(flow)
    
    def _should_intercept_request(self, parsed_url, request) -> bool:
        """Determine if this request should be intercepted."""
        # Check if it's a googleapis.com domain
        if not parsed_url.hostname or not parsed_url.hostname.endswith('googleapis.com'):
            return False
        
        # Check if it's an OAuth2 token endpoint
        oauth_paths = [
            '/oauth2/v4/token',
            '/oauth2/v3/token',
            '/oauth2/v2/token',
            '/token',
        ]
        
        if not any(parsed_url.path.endswith(path) for path in oauth_paths):
            return False
        
        # Check if it's a POST request (OAuth2 token requests are typically POST)
        if request.method != 'POST':
            return False
        
        self.logger.info(f"OAuth2 token request detected: {parsed_url.geturl()}")
        return True
    
    def _handle_oauth_request(self, flow: http.HTTPFlow) -> None:
        """Handle OAuth2 token request by returning a fake response."""
        self.intercepted_requests += 1
        
        # Log the original request details
        self.logger.info(f"Intercepting OAuth2 request #{self.intercepted_requests}")
        self.logger.info(f"URL: {flow.request.pretty_url}")
        self.logger.info(f"Method: {flow.request.method}")
        self.logger.info(f"Headers: {dict(flow.request.headers)}")
        
        # Log request body if it exists
        if flow.request.content:
            try:
                if flow.request.headers.get('content-type', '').startswith('application/x-www-form-urlencoded'):
                    body_params = parse_qs(flow.request.content.decode('utf-8'))
                    self.logger.info(f"Form data: {body_params}")
                else:
                    self.logger.info(f"Request body: {flow.request.content.decode('utf-8')[:500]}")
            except Exception as e:
                self.logger.warning(f"Failed to decode request body: {e}")
        
        # Create fake OAuth2 response
        fake_response = self._create_fake_oauth_response()
        
        # Create the HTTP response
        flow.response = http.Response.make(
            200,  # HTTP 200 OK
            json.dumps(fake_response).encode('utf-8'),
            {
                "Content-Type": "application/json",
                "Cache-Control": "no-cache, no-store, max-age=0, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "Mon, 01 Jan 1990 00:00:00 GMT",
                "Date": time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime()),
                "Server": "OAuth Interceptor/1.0",
                "X-Intercepted-By": "oauth-interceptor",
            }
        )
        
        self.logger.info(f"Returned fake OAuth2 response: {fake_response}")
        self.logger.info("=" * 60)
    
    def _create_fake_oauth_response(self) -> dict:
        """Create a fake OAuth2 token response."""
        current_time = int(time.time())
        
        response = {
            "access_token": self.oauth_fake_token,
            "token_type": self.oauth_token_type,
            "expires_in": self.oauth_token_lifetime,
            "scope": self.oauth_scope,
            "issued_at": current_time,
            "expires_at": current_time + self.oauth_token_lifetime,
        }
        
        # Add some additional fields that might be expected
        if "cloud-platform" in self.oauth_scope:
            response.update({
                "project_id": "fake-project-id",
                "client_id": "fake-client-id",
            })
        
        return response
    
    def response(self, flow: http.HTTPFlow) -> None:
        """Log responses for debugging."""
        # Only log responses for requests we didn't intercept
        if flow.response and not flow.response.headers.get("X-Intercepted-By"):
            parsed_url = urlparse(flow.request.pretty_url)
            if parsed_url.hostname and parsed_url.hostname.endswith('googleapis.com'):
                self.logger.debug(f"Passthrough response from {flow.request.pretty_url}: {flow.response.status_code}")
    
    def done(self) -> None:
        """Called when mitmproxy shuts down."""
        uptime = time.time() - self.start_time
        self.logger.info("OAuth Interceptor addon shutting down")
        self.logger.info(f"Total intercepted requests: {self.intercepted_requests}")
        self.logger.info(f"Uptime: {uptime:.2f} seconds")


# Create addon instance
addons = [OAuthInterceptor()]
