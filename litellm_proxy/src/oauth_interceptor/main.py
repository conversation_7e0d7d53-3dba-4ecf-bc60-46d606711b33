"""Main entry point for OAuth Interceptor."""

import sys
import logging
import signal
import subprocess
from pathlib import Path
from typing import Optional

import click
import colorama
from colorama import Fore, Style

from .config import load_config, save_default_config


# Initialize colorama for cross-platform colored output
colorama.init()


def setup_logging(config) -> None:
    """Setup logging configuration."""
    log_level = getattr(logging, config.logging.level.upper(), logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    if config.logging.console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # File handler
    if config.logging.file:
        file_handler = logging.FileHandler(config.logging.file)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)


def print_banner() -> None:
    """Print application banner."""
    banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    OAuth Interceptor                        ║
║              for Gemini-CLI + LiteLLM Integration           ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(banner)


def get_addon_path() -> str:
    """Get the path to the OAuth interceptor addon."""
    current_dir = Path(__file__).parent
    addon_path = current_dir / "addons" / "oauth_interceptor.py"
    return str(addon_path)


@click.group()
@click.version_option(version="0.1.0")
def cli() -> None:
    """OAuth Interceptor for Gemini-CLI.
    
    Intercepts OAuth2 authentication requests and provides fake responses
    to enable gemini-cli integration with LiteLLM proxy.
    """
    pass


@cli.command()
@click.option('--port', '-p', default=8888, help='Proxy port (default: 8888)')
@click.option('--host', '-h', default='127.0.0.1', help='Proxy host (default: 127.0.0.1)')
@click.option('--config', '-c', help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.option('--transparent', '-t', is_flag=True, help='Use transparent proxy mode')
def start(port: int, host: str, config: Optional[str], verbose: bool, transparent: bool) -> None:
    """Start the OAuth interceptor proxy."""
    print_banner()
    
    # Load configuration
    cfg = load_config(config)
    
    # Override with command line options
    if port != 8888:
        cfg.proxy.port = port
    if host != '127.0.0.1':
        cfg.proxy.host = host
    if verbose:
        cfg.logging.level = 'DEBUG'
    
    # Setup logging
    setup_logging(cfg)
    logger = logging.getLogger(__name__)
    
    logger.info(f"Starting OAuth Interceptor on {cfg.proxy.host}:{cfg.proxy.port}")
    logger.info(f"Configuration: {cfg}")
    
    # Get addon path
    addon_path = get_addon_path()
    if not Path(addon_path).exists():
        logger.error(f"Addon file not found: {addon_path}")
        sys.exit(1)
    
    # Build mitmproxy command
    cmd = [
        'mitmdump',
        '--listen-host', cfg.proxy.host,
        '--listen-port', str(cfg.proxy.port),
        '--scripts', addon_path,
        '--set', f'oauth_token_lifetime={cfg.oauth.token_lifetime}',
        '--set', f'oauth_scope={cfg.oauth.scope}',
        '--set', f'oauth_fake_token={cfg.oauth.fake_token}',
        '--set', f'oauth_token_type={cfg.oauth.token_type}',
    ]
    
    if transparent:
        cmd.extend(['--mode', 'transparent'])
        logger.info("Using transparent proxy mode")
    
    if verbose:
        cmd.append('--verbose')
    
    print(f"{Fore.GREEN}✓ Starting mitmproxy with OAuth interception...{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}  Proxy: {cfg.proxy.host}:{cfg.proxy.port}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}  Mode: {'Transparent' if transparent else 'HTTP Proxy'}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}  Press Ctrl+C to stop{Style.RESET_ALL}")
    print()
    
    try:
        # Start mitmproxy
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Shutting down OAuth Interceptor...{Style.RESET_ALL}")
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to start mitmproxy: {e}")
        sys.exit(1)
    except FileNotFoundError:
        logger.error("mitmproxy not found. Please install it: pip install mitmproxy")
        sys.exit(1)


@cli.command()
@click.option('--output', '-o', default='config.yaml', help='Output file path')
def init_config(output: str) -> None:
    """Generate a default configuration file."""
    try:
        save_default_config(output)
        print(f"{Fore.GREEN}✓ Default configuration saved to {output}{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}✗ Failed to save configuration: {e}{Style.RESET_ALL}")
        sys.exit(1)


@cli.command()
def status() -> None:
    """Check the status of OAuth Interceptor."""
    import psutil
    import requests

    print(f"{Fore.CYAN}Checking OAuth Interceptor status...{Style.RESET_ALL}")

    # Check if mitmproxy process is running
    mitm_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] in ['mitmdump', 'mitmproxy'] and proc.info['cmdline']:
                cmdline = ' '.join(proc.info['cmdline'])
                if 'oauth_interceptor.py' in cmdline:
                    mitm_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue

    if mitm_processes:
        print(f"{Fore.GREEN}✓ OAuth Interceptor is running{Style.RESET_ALL}")
        for proc in mitm_processes:
            print(f"  PID: {proc['pid']}")
            print(f"  Command: {' '.join(proc['cmdline'])}")
    else:
        print(f"{Fore.YELLOW}⚠ OAuth Interceptor is not running{Style.RESET_ALL}")

    # Try to connect to proxy port
    try:
        response = requests.get('http://httpbin.org/ip',
                              proxies={'http': 'http://127.0.0.1:8888', 'https': 'http://127.0.0.1:8888'},
                              timeout=5)
        print(f"{Fore.GREEN}✓ Proxy is accessible on port 8888{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.YELLOW}⚠ Proxy not accessible on port 8888: {e}{Style.RESET_ALL}")


@cli.command()
@click.option('--port', '-p', default=8888, help='Test proxy port')
def test(port: int) -> None:
    """Test OAuth interception functionality."""
    import requests
    import json

    print(f"{Fore.CYAN}Testing OAuth interception functionality...{Style.RESET_ALL}")

    # Test OAuth2 token request
    test_url = "https://www.googleapis.com/oauth2/v4/token"
    test_data = {
        'grant_type': 'client_credentials',
        'scope': 'https://www.googleapis.com/auth/cloud-platform'
    }

    proxy_config = {
        'http': f'http://127.0.0.1:{port}',
        'https': f'http://127.0.0.1:{port}'
    }

    try:
        print(f"Sending test OAuth2 request to {test_url}...")
        response = requests.post(test_url, data=test_data, proxies=proxy_config,
                               timeout=10, verify=False)

        if response.status_code == 200:
            try:
                data = response.json()
                if 'access_token' in data and 'fake_access_token' in data['access_token']:
                    print(f"{Fore.GREEN}✓ OAuth interception working correctly{Style.RESET_ALL}")
                    print(f"  Access token: {data['access_token']}")
                    print(f"  Token type: {data.get('token_type', 'N/A')}")
                    print(f"  Expires in: {data.get('expires_in', 'N/A')} seconds")
                else:
                    print(f"{Fore.YELLOW}⚠ Received response but token doesn't look fake{Style.RESET_ALL}")
                    print(f"  Response: {json.dumps(data, indent=2)}")
            except json.JSONDecodeError:
                print(f"{Fore.RED}✗ Invalid JSON response{Style.RESET_ALL}")
                print(f"  Response: {response.text}")
        else:
            print(f"{Fore.RED}✗ Request failed with status {response.status_code}{Style.RESET_ALL}")
            print(f"  Response: {response.text}")

    except requests.exceptions.ProxyError:
        print(f"{Fore.RED}✗ Cannot connect to proxy on port {port}{Style.RESET_ALL}")
        print("  Make sure OAuth Interceptor is running")
    except Exception as e:
        print(f"{Fore.RED}✗ Test failed: {e}{Style.RESET_ALL}")


if __name__ == '__main__':
    cli()
