"""Main entry point for Proxy Toolkit."""

import sys
import subprocess
from pathlib import Path
from typing import Optional

import click
import colorama
from colorama import Fore, Style

from .core.config import load_config, save_default_config
from .core.registry import PluginRegistry
from .core.logger import setup_logging


# Initialize colorama for cross-platform colored output
colorama.init()


def print_banner() -> None:
    """Print application banner."""
    banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    LiteLLM Proxy Toolkit                    ║
║         Extensible proxy for LiteLLM integration            ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(banner)


def get_master_addon_path() -> str:
    """Get the path to the master addon that loads all plugins."""
    current_dir = Path(__file__).parent
    addon_path = current_dir / "master_addon.py"
    return str(addon_path)


@click.group()
@click.version_option(version="0.2.0")
def cli() -> None:
    """LiteLLM Proxy Toolkit.
    
    An extensible proxy toolkit for LiteLLM integration with support for:
    - OAuth2 token interception
    - HTTP header modification  
    - URL routing and redirection
    - Request/response logging
    - Custom plugin development
    """
    pass


@cli.command()
@click.option('--port', '-p', default=8888, help='Proxy port (default: 8888)')
@click.option('--host', '-h', default='127.0.0.1', help='Proxy host (default: 127.0.0.1)')
@click.option('--config', '-c', help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.option('--transparent', '-t', is_flag=True, help='Use transparent proxy mode')
def start(port: int, host: str, config: Optional[str], verbose: bool, transparent: bool) -> None:
    """Start the proxy toolkit."""
    print_banner()
    
    # Load configuration
    cfg = load_config(config)
    
    # Override with command line options
    if port != 8888:
        cfg.proxy.port = port
    if host != '127.0.0.1':
        cfg.proxy.host = host
    if verbose:
        cfg.logging.level = 'DEBUG'
    
    # Setup logging
    setup_logging(cfg.logging)
    
    # Get master addon path
    addon_path = get_master_addon_path()
    if not Path(addon_path).exists():
        print(f"{Fore.RED}✗ Master addon file not found: {addon_path}{Style.RESET_ALL}")
        sys.exit(1)
    
    # Build mitmproxy command
    cmd = [
        'mitmdump',
        '--listen-host', cfg.proxy.host,
        '--listen-port', str(cfg.proxy.port),
        '--scripts', addon_path,
        '--set', f'config_file={config or "config.yaml"}',
    ]
    
    if transparent:
        cmd.extend(['--mode', 'transparent'])
        print(f"{Fore.YELLOW}  Mode: Transparent proxy{Style.RESET_ALL}")
    else:
        print(f"{Fore.YELLOW}  Mode: HTTP proxy{Style.RESET_ALL}")
    
    if verbose:
        cmd.append('--verbose')
    
    print(f"{Fore.GREEN}✓ Starting LiteLLM Proxy Toolkit...{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}  Proxy: {cfg.proxy.host}:{cfg.proxy.port}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}  Config: {config or 'config.yaml'}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}  Press Ctrl+C to stop{Style.RESET_ALL}")
    print()
    
    try:
        # Start mitmproxy
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Shutting down Proxy Toolkit...{Style.RESET_ALL}")
    except subprocess.CalledProcessError as e:
        print(f"{Fore.RED}✗ Failed to start mitmproxy: {e}{Style.RESET_ALL}")
        sys.exit(1)
    except FileNotFoundError:
        print(f"{Fore.RED}✗ mitmproxy not found. Please install it: pip install mitmproxy{Style.RESET_ALL}")
        sys.exit(1)


@cli.command()
@click.option('--output', '-o', default='config.yaml', help='Output file path')
def init_config(output: str) -> None:
    """Generate a default configuration file."""
    try:
        save_default_config(output)
        print(f"{Fore.GREEN}✓ Default configuration saved to {output}{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}✗ Failed to save configuration: {e}{Style.RESET_ALL}")
        sys.exit(1)


@cli.command()
@click.option('--config', '-c', help='Configuration file path')
def plugins(config: Optional[str]) -> None:
    """List available plugins and their status."""
    print(f"{Fore.CYAN}Available Plugins:{Style.RESET_ALL}")
    print()
    
    # Load configuration to see which plugins are enabled
    cfg = load_config(config)
    
    # Create registry and discover plugins
    registry = PluginRegistry()
    registry.discover_plugins()
    
    for plugin_name in registry.list_plugins():
        try:
            info = registry.get_plugin_info(plugin_name)
            
            # Check if plugin is configured
            plugin_config = cfg.plugins.get(plugin_name)
            enabled = plugin_config.enabled if plugin_config else False
            priority = plugin_config.priority if plugin_config else 100
            
            status_color = Fore.GREEN if enabled else Fore.YELLOW
            status_text = "ENABLED" if enabled else "DISABLED"
            
            print(f"  {status_color}● {plugin_name}{Style.RESET_ALL}")
            print(f"    Status: {status_color}{status_text}{Style.RESET_ALL}")
            print(f"    Priority: {priority}")
            print(f"    Class: {info['class_name']}")
            if info.get('doc'):
                print(f"    Description: {info['doc'].strip().split('.')[0]}")
            print()
            
        except Exception as e:
            print(f"  {Fore.RED}● {plugin_name} (ERROR: {e}){Style.RESET_ALL}")
            print()


@cli.command()
@click.option('--config', '-c', help='Configuration file path')
def status() -> None:
    """Check the status of Proxy Toolkit."""
    import psutil
    import requests
    
    print(f"{Fore.CYAN}Checking Proxy Toolkit status...{Style.RESET_ALL}")
    
    # Check if mitmproxy process is running
    mitm_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] in ['mitmdump', 'mitmproxy'] and proc.info['cmdline']:
                cmdline = ' '.join(proc.info['cmdline'])
                if 'master_addon.py' in cmdline:
                    mitm_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if mitm_processes:
        print(f"{Fore.GREEN}✓ Proxy Toolkit is running{Style.RESET_ALL}")
        for proc in mitm_processes:
            print(f"  PID: {proc['pid']}")
    else:
        print(f"{Fore.YELLOW}⚠ Proxy Toolkit is not running{Style.RESET_ALL}")
    
    # Try to connect to proxy port
    try:
        response = requests.get('http://httpbin.org/ip', 
                              proxies={'http': 'http://127.0.0.1:8888', 'https': 'http://127.0.0.1:8888'},
                              timeout=5)
        print(f"{Fore.GREEN}✓ Proxy is accessible on port 8888{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.YELLOW}⚠ Proxy not accessible on port 8888: {e}{Style.RESET_ALL}")


@cli.command()
@click.option('--port', '-p', default=8888, help='Test proxy port')
def test(port: int) -> None:
    """Test proxy functionality."""
    print(f"{Fore.CYAN}Testing Proxy Toolkit functionality...{Style.RESET_ALL}")
    print("This command will be implemented in the next version.")


if __name__ == '__main__':
    cli()
