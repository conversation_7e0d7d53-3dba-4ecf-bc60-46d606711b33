"""Logging system for Proxy Toolkit."""

import sys
import logging
from pathlib import Path
from typing import Optional

import structlog


def setup_logging(config) -> None:
    """Setup structured logging configuration.
    
    Args:
        config: LoggingConfig object with logging settings
    """
    log_level = getattr(logging, config.level.upper(), logging.INFO)
    
    # Configure structlog
    if config.format == "structured":
        # Structured logging with JSON output
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    else:
        # Simple logging format
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.dev.ConsoleRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    
    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    if config.console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        root_logger.addHandler(console_handler)
    
    # File handler
    if config.file:
        file_handler = logging.FileHandler(config.file)
        file_handler.setLevel(log_level)
        root_logger.addHandler(file_handler)


def get_logger(name: str, plugin_name: Optional[str] = None):
    """Get a logger instance for a component.
    
    Args:
        name: Logger name (usually __name__)
        plugin_name: Optional plugin name for context
        
    Returns:
        Structured logger instance
    """
    logger = structlog.get_logger(name)
    
    if plugin_name:
        logger = logger.bind(plugin=plugin_name)
    
    return logger


class PluginLogger:
    """Logger wrapper for plugins with automatic context."""
    
    def __init__(self, plugin_name: str):
        self.plugin_name = plugin_name
        self.logger = get_logger(__name__, plugin_name)
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message."""
        self.logger.error(message, **kwargs)
    
    def exception(self, message: str, **kwargs):
        """Log exception with traceback."""
        self.logger.exception(message, **kwargs)
    
    def request(self, method: str, url: str, **kwargs):
        """Log request information."""
        self.logger.info("Request", method=method, url=url, **kwargs)
    
    def response(self, status_code: int, url: str, **kwargs):
        """Log response information."""
        self.logger.info("Response", status_code=status_code, url=url, **kwargs)
    
    def intercept(self, action: str, url: str, **kwargs):
        """Log interception action."""
        self.logger.info("Intercept", action=action, url=url, **kwargs)
