"""Base addon class for Proxy Toolkit plugins."""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from urllib.parse import urlparse

from mitmproxy import http
from mitmproxy.addonmanager import Loader

from .logger import PluginLogger


class BaseAddon(ABC):
    """Base class for all proxy toolkit addons."""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = PluginLogger(name)
        self.enabled = True
        self.priority = 100
        self.config = {}
        
        # Statistics
        self.stats = {
            'requests_processed': 0,
            'requests_modified': 0,
            'errors': 0,
        }
    
    @abstractmethod
    def get_config_schema(self) -> Dict[str, Any]:
        """Return the configuration schema for this addon.
        
        Returns:
            Dictionary describing the expected configuration structure
        """
        pass
    
    def configure(self, config: Dict[str, Any]) -> None:
        """Configure the addon with provided settings.
        
        Args:
            config: Configuration dictionary for this addon
        """
        self.config = config
        self.logger.info("Addon configured", config=config)
    
    def load(self, loader: Loader) -> None:
        """Load addon options into mitmproxy.
        
        Args:
            loader: Mitmproxy loader instance
        """
        # Register configuration options with mitmproxy
        for key, value in self.config.items():
            option_name = f"{self.name}_{key}"
            loader.add_option(
                name=option_name,
                typespec=type(value),
                default=value,
                help=f"{self.name} addon option: {key}"
            )
    
    def running(self) -> None:
        """Called when mitmproxy starts."""
        self.logger.info("Addon started", enabled=self.enabled, priority=self.priority)
    
    def done(self) -> None:
        """Called when mitmproxy shuts down."""
        self.logger.info("Addon stopped", stats=self.stats)
    
    def should_process_request(self, flow: http.HTTPFlow) -> bool:
        """Determine if this addon should process the request.
        
        Args:
            flow: HTTP flow object
            
        Returns:
            True if addon should process this request
        """
        if not self.enabled:
            return False
        
        return self._should_process_request_impl(flow)
    
    @abstractmethod
    def _should_process_request_impl(self, flow: http.HTTPFlow) -> bool:
        """Implementation-specific request filtering logic.
        
        Args:
            flow: HTTP flow object
            
        Returns:
            True if addon should process this request
        """
        pass
    
    def request(self, flow: http.HTTPFlow) -> None:
        """Handle incoming request.
        
        Args:
            flow: HTTP flow object
        """
        if not self.should_process_request(flow):
            return
        
        self.stats['requests_processed'] += 1
        
        try:
            if self._process_request(flow):
                self.stats['requests_modified'] += 1
        except Exception as e:
            self.stats['errors'] += 1
            self.logger.exception("Error processing request", 
                                url=flow.request.pretty_url, error=str(e))
    
    def response(self, flow: http.HTTPFlow) -> None:
        """Handle response.
        
        Args:
            flow: HTTP flow object
        """
        if not self.should_process_request(flow):
            return
        
        try:
            self._process_response(flow)
        except Exception as e:
            self.stats['errors'] += 1
            self.logger.exception("Error processing response",
                                url=flow.request.pretty_url, error=str(e))
    
    def _process_request(self, flow: http.HTTPFlow) -> bool:
        """Process the request. Override in subclasses.
        
        Args:
            flow: HTTP flow object
            
        Returns:
            True if request was modified
        """
        return False
    
    def _process_response(self, flow: http.HTTPFlow) -> bool:
        """Process the response. Override in subclasses.
        
        Args:
            flow: HTTP flow object
            
        Returns:
            True if response was modified
        """
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get addon statistics.
        
        Returns:
            Dictionary with addon statistics
        """
        return {
            'name': self.name,
            'enabled': self.enabled,
            'priority': self.priority,
            'stats': self.stats.copy()
        }
    
    # Utility methods for common operations
    def _match_host(self, flow: http.HTTPFlow, pattern: str) -> bool:
        """Check if request host matches pattern.
        
        Args:
            flow: HTTP flow object
            pattern: Host pattern (supports wildcards)
            
        Returns:
            True if host matches pattern
        """
        parsed_url = urlparse(flow.request.pretty_url)
        host = parsed_url.hostname or ""
        
        if pattern.startswith('*.'):
            # Wildcard subdomain matching
            domain = pattern[2:]
            return host.endswith(domain)
        else:
            return host == pattern
    
    def _match_path(self, flow: http.HTTPFlow, pattern: str) -> bool:
        """Check if request path matches pattern.
        
        Args:
            flow: HTTP flow object
            pattern: Path pattern (supports wildcards)
            
        Returns:
            True if path matches pattern
        """
        parsed_url = urlparse(flow.request.pretty_url)
        path = parsed_url.path
        
        if pattern.endswith('*'):
            # Prefix matching
            prefix = pattern[:-1]
            return path.startswith(prefix)
        else:
            return path == pattern
