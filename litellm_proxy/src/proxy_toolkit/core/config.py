"""Configuration management for Proxy Toolkit."""

import os
import yaml
from dataclasses import dataclass, field
from pathlib import Path
from typing import Optional, Dict, Any, List


@dataclass
class ProxyConfig:
    """Proxy server configuration."""
    host: str = "127.0.0.1"
    port: int = 8888
    mode: str = "regular"  # regular, transparent, upstream


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    file: Optional[str] = None
    format: str = "structured"  # simple, structured
    console: bool = True


@dataclass
class PluginConfig:
    """Individual plugin configuration."""
    enabled: bool = True
    priority: int = 100
    config: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Config:
    """Main configuration class."""
    proxy: ProxyConfig = field(default_factory=ProxyConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    plugins: Dict[str, PluginConfig] = field(default_factory=dict)


def load_config(config_path: Optional[str] = None) -> Config:
    """Load configuration from file or use defaults.
    
    Args:
        config_path: Path to configuration file. If None, looks for
                    config.yaml in current directory.
    
    Returns:
        Config object with loaded or default values.
    """
    config = Config()
    
    if config_path is None:
        config_path = "config.yaml"
    
    config_file = Path(config_path)
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            if data:
                # Update proxy config
                if 'proxy' in data:
                    proxy_data = data['proxy']
                    config.proxy.host = proxy_data.get('host', config.proxy.host)
                    config.proxy.port = proxy_data.get('port', config.proxy.port)
                    config.proxy.mode = proxy_data.get('mode', config.proxy.mode)
                
                # Update logging config
                if 'logging' in data:
                    logging_data = data['logging']
                    config.logging.level = logging_data.get('level', config.logging.level)
                    config.logging.file = logging_data.get('file', config.logging.file)
                    config.logging.format = logging_data.get('format', config.logging.format)
                    config.logging.console = logging_data.get('console', config.logging.console)
                
                # Update plugins config
                if 'plugins' in data:
                    plugins_data = data['plugins']
                    for plugin_name, plugin_data in plugins_data.items():
                        plugin_config = PluginConfig(
                            enabled=plugin_data.get('enabled', True),
                            priority=plugin_data.get('priority', 100),
                            config=plugin_data.get('config', {})
                        )
                        config.plugins[plugin_name] = plugin_config
                    
        except Exception as e:
            print(f"Warning: Failed to load config from {config_path}: {e}")
            print("Using default configuration.")
    
    # Override with environment variables
    config.proxy.port = int(os.getenv('PROXY_TOOLKIT_PORT', config.proxy.port))
    config.proxy.host = os.getenv('PROXY_TOOLKIT_HOST', config.proxy.host)
    config.logging.level = os.getenv('PROXY_TOOLKIT_LOG_LEVEL', config.logging.level)
    
    return config


def save_default_config(config_path: str = "config.yaml") -> None:
    """Save default configuration to file.
    
    Args:
        config_path: Path where to save the configuration file.
    """
    default_config = {
        'proxy': {
            'host': '127.0.0.1',
            'port': 8888,
            'mode': 'regular',
        },
        'logging': {
            'level': 'INFO',
            'file': 'proxy_toolkit.log',
            'format': 'structured',
            'console': True,
        },
        'plugins': {
            'oauth_interceptor': {
                'enabled': True,
                'priority': 100,
                'config': {
                    'token_lifetime': 3600,
                    'scope': 'https://www.googleapis.com/auth/cloud-platform',
                    'fake_token': 'fake_access_token_for_litellm_proxy',
                    'token_type': 'Bearer',
                }
            },
            'header_modifier': {
                'enabled': False,
                'priority': 200,
                'config': {
                    'rules': []
                }
            },
            'route_redirector': {
                'enabled': False,
                'priority': 300,
                'config': {
                    'rules': []
                }
            },
            'request_logger': {
                'enabled': True,
                'priority': 999,
                'config': {
                    'log_requests': True,
                    'log_responses': False,
                    'exclude_paths': ['/health', '/metrics']
                }
            }
        }
    }
    
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(default_config, f, default_flow_style=False, indent=2)
