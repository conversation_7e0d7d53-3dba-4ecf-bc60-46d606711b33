"""Plugin registry system for Proxy Toolkit."""

import importlib
import pkgutil
from typing import Dict, List, Type, Any
from pathlib import Path

from .base_addon import BaseAddon
from .logger import get_logger


class PluginRegistry:
    """Registry for managing proxy toolkit plugins."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._plugins: Dict[str, Type[BaseAddon]] = {}
        self._instances: Dict[str, BaseAddon] = {}
        self._load_order: List[str] = []
    
    def discover_plugins(self, package_path: str = "proxy_toolkit.addons") -> None:
        """Discover and register plugins from the addons package.
        
        Args:
            package_path: Python package path to search for plugins
        """
        try:
            # Import the addons package
            addons_package = importlib.import_module(package_path)
            package_dir = Path(addons_package.__file__).parent
            
            # Iterate through all modules in the package
            for finder, name, ispkg in pkgutil.iter_modules([str(package_dir)]):
                if not ispkg and not name.startswith('_'):
                    module_name = f"{package_path}.{name}"
                    try:
                        module = importlib.import_module(module_name)
                        self._register_module_plugins(module, name)
                    except Exception as e:
                        self.logger.error("Failed to load plugin module", 
                                        module=module_name, error=str(e))
                        
        except Exception as e:
            self.logger.error("Failed to discover plugins", 
                            package=package_path, error=str(e))
    
    def _register_module_plugins(self, module, module_name: str) -> None:
        """Register plugins from a module.
        
        Args:
            module: Python module object
            module_name: Name of the module
        """
        # Look for classes that inherit from BaseAddon
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            
            if (isinstance(attr, type) and 
                issubclass(attr, BaseAddon) and 
                attr != BaseAddon):
                
                plugin_name = getattr(attr, 'PLUGIN_NAME', module_name)
                self.register_plugin(plugin_name, attr)
    
    def register_plugin(self, name: str, plugin_class: Type[BaseAddon]) -> None:
        """Register a plugin class.
        
        Args:
            name: Plugin name
            plugin_class: Plugin class that inherits from BaseAddon
        """
        if not issubclass(plugin_class, BaseAddon):
            raise ValueError(f"Plugin {name} must inherit from BaseAddon")
        
        self._plugins[name] = plugin_class
        self.logger.info("Plugin registered", name=name, class_name=plugin_class.__name__)
    
    def create_instance(self, name: str, config: Dict[str, Any]) -> BaseAddon:
        """Create an instance of a plugin.
        
        Args:
            name: Plugin name
            config: Plugin configuration
            
        Returns:
            Plugin instance
        """
        if name not in self._plugins:
            raise ValueError(f"Plugin {name} not found")
        
        plugin_class = self._plugins[name]
        instance = plugin_class(name)
        
        # Configure the instance
        instance.enabled = config.get('enabled', True)
        instance.priority = config.get('priority', 100)
        instance.configure(config.get('config', {}))
        
        self._instances[name] = instance
        self.logger.info("Plugin instance created", name=name, enabled=instance.enabled)
        
        return instance
    
    def get_instance(self, name: str) -> BaseAddon:
        """Get an existing plugin instance.
        
        Args:
            name: Plugin name
            
        Returns:
            Plugin instance
        """
        if name not in self._instances:
            raise ValueError(f"Plugin instance {name} not found")
        
        return self._instances[name]
    
    def get_enabled_instances(self) -> List[BaseAddon]:
        """Get all enabled plugin instances sorted by priority.
        
        Returns:
            List of enabled plugin instances
        """
        enabled_plugins = [
            plugin for plugin in self._instances.values() 
            if plugin.enabled
        ]
        
        # Sort by priority (lower numbers = higher priority)
        enabled_plugins.sort(key=lambda p: p.priority)
        
        return enabled_plugins
    
    def list_plugins(self) -> List[str]:
        """List all registered plugin names.
        
        Returns:
            List of plugin names
        """
        return list(self._plugins.keys())
    
    def list_instances(self) -> List[str]:
        """List all created plugin instance names.
        
        Returns:
            List of instance names
        """
        return list(self._instances.keys())
    
    def get_plugin_info(self, name: str) -> Dict[str, Any]:
        """Get information about a plugin.
        
        Args:
            name: Plugin name
            
        Returns:
            Dictionary with plugin information
        """
        if name not in self._plugins:
            raise ValueError(f"Plugin {name} not found")
        
        plugin_class = self._plugins[name]
        info = {
            'name': name,
            'class_name': plugin_class.__name__,
            'module': plugin_class.__module__,
            'doc': plugin_class.__doc__,
            'registered': True,
        }
        
        if name in self._instances:
            instance = self._instances[name]
            info.update({
                'instance_created': True,
                'enabled': instance.enabled,
                'priority': instance.priority,
                'config_schema': instance.get_config_schema(),
                'stats': instance.get_stats(),
            })
        else:
            info['instance_created'] = False
        
        return info
    
    def reload_plugin(self, name: str, config: Dict[str, Any]) -> None:
        """Reload a plugin with new configuration.
        
        Args:
            name: Plugin name
            config: New plugin configuration
        """
        if name in self._instances:
            # Remove old instance
            del self._instances[name]
        
        # Create new instance
        self.create_instance(name, config)
        self.logger.info("Plugin reloaded", name=name)
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all plugin instances.
        
        Returns:
            Dictionary with all plugin statistics
        """
        return {
            name: instance.get_stats() 
            for name, instance in self._instances.items()
        }
