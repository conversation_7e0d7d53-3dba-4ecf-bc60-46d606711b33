"""LiteLLM Proxy Toolkit

An extensible proxy toolkit for LiteLLM integration with support for:
- OAuth2 token interception
- HTTP header modification
- URL routing and redirection
- Request/response logging
- Custom plugin development
"""

__version__ = "0.2.0"
__author__ = "User"
__email__ = "<EMAIL>"

from .core.config import Config, load_config
from .core.registry import PluginRegistry
from .core.logger import setup_logging

__all__ = [
    "Config",
    "load_config", 
    "PluginRegistry",
    "setup_logging",
]
