"""Master addon that loads and manages all Proxy Toolkit plugins."""

import sys
from pathlib import Path
from typing import List

from mitmproxy import ctx
from mitmproxy.addonmanager import Loader

# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent
sys.path.insert(0, str(src_dir))

from proxy_toolkit.core.config import load_config
from proxy_toolkit.core.registry import PluginRegistry
from proxy_toolkit.core.logger import setup_logging, get_logger
from proxy_toolkit.core.base_addon import BaseAddon


class MasterAddon:
    """Master addon that coordinates all Proxy Toolkit plugins."""
    
    def __init__(self):
        self.logger = None
        self.registry = PluginRegistry()
        self.active_addons: List[BaseAddon] = []
        self.config = None
    
    def load(self, loader: Loader) -> None:
        """Load configuration and initialize plugins."""
        # Add configuration option
        loader.add_option(
            name="config_file",
            typespec=str,
            default="config.yaml",
            help="Path to configuration file"
        )
    
    def configure(self, updates) -> None:
        """Configure the master addon and all plugins."""
        if "config_file" in updates:
            config_file = ctx.options.config_file
            
            # Load configuration
            self.config = load_config(config_file)
            
            # Setup logging
            setup_logging(self.config.logging)
            self.logger = get_logger(__name__)
            
            self.logger.info("Master addon configuring", config_file=config_file)
            
            # Discover and register plugins
            self.registry.discover_plugins()
            
            # Create instances for enabled plugins
            self._create_plugin_instances()
            
            # Configure all plugin instances
            for addon in self.active_addons:
                if hasattr(addon, 'load'):
                    addon.load(loader)
    
    def _create_plugin_instances(self) -> None:
        """Create instances for all enabled plugins."""
        self.active_addons = []
        
        for plugin_name, plugin_config in self.config.plugins.items():
            if plugin_config.enabled:
                try:
                    addon = self.registry.create_instance(plugin_name, {
                        'enabled': plugin_config.enabled,
                        'priority': plugin_config.priority,
                        'config': plugin_config.config
                    })
                    self.active_addons.append(addon)
                    self.logger.info("Plugin loaded", name=plugin_name, priority=addon.priority)
                except Exception as e:
                    self.logger.error("Failed to load plugin", name=plugin_name, error=str(e))
        
        # Sort by priority
        self.active_addons.sort(key=lambda a: a.priority)
        
        self.logger.info("All plugins loaded", 
                        total_plugins=len(self.active_addons),
                        enabled_plugins=[a.name for a in self.active_addons])
    
    def running(self) -> None:
        """Called when mitmproxy starts."""
        if self.logger:
            self.logger.info("Proxy Toolkit started")
        
        # Notify all addons
        for addon in self.active_addons:
            try:
                addon.running()
            except Exception as e:
                if self.logger:
                    self.logger.error("Error in addon running", addon=addon.name, error=str(e))
    
    def done(self) -> None:
        """Called when mitmproxy shuts down."""
        if self.logger:
            self.logger.info("Proxy Toolkit shutting down")
        
        # Notify all addons
        for addon in self.active_addons:
            try:
                addon.done()
            except Exception as e:
                if self.logger:
                    self.logger.error("Error in addon done", addon=addon.name, error=str(e))
    
    def request(self, flow) -> None:
        """Handle incoming requests through all enabled addons."""
        for addon in self.active_addons:
            try:
                addon.request(flow)
                # If response was created, stop processing
                if flow.response:
                    break
            except Exception as e:
                if self.logger:
                    self.logger.error("Error in addon request", addon=addon.name, error=str(e))
    
    def response(self, flow) -> None:
        """Handle responses through all enabled addons."""
        for addon in self.active_addons:
            try:
                addon.response(flow)
            except Exception as e:
                if self.logger:
                    self.logger.error("Error in addon response", addon=addon.name, error=str(e))


# Create the addon instance that mitmproxy will use
addons = [MasterAddon()]
