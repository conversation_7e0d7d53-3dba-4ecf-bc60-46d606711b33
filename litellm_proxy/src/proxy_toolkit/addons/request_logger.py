"""Request Logger Addon for Proxy Toolkit."""

import time
from typing import Dict, Any, List
from urllib.parse import urlparse

from mitmproxy import http

from ..core.base_addon import BaseAddon


class RequestLoggerAddon(BaseAddon):
    """Addon to log HTTP requests and responses."""
    
    PLUGIN_NAME = "request_logger"
    
    def __init__(self, name: str):
        super().__init__(name)
        
        # Default configuration
        self.log_requests = True
        self.log_responses = False
        self.exclude_paths = ['/health', '/metrics']
        self.log_headers = False
        self.log_body = False
        self.max_body_size = 1024
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Return the configuration schema for request logger."""
        return {
            "log_requests": {
                "type": "boolean",
                "default": True,
                "description": "Whether to log HTTP requests"
            },
            "log_responses": {
                "type": "boolean",
                "default": False,
                "description": "Whether to log HTTP responses"
            },
            "exclude_paths": {
                "type": "array",
                "default": ["/health", "/metrics"],
                "description": "List of paths to exclude from logging"
            },
            "log_headers": {
                "type": "boolean",
                "default": False,
                "description": "Whether to log HTTP headers"
            },
            "log_body": {
                "type": "boolean",
                "default": False,
                "description": "Whether to log request/response bodies"
            },
            "max_body_size": {
                "type": "integer",
                "default": 1024,
                "description": "Maximum body size to log (bytes)"
            }
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        """Configure the request logger."""
        super().configure(config)
        
        self.log_requests = config.get('log_requests', True)
        self.log_responses = config.get('log_responses', False)
        self.exclude_paths = config.get('exclude_paths', ['/health', '/metrics'])
        self.log_headers = config.get('log_headers', False)
        self.log_body = config.get('log_body', False)
        self.max_body_size = config.get('max_body_size', 1024)
        
        self.logger.info("Request logger configured",
                        log_requests=self.log_requests,
                        log_responses=self.log_responses,
                        exclude_paths=self.exclude_paths)
    
    def _should_process_request_impl(self, flow: http.HTTPFlow) -> bool:
        """Check if this request should be logged."""
        if not (self.log_requests or self.log_responses):
            return False
        
        # Check if path is excluded
        parsed_url = urlparse(flow.request.pretty_url)
        path = parsed_url.path
        
        for exclude_path in self.exclude_paths:
            if path.startswith(exclude_path):
                return False
        
        return True
    
    def _process_request(self, flow: http.HTTPFlow) -> bool:
        """Log the HTTP request."""
        if not self.log_requests:
            return False
        
        # Store request start time for response timing
        flow.metadata['request_start_time'] = time.time()
        
        # Basic request information
        log_data = {
            "method": flow.request.method,
            "url": flow.request.pretty_url,
            "host": flow.request.pretty_host,
            "path": flow.request.path,
            "scheme": flow.request.scheme,
        }
        
        # Add headers if enabled
        if self.log_headers:
            log_data["headers"] = dict(flow.request.headers)
        
        # Add body if enabled and present
        if self.log_body and flow.request.content:
            try:
                body_size = len(flow.request.content)
                if body_size <= self.max_body_size:
                    # Try to decode as text
                    try:
                        body_text = flow.request.content.decode('utf-8')
                        log_data["body"] = body_text
                    except UnicodeDecodeError:
                        log_data["body"] = f"<binary data, {body_size} bytes>"
                else:
                    log_data["body"] = f"<body too large, {body_size} bytes>"
                    
                log_data["body_size"] = body_size
            except Exception as e:
                log_data["body_error"] = str(e)
        
        self.logger.request(flow.request.method, flow.request.pretty_url, **log_data)
        return False  # We don't modify the request
    
    def _process_response(self, flow: http.HTTPFlow) -> bool:
        """Log the HTTP response."""
        if not self.log_responses:
            return False
        
        # Calculate response time if we have start time
        response_time = None
        if 'request_start_time' in flow.metadata:
            response_time = time.time() - flow.metadata['request_start_time']
        
        # Basic response information
        log_data = {
            "status_code": flow.response.status_code,
            "reason": flow.response.reason,
            "url": flow.request.pretty_url,
        }
        
        if response_time is not None:
            log_data["response_time_ms"] = round(response_time * 1000, 2)
        
        # Add headers if enabled
        if self.log_headers:
            log_data["headers"] = dict(flow.response.headers)
        
        # Add body if enabled and present
        if self.log_body and flow.response.content:
            try:
                body_size = len(flow.response.content)
                if body_size <= self.max_body_size:
                    # Try to decode as text
                    try:
                        body_text = flow.response.content.decode('utf-8')
                        log_data["body"] = body_text
                    except UnicodeDecodeError:
                        log_data["body"] = f"<binary data, {body_size} bytes>"
                else:
                    log_data["body"] = f"<body too large, {body_size} bytes>"
                    
                log_data["body_size"] = body_size
            except Exception as e:
                log_data["body_error"] = str(e)
        
        self.logger.response(flow.response.status_code, flow.request.pretty_url, **log_data)
        return False  # We don't modify the response
