"""Header Modifier Addon for Proxy Toolkit."""

import re
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse

from mitmproxy import http

from ..core.base_addon import BaseAddon


class HeaderRule:
    """Represents a header modification rule."""
    
    def __init__(self, rule_config: Dict[str, Any]):
        self.match = rule_config.get('match', {})
        self.actions = rule_config.get('actions', {})
        
        # Parse match conditions
        self.host_pattern = self.match.get('host')
        self.path_pattern = self.match.get('path')
        self.method_pattern = self.match.get('method')
        
        # Parse actions
        self.add_headers = self.actions.get('add_headers', {})
        self.remove_headers = self.actions.get('remove_headers', [])
        self.replace_headers = self.actions.get('replace_headers', {})
        self.modify_headers = self.actions.get('modify_headers', {})
    
    def matches(self, flow: http.HTTPFlow) -> bool:
        """Check if this rule matches the given flow."""
        parsed_url = urlparse(flow.request.pretty_url)
        
        # Check host pattern
        if self.host_pattern:
            if not self._match_pattern(parsed_url.hostname or "", self.host_pattern):
                return False
        
        # Check path pattern
        if self.path_pattern:
            if not self._match_pattern(parsed_url.path, self.path_pattern):
                return False
        
        # Check method pattern
        if self.method_pattern:
            if not self._match_pattern(flow.request.method, self.method_pattern):
                return False
        
        return True
    
    def _match_pattern(self, value: str, pattern: str) -> bool:
        """Match a value against a pattern (supports wildcards and regex)."""
        if pattern.startswith('regex:'):
            # Regex pattern
            regex_pattern = pattern[6:]
            return bool(re.match(regex_pattern, value))
        elif '*' in pattern:
            # Wildcard pattern
            regex_pattern = pattern.replace('*', '.*')
            return bool(re.match(regex_pattern, value))
        else:
            # Exact match
            return value == pattern


class HeaderModifierAddon(BaseAddon):
    """Addon to modify HTTP headers based on configurable rules."""
    
    PLUGIN_NAME = "header_modifier"
    
    def __init__(self, name: str):
        super().__init__(name)
        self.rules: List[HeaderRule] = []
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Return the configuration schema for header modifier."""
        return {
            "rules": {
                "type": "array",
                "default": [],
                "description": "List of header modification rules",
                "items": {
                    "type": "object",
                    "properties": {
                        "match": {
                            "type": "object",
                            "properties": {
                                "host": {"type": "string", "description": "Host pattern to match"},
                                "path": {"type": "string", "description": "Path pattern to match"},
                                "method": {"type": "string", "description": "HTTP method to match"}
                            }
                        },
                        "actions": {
                            "type": "object",
                            "properties": {
                                "add_headers": {
                                    "type": "object",
                                    "description": "Headers to add"
                                },
                                "remove_headers": {
                                    "type": "array",
                                    "description": "Headers to remove"
                                },
                                "replace_headers": {
                                    "type": "object", 
                                    "description": "Headers to replace"
                                },
                                "modify_headers": {
                                    "type": "object",
                                    "description": "Headers to modify with regex"
                                }
                            }
                        }
                    }
                }
            }
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        """Configure the header modifier."""
        super().configure(config)
        
        # Parse rules
        self.rules = []
        rules_config = config.get('rules', [])
        
        for rule_config in rules_config:
            try:
                rule = HeaderRule(rule_config)
                self.rules.append(rule)
            except Exception as e:
                self.logger.error("Failed to parse header rule", rule=rule_config, error=str(e))
        
        self.logger.info("Header modifier configured", rules_count=len(self.rules))
    
    def _should_process_request_impl(self, flow: http.HTTPFlow) -> bool:
        """Check if any rules match this request."""
        return any(rule.matches(flow) for rule in self.rules)
    
    def _process_request(self, flow: http.HTTPFlow) -> bool:
        """Apply header modifications to the request."""
        modified = False
        
        for rule in self.rules:
            if rule.matches(flow):
                if self._apply_rule_to_headers(flow.request.headers, rule, flow):
                    modified = True
                    self.logger.info("Applied header rule to request",
                                   url=flow.request.pretty_url,
                                   rule_actions=rule.actions)
        
        return modified
    
    def _process_response(self, flow: http.HTTPFlow) -> bool:
        """Apply header modifications to the response."""
        if not flow.response:
            return False
        
        modified = False
        
        for rule in self.rules:
            if rule.matches(flow):
                if self._apply_rule_to_headers(flow.response.headers, rule, flow):
                    modified = True
                    self.logger.info("Applied header rule to response",
                                   url=flow.request.pretty_url,
                                   rule_actions=rule.actions)
        
        return modified
    
    def _apply_rule_to_headers(self, headers, rule: HeaderRule, flow: http.HTTPFlow) -> bool:
        """Apply a header rule to the given headers object."""
        modified = False
        
        # Add headers
        for header_name, header_value in rule.add_headers.items():
            # Support variable substitution
            header_value = self._substitute_variables(header_value, flow)
            headers[header_name] = header_value
            modified = True
            self.logger.debug("Added header", header=header_name, value=header_value)
        
        # Remove headers
        for header_name in rule.remove_headers:
            if header_name in headers:
                del headers[header_name]
                modified = True
                self.logger.debug("Removed header", header=header_name)
        
        # Replace headers
        for header_name, header_value in rule.replace_headers.items():
            if header_name in headers:
                # Support variable substitution
                header_value = self._substitute_variables(header_value, flow)
                headers[header_name] = header_value
                modified = True
                self.logger.debug("Replaced header", header=header_name, value=header_value)
        
        # Modify headers with regex
        for header_name, modification in rule.modify_headers.items():
            if header_name in headers:
                current_value = headers[header_name]
                if isinstance(modification, dict):
                    pattern = modification.get('pattern', '')
                    replacement = modification.get('replacement', '')
                    new_value = re.sub(pattern, replacement, current_value)
                    if new_value != current_value:
                        headers[header_name] = new_value
                        modified = True
                        self.logger.debug("Modified header", header=header_name, 
                                        old_value=current_value, new_value=new_value)
        
        return modified
    
    def _substitute_variables(self, value: str, flow: http.HTTPFlow) -> str:
        """Substitute variables in header values."""
        # Support basic variable substitution
        substitutions = {
            '{host}': flow.request.pretty_host,
            '{path}': flow.request.path,
            '{method}': flow.request.method,
            '{scheme}': flow.request.scheme,
        }
        
        for var, replacement in substitutions.items():
            value = value.replace(var, replacement)
        
        return value
