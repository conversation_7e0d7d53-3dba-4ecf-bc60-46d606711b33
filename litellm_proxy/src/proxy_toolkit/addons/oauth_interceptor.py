"""OAuth2 Token Interceptor Addon for Proxy Toolkit."""

import json
import time
from typing import Dict, Any
from urllib.parse import urlparse, parse_qs

from mitmproxy import http

from ..core.base_addon import BaseAddon


class OAuthInterceptorAddon(BaseAddon):
    """Addon to intercept OAuth2 token requests and return fake responses."""
    
    PLUGIN_NAME = "oauth_interceptor"
    
    def __init__(self, name: str):
        super().__init__(name)
        
        # Default configuration
        self.token_lifetime = 3600
        self.scope = "https://www.googleapis.com/auth/cloud-platform"
        self.fake_token = "fake_access_token_for_litellm_proxy"
        self.token_type = "Bearer"
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Return the configuration schema for OAuth interceptor."""
        return {
            "token_lifetime": {
                "type": "integer",
                "default": 3600,
                "description": "OAuth token lifetime in seconds"
            },
            "scope": {
                "type": "string", 
                "default": "https://www.googleapis.com/auth/cloud-platform",
                "description": "OAuth token scope"
            },
            "fake_token": {
                "type": "string",
                "default": "fake_access_token_for_litellm_proxy",
                "description": "Fake access token to return"
            },
            "token_type": {
                "type": "string",
                "default": "Bearer",
                "description": "OAuth token type"
            }
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        """Configure the OAuth interceptor."""
        super().configure(config)
        
        self.token_lifetime = config.get('token_lifetime', 3600)
        self.scope = config.get('scope', 'https://www.googleapis.com/auth/cloud-platform')
        self.fake_token = config.get('fake_token', 'fake_access_token_for_litellm_proxy')
        self.token_type = config.get('token_type', 'Bearer')
        
        self.logger.info("OAuth interceptor configured",
                        token_lifetime=self.token_lifetime,
                        scope=self.scope,
                        fake_token=self.fake_token[:20] + "...")
    
    def _should_process_request_impl(self, flow: http.HTTPFlow) -> bool:
        """Check if this is an OAuth2 token request that should be intercepted."""
        parsed_url = urlparse(flow.request.pretty_url)
        
        # Check if it's a googleapis.com domain
        if not parsed_url.hostname or not parsed_url.hostname.endswith('googleapis.com'):
            return False
        
        # Check if it's an OAuth2 token endpoint
        oauth_paths = [
            '/oauth2/v4/token',
            '/oauth2/v3/token', 
            '/oauth2/v2/token',
            '/token',
        ]
        
        if not any(parsed_url.path.endswith(path) for path in oauth_paths):
            return False
        
        # Check if it's a POST request (OAuth2 token requests are typically POST)
        if flow.request.method != 'POST':
            return False
        
        return True
    
    def _process_request(self, flow: http.HTTPFlow) -> bool:
        """Process OAuth2 token request by returning a fake response."""
        self.logger.intercept("oauth_token_request", flow.request.pretty_url,
                            method=flow.request.method,
                            headers=dict(flow.request.headers))
        
        # Log request body if it exists
        if flow.request.content:
            try:
                if flow.request.headers.get('content-type', '').startswith('application/x-www-form-urlencoded'):
                    body_params = parse_qs(flow.request.content.decode('utf-8'))
                    self.logger.debug("OAuth request form data", form_data=body_params)
                else:
                    body_content = flow.request.content.decode('utf-8')[:500]
                    self.logger.debug("OAuth request body", body=body_content)
            except Exception as e:
                self.logger.warning("Failed to decode request body", error=str(e))
        
        # Create fake OAuth2 response
        fake_response = self._create_fake_oauth_response()
        
        # Create the HTTP response
        flow.response = http.Response.make(
            200,  # HTTP 200 OK
            json.dumps(fake_response).encode('utf-8'),
            {
                "Content-Type": "application/json",
                "Cache-Control": "no-cache, no-store, max-age=0, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "Mon, 01 Jan 1990 00:00:00 GMT",
                "Date": time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime()),
                "Server": "Proxy Toolkit OAuth Interceptor/1.0",
                "X-Intercepted-By": "proxy-toolkit-oauth",
            }
        )
        
        self.logger.intercept("oauth_response_created", flow.request.pretty_url,
                            response=fake_response)
        
        return True
    
    def _create_fake_oauth_response(self) -> Dict[str, Any]:
        """Create a fake OAuth2 token response."""
        current_time = int(time.time())
        
        response = {
            "access_token": self.fake_token,
            "token_type": self.token_type,
            "expires_in": self.token_lifetime,
            "scope": self.scope,
            "issued_at": current_time,
            "expires_at": current_time + self.token_lifetime,
        }
        
        # Add some additional fields that might be expected
        if "cloud-platform" in self.scope:
            response.update({
                "project_id": "fake-project-id",
                "client_id": "fake-client-id",
            })
        
        return response
