"""Route Redirector Addon for Proxy Toolkit."""

import re
from typing import Dict, Any, List
from urllib.parse import urlparse, urlunparse

from mitmproxy import http

from ..core.base_addon import BaseAddon


class RedirectRule:
    """Represents a URL redirection rule."""
    
    def __init__(self, rule_config: Dict[str, Any]):
        self.match = rule_config.get('match', {})
        self.redirect = rule_config.get('redirect', {})
        
        # Parse match conditions
        self.host_pattern = self.match.get('host')
        self.path_pattern = self.match.get('path')
        self.method_pattern = self.match.get('method')
        
        # Parse redirect target
        self.target_host = self.redirect.get('host')
        self.target_path = self.redirect.get('path')
        self.target_scheme = self.redirect.get('scheme')
        self.preserve_query = self.redirect.get('preserve_query', True)
    
    def matches(self, flow: http.HTTPFlow) -> bool:
        """Check if this rule matches the given flow."""
        parsed_url = urlparse(flow.request.pretty_url)
        
        # Check host pattern
        if self.host_pattern:
            if not self._match_pattern(parsed_url.hostname or "", self.host_pattern):
                return False
        
        # Check path pattern
        if self.path_pattern:
            if not self._match_pattern(parsed_url.path, self.path_pattern):
                return False
        
        # Check method pattern
        if self.method_pattern:
            if not self._match_pattern(flow.request.method, self.method_pattern):
                return False
        
        return True
    
    def _match_pattern(self, value: str, pattern: str) -> bool:
        """Match a value against a pattern (supports wildcards and regex)."""
        if pattern.startswith('regex:'):
            # Regex pattern
            regex_pattern = pattern[6:]
            return bool(re.match(regex_pattern, value))
        elif '*' in pattern:
            # Wildcard pattern
            regex_pattern = pattern.replace('*', '.*')
            return bool(re.match(regex_pattern, value))
        else:
            # Exact match
            return value == pattern
    
    def apply_redirect(self, flow: http.HTTPFlow) -> str:
        """Apply the redirect rule and return the new URL."""
        parsed_url = urlparse(flow.request.pretty_url)
        
        # Start with original URL components
        new_scheme = self.target_scheme or parsed_url.scheme
        new_host = self.target_host or parsed_url.hostname
        new_path = self.target_path or parsed_url.path
        new_query = parsed_url.query if self.preserve_query else ""
        
        # Apply path substitutions
        if self.target_path and '{path}' in self.target_path:
            # Extract path from original URL
            original_path = parsed_url.path
            if self.path_pattern and self.path_pattern.endswith('*'):
                # Extract the wildcard part
                prefix = self.path_pattern[:-1]
                if original_path.startswith(prefix):
                    wildcard_part = original_path[len(prefix):]
                    new_path = self.target_path.replace('{path}', wildcard_part)
        
        # Construct new URL
        new_url = urlunparse((
            new_scheme,
            new_host,
            new_path,
            parsed_url.params,
            new_query,
            parsed_url.fragment
        ))
        
        return new_url


class RouteRedirectorAddon(BaseAddon):
    """Addon to redirect HTTP requests based on configurable rules."""
    
    PLUGIN_NAME = "route_redirector"
    
    def __init__(self, name: str):
        super().__init__(name)
        self.rules: List[RedirectRule] = []
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Return the configuration schema for route redirector."""
        return {
            "rules": {
                "type": "array",
                "default": [],
                "description": "List of URL redirection rules",
                "items": {
                    "type": "object",
                    "properties": {
                        "match": {
                            "type": "object",
                            "properties": {
                                "host": {"type": "string", "description": "Host pattern to match"},
                                "path": {"type": "string", "description": "Path pattern to match"},
                                "method": {"type": "string", "description": "HTTP method to match"}
                            }
                        },
                        "redirect": {
                            "type": "object",
                            "properties": {
                                "host": {"type": "string", "description": "Target host"},
                                "path": {"type": "string", "description": "Target path (supports {path} substitution)"},
                                "scheme": {"type": "string", "description": "Target scheme (http/https)"},
                                "preserve_query": {"type": "boolean", "description": "Whether to preserve query parameters"}
                            }
                        }
                    }
                }
            }
        }
    
    def configure(self, config: Dict[str, Any]) -> None:
        """Configure the route redirector."""
        super().configure(config)
        
        # Parse rules
        self.rules = []
        rules_config = config.get('rules', [])
        
        for rule_config in rules_config:
            try:
                rule = RedirectRule(rule_config)
                self.rules.append(rule)
            except Exception as e:
                self.logger.error("Failed to parse redirect rule", rule=rule_config, error=str(e))
        
        self.logger.info("Route redirector configured", rules_count=len(self.rules))
    
    def _should_process_request_impl(self, flow: http.HTTPFlow) -> bool:
        """Check if any rules match this request."""
        return any(rule.matches(flow) for rule in self.rules)
    
    def _process_request(self, flow: http.HTTPFlow) -> bool:
        """Apply URL redirection to the request."""
        for rule in self.rules:
            if rule.matches(flow):
                original_url = flow.request.pretty_url
                new_url = rule.apply_redirect(flow)
                
                if new_url != original_url:
                    # Parse the new URL and update the request
                    parsed_new_url = urlparse(new_url)
                    
                    flow.request.scheme = parsed_new_url.scheme
                    flow.request.host = parsed_new_url.hostname
                    flow.request.port = parsed_new_url.port or (443 if parsed_new_url.scheme == 'https' else 80)
                    flow.request.path = parsed_new_url.path
                    if parsed_new_url.query:
                        flow.request.query = parsed_new_url.query
                    
                    self.logger.intercept("url_redirect", original_url,
                                        new_url=new_url,
                                        rule_match=rule.match,
                                        rule_redirect=rule.redirect)
                    
                    return True
        
        return False
