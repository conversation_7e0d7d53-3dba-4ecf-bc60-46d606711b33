"""Proxy Toolkit addons package."""

# Import all addon classes for automatic discovery
from .oauth_interceptor import OAuthInterceptorAddon
from .request_logger import RequestLoggerAddon

# Placeholder imports for future addons
try:
    from .header_modifier import HeaderModifierAddon
except ImportError:
    HeaderModifierAddon = None

try:
    from .route_redirector import RouteRedirectorAddon  
except ImportError:
    RouteRedirectorAddon = None

__all__ = [
    "OAuthInterceptorAddon",
    "RequestLoggerAddon",
    "HeaderModifierAddon", 
    "RouteRedirectorAddon",
]
