{"config_file": "config.yaml", "event": "Master addon configuring", "logger": "__mitmproxy_script__.master_addon", "level": "info", "timestamp": "2025-07-16T09:15:37.046444Z"}
{"name": "header_modifier", "class_name": "HeaderModifierAddon", "event": "Plugin registered", "logger": "proxy_toolkit.core.registry", "level": "info", "timestamp": "2025-07-16T09:15:37.047680Z"}
{"name": "oauth_interceptor", "class_name": "OAuthInterceptorAddon", "event": "Plugin registered", "logger": "proxy_toolkit.core.registry", "level": "info", "timestamp": "2025-07-16T09:15:37.047865Z"}
{"name": "request_logger", "class_name": "RequestLoggerAddon", "event": "Plugin registered", "logger": "proxy_toolkit.core.registry", "level": "info", "timestamp": "2025-07-16T09:15:37.048274Z"}
{"name": "route_redirector", "class_name": "RouteRedirectorAddon", "event": "Plugin registered", "logger": "proxy_toolkit.core.registry", "level": "info", "timestamp": "2025-07-16T09:15:37.048474Z"}
{"plugin": "oauth_interceptor", "config": {"fake_token": "fake_access_token_for_litellm_proxy", "scope": "https://www.googleapis.com/auth/cloud-platform", "token_lifetime": 3600, "token_type": "Bearer"}, "event": "Addon configured", "logger": "proxy_toolkit.core.logger", "level": "info", "timestamp": "2025-07-16T09:15:37.048656Z"}
{"plugin": "oauth_interceptor", "token_lifetime": 3600, "scope": "https://www.googleapis.com/auth/cloud-platform", "fake_token": "fake_access_token_fo...", "event": "OAuth interceptor configured", "logger": "proxy_toolkit.core.logger", "level": "info", "timestamp": "2025-07-16T09:15:37.048759Z"}
{"name": "oauth_interceptor", "enabled": true, "event": "Plugin instance created", "logger": "proxy_toolkit.core.registry", "level": "info", "timestamp": "2025-07-16T09:15:37.048858Z"}
{"name": "oauth_interceptor", "priority": 100, "event": "Plugin loaded", "logger": "__mitmproxy_script__.master_addon", "level": "info", "timestamp": "2025-07-16T09:15:37.048962Z"}
{"plugin": "request_logger", "config": {"exclude_paths": ["/health", "/metrics"], "log_requests": true, "log_responses": false}, "event": "Addon configured", "logger": "proxy_toolkit.core.logger", "level": "info", "timestamp": "2025-07-16T09:15:37.049069Z"}
{"plugin": "request_logger", "log_requests": true, "log_responses": false, "exclude_paths": ["/health", "/metrics"], "event": "Request logger configured", "logger": "proxy_toolkit.core.logger", "level": "info", "timestamp": "2025-07-16T09:15:37.049155Z"}
{"name": "request_logger", "enabled": true, "event": "Plugin instance created", "logger": "proxy_toolkit.core.registry", "level": "info", "timestamp": "2025-07-16T09:15:37.049239Z"}
{"name": "request_logger", "priority": 999, "event": "Plugin loaded", "logger": "__mitmproxy_script__.master_addon", "level": "info", "timestamp": "2025-07-16T09:15:37.049319Z"}
{"total_plugins": 2, "enabled_plugins": ["oauth_interceptor", "request_logger"], "event": "All plugins loaded", "logger": "__mitmproxy_script__.master_addon", "level": "info", "timestamp": "2025-07-16T09:15:37.049404Z"}
HTTP(S) proxy listening at 127.0.0.1:8888.
{"event": "Proxy Toolkit started", "logger": "__mitmproxy_script__.master_addon", "level": "info", "timestamp": "2025-07-16T09:15:37.050279Z"}
{"plugin": "oauth_interceptor", "enabled": true, "priority": 100, "event": "Addon started", "logger": "proxy_toolkit.core.logger", "level": "info", "timestamp": "2025-07-16T09:15:37.050376Z"}
{"plugin": "request_logger", "enabled": true, "priority": 999, "event": "Addon started", "logger": "proxy_toolkit.core.logger", "level": "info", "timestamp": "2025-07-16T09:15:37.050464Z"}
{"event": "Proxy Toolkit shutting down", "logger": "__mitmproxy_script__.master_addon", "level": "info", "timestamp": "2025-07-16T09:15:41.464147Z"}
{"plugin": "oauth_interceptor", "stats": {"requests_processed": 0, "requests_modified": 0, "errors": 0}, "event": "Addon stopped", "logger": "proxy_toolkit.core.logger", "level": "info", "timestamp": "2025-07-16T09:15:41.464330Z"}
{"plugin": "request_logger", "stats": {"requests_processed": 0, "requests_modified": 0, "errors": 0}, "event": "Addon stopped", "logger": "proxy_toolkit.core.logger", "level": "info", "timestamp": "2025-07-16T09:15:41.464435Z"}
