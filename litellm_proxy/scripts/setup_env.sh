#!/bin/bash
# Setup environment for OAuth Interceptor

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "🔧 Setting up OAuth Interceptor environment..."

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✓\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m⚠\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m✗\033[0m $1"
}

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    print_error "uv is not installed. Please install it first:"
    echo "  curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

print_status "uv is installed"

# Navigate to project directory
cd "$PROJECT_DIR"

# Install dependencies
echo "📦 Installing dependencies..."
uv sync
print_status "Dependencies installed"

# Create default config if it doesn't exist
if [ ! -f "config.yaml" ]; then
    echo "⚙️ Creating default configuration..."
    uv run oauth-interceptor init-config
    print_status "Default configuration created"
else
    print_warning "Configuration file already exists"
fi

# Make scripts executable
chmod +x scripts/*.py scripts/*.sh
print_status "Scripts made executable"

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Usage:"
echo "  # Start the proxy"
echo "  uv run oauth-interceptor start"
echo ""
echo "  # Or use the standalone script"
echo "  python scripts/start_proxy.py"
echo ""
echo "  # Check status"
echo "  uv run oauth-interceptor status"
echo ""
echo "  # Test functionality"
echo "  uv run oauth-interceptor test"
echo ""
echo "Environment variables for gemini-cli:"
echo "  export HTTP_PROXY=http://localhost:8888"
echo "  export HTTPS_PROXY=http://localhost:8888"
echo "  export GOOGLE_GENAI_USE_VERTEXAI=true"
echo "  export GOOGLE_VERTEX_BASE_URL=\"http://************:4000/vertex_ai\""
echo "  export GOOGLE_CLOUD_PROJECT=\"program-center\""
echo "  export GOOGLE_CLOUD_LOCATION=\"us-central1\""
echo "  export GOOGLE_APPLICATION_CREDENTIALS=\"/home/<USER>/.gemini/credentials.json\""
