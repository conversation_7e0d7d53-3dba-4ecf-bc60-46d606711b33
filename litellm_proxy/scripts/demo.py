#!/usr/bin/env python3
"""Demo script to show OAuth Interceptor in action."""

import os
import sys
import time
import subprocess
import requests
import json
from pathlib import Path

def print_banner():
    """Print demo banner."""
    print("=" * 70)
    print("🎭 OAuth Interceptor Demo")
    print("   Demonstrating OAuth2 token interception for gemini-cli")
    print("=" * 70)

def print_step(step, description):
    """Print step information."""
    print(f"\n📋 Step {step}: {description}")
    print("-" * 50)

def wait_for_user():
    """Wait for user to press Enter."""
    input("\n⏸️  Press Enter to continue...")

def start_proxy_demo():
    """Start proxy and demonstrate functionality."""
    print_banner()
    
    # Step 1: Show configuration
    print_step(1, "Configuration Overview")
    print("🔧 OAuth Interceptor will:")
    print("   • Listen on port 8888")
    print("   • Intercept requests to *.googleapis.com/oauth2/*/token")
    print("   • Return fake OAuth2 tokens")
    print("   • Allow other requests to pass through")
    
    wait_for_user()
    
    # Step 2: Start the proxy
    print_step(2, "Starting OAuth Interceptor")
    
    base_dir = Path(__file__).parent.parent
    addon_path = base_dir / "src" / "oauth_interceptor" / "addons" / "oauth_interceptor.py"
    
    cmd = [
        'mitmdump',
        '--listen-host', '127.0.0.1',
        '--listen-port', '8888',
        '--scripts', str(addon_path),
        '--set', 'oauth_fake_token=demo_fake_token_12345',
        '--set', 'oauth_token_lifetime=7200',
    ]
    
    print("🚀 Starting proxy with command:")
    print(f"   {' '.join(cmd)}")
    print("\n📝 You should see mitmproxy start up...")
    
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # Wait for proxy to start
    print("⏳ Waiting for proxy to initialize...")
    time.sleep(3)
    
    try:
        # Step 3: Test proxy accessibility
        print_step(3, "Testing Proxy Accessibility")
        
        try:
            response = requests.get(
                'http://httpbin.org/ip',
                proxies={'http': 'http://127.0.0.1:8888'},
                timeout=5
            )
            if response.status_code == 200:
                print("✅ Proxy is accessible and working")
                data = response.json()
                print(f"   Your IP through proxy: {data.get('origin', 'Unknown')}")
            else:
                print(f"❌ Proxy test failed with status {response.status_code}")
                return
        except Exception as e:
            print(f"❌ Cannot access proxy: {e}")
            return
        
        wait_for_user()
        
        # Step 4: Demonstrate OAuth interception
        print_step(4, "Demonstrating OAuth2 Interception")
        
        print("🎯 Sending OAuth2 token request to googleapis.com...")
        print("   This request would normally require real Google credentials")
        print("   But our interceptor will catch it and return a fake token!")
        
        # Simulate OAuth request (using HTTP to avoid certificate issues in demo)
        oauth_url = "http://www.googleapis.com/oauth2/v4/token"  # Using HTTP for demo
        oauth_data = {
            'grant_type': 'client_credentials',
            'scope': 'https://www.googleapis.com/auth/cloud-platform'
        }
        
        try:
            print(f"\n📤 POST {oauth_url}")
            print(f"   Data: {oauth_data}")
            
            response = requests.post(
                oauth_url,
                data=oauth_data,
                proxies={'http': 'http://127.0.0.1:8888'},
                timeout=10
            )
            
            print(f"\n📥 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ OAuth interception successful!")
                print(f"   Access Token: {data.get('access_token', 'N/A')}")
                print(f"   Token Type: {data.get('token_type', 'N/A')}")
                print(f"   Expires In: {data.get('expires_in', 'N/A')} seconds")
                print(f"   Scope: {data.get('scope', 'N/A')}")
                
                if 'demo_fake_token_12345' in data.get('access_token', ''):
                    print("\n🎉 Perfect! The interceptor returned our fake token!")
                else:
                    print("\n⚠️  Token doesn't match expected fake token")
            else:
                print(f"❌ OAuth request failed: {response.text}")
                
        except Exception as e:
            print(f"❌ OAuth test failed: {e}")
        
        wait_for_user()
        
        # Step 5: Show environment setup
        print_step(5, "Environment Setup for gemini-cli")
        
        print("🔧 To use with gemini-cli, set these environment variables:")
        print()
        print("export HTTP_PROXY=http://localhost:8888")
        print("export HTTPS_PROXY=http://localhost:8888")
        print("export GOOGLE_GENAI_USE_VERTEXAI=true")
        print("export GOOGLE_VERTEX_BASE_URL=\"http://************:4000/vertex_ai\"")
        print("export GOOGLE_CLOUD_PROJECT=\"program-center\"")
        print("export GOOGLE_CLOUD_LOCATION=\"us-central1\"")
        print("export GOOGLE_APPLICATION_CREDENTIALS=\"/home/<USER>/.gemini/credentials.json\"")
        print()
        print("Then run: gemini-cli \"Hello, world!\"")
        
        wait_for_user()
        
        print_step(6, "Demo Complete")
        print("🎉 OAuth Interceptor demo completed successfully!")
        print()
        print("📚 What you learned:")
        print("   • How to start the OAuth interceptor")
        print("   • How OAuth2 requests are intercepted and faked")
        print("   • How to configure gemini-cli to use the proxy")
        print()
        print("🚀 Next steps:")
        print("   • Configure your actual gemini-cli environment")
        print("   • Test with real gemini-cli commands")
        print("   • Monitor logs for debugging")
        
    finally:
        # Clean up
        print("\n🧹 Cleaning up...")
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
        print("✅ Proxy stopped")


if __name__ == "__main__":
    try:
        start_proxy_demo()
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        sys.exit(1)
