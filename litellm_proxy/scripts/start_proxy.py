#!/usr/bin/env python3
"""Standalone script to start OAuth Interceptor proxy."""

import os
import sys
import subprocess
import signal
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from oauth_interceptor.config import load_config


def find_addon_path():
    """Find the OAuth interceptor addon path."""
    script_dir = Path(__file__).parent
    addon_path = script_dir.parent / "src" / "oauth_interceptor" / "addons" / "oauth_interceptor.py"
    
    if not addon_path.exists():
        raise FileNotFoundError(f"Addon not found at {addon_path}")
    
    return str(addon_path)


def start_proxy(config_file=None, port=None, host=None, verbose=False):
    """Start the OAuth interceptor proxy."""
    # Load configuration
    config = load_config(config_file)
    
    # Override with command line options
    if port:
        config.proxy.port = port
    if host:
        config.proxy.host = host
    if verbose:
        config.logging.level = 'DEBUG'
    
    # Get addon path
    addon_path = find_addon_path()
    
    # Build mitmproxy command
    cmd = [
        'mitmdump',
        '--listen-host', config.proxy.host,
        '--listen-port', str(config.proxy.port),
        '--scripts', addon_path,
        '--set', f'oauth_token_lifetime={config.oauth.token_lifetime}',
        '--set', f'oauth_scope={config.oauth.scope}',
        '--set', f'oauth_fake_token={config.oauth.fake_token}',
        '--set', f'oauth_token_type={config.oauth.token_type}',
    ]
    
    if verbose:
        cmd.append('--verbose')
    
    print(f"Starting OAuth Interceptor on {config.proxy.host}:{config.proxy.port}")
    print(f"Command: {' '.join(cmd)}")
    print("Press Ctrl+C to stop")
    print()
    
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        print("\nShutting down OAuth Interceptor...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # Start mitmproxy
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Failed to start mitmproxy: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print("mitmproxy not found. Please install it: pip install mitmproxy")
        sys.exit(1)


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Start OAuth Interceptor proxy")
    parser.add_argument('--config', '-c', help='Configuration file path')
    parser.add_argument('--port', '-p', type=int, default=8888, help='Proxy port')
    parser.add_argument('--host', '-h', default='127.0.0.1', help='Proxy host')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    start_proxy(
        config_file=args.config,
        port=args.port,
        host=args.host,
        verbose=args.verbose
    )
