from typing import Awaitable, Callable, Optional

class Server:
    def getsockname(self) -> tuple[str, int]: ...
    def send_datagram(self, data: bytes, src_addr: tuple[str, int], dst_addr: tuple[str, int]) -> None: ...
    def close(self) -> None: ...
    async def wait_closed(self) -> None: ...

class TcpStream:
    async def read(self, n: int) -> bytes: ...
    def write(self, data: bytes): ...
    async def drain(self) -> None: ...
    def write_eof(self): ...
    def close(self): ...
    def is_closing(self) -> bool: ...
    def get_extra_info(self, name: str, default=None) -> tuple[str, int]: ...
    def __repr__(self) -> str: ...

async def start_server(
    host: str,
    port: int,
    private_key: str,
    peer_public_keys: list[str],
    handle_connection: Callable[[TcpStream], Awaitable[None]],
    receive_datagram: Callable[[bytes, tuple[str, int], tuple[str, int]], None],
) -> Server: ...
def genkey() -> str: ...
def pubkey(private_key: str) -> str: ...
