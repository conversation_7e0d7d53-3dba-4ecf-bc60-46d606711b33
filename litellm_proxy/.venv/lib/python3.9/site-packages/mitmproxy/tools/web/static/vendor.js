require=(function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.CodeMirror=t()}(this,function(){"use strict";function e(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}function t(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function r(e,r){return t(e).appendChild(r)}function n(e,t,r,n){var i=document.createElement(e);if(r&&(i.className=r),n&&(i.style.cssText=n),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function i(e,t,r,i){var o=n(e,t,r,i);return o.setAttribute("role","presentation"),o}function o(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function l(){var e;try{e=document.activeElement}catch(t){e=document.body||null}for(;e&&e.shadowRoot&&e.shadowRoot.activeElement;)e=e.shadowRoot.activeElement;return e}function s(t,r){var n=t.className;e(r).test(n)||(t.className+=(n?" ":"")+r)}function a(t,r){for(var n=t.split(" "),i=0;i<n.length;i++)n[i]&&!e(n[i]).test(r)&&(r+=" "+n[i]);return r}function u(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function c(e,t,r){t||(t={});for(var n in e)!e.hasOwnProperty(n)||!1===r&&t.hasOwnProperty(n)||(t[n]=e[n]);return t}function f(e,t,r,n,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=n||0,l=i||0;;){var s=e.indexOf("\t",o);if(s<0||s>=t)return l+(t-o);l+=s-o,l+=r-l%r,o=s+1}}function h(e,t){for(var r=0;r<e.length;++r)if(e[r]==t)return r;return-1}function d(e,t,r){for(var n=0,i=0;;){var o=e.indexOf("\t",n);-1==o&&(o=e.length);var l=o-n;if(o==e.length||i+l>=t)return n+Math.min(l,t-i);if(i+=o-n,i+=r-i%r,n=o+1,i>=t)return n}}function p(e){for(;Kl.length<=e;)Kl.push(g(Kl)+" ");return Kl[e]}function g(e){return e[e.length-1]}function v(e,t){for(var r=[],n=0;n<e.length;n++)r[n]=t(e[n],n);return r}function m(e,t,r){for(var n=0,i=r(t);n<e.length&&r(e[n])<=i;)n++;e.splice(n,0,t)}function y(){}function b(e,t){var r;return Object.create?r=Object.create(e):(y.prototype=e,r=new y),t&&c(t,r),r}function w(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||jl.test(e))}function x(e,t){return t?!!(t.source.indexOf("\\w")>-1&&w(e))||t.test(e):w(e)}function C(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}function S(e){return e.charCodeAt(0)>=768&&Xl.test(e)}function L(e,t,r){for(;(r<0?t>0:t<e.length)&&S(e.charAt(t));)t+=r;return t}function k(e,t,r){for(var n=t>r?-1:1;;){if(t==r)return t;var i=(t+r)/2,o=n<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:r;e(o)?r=o:t=o+n}}function T(e,t,r){var o=this;this.input=r,o.scrollbarFiller=n("div",null,"CodeMirror-scrollbar-filler"),o.scrollbarFiller.setAttribute("cm-not-content","true"),o.gutterFiller=n("div",null,"CodeMirror-gutter-filler"),o.gutterFiller.setAttribute("cm-not-content","true"),o.lineDiv=i("div",null,"CodeMirror-code"),o.selectionDiv=n("div",null,null,"position: relative; z-index: 1"),o.cursorDiv=n("div",null,"CodeMirror-cursors"),o.measure=n("div",null,"CodeMirror-measure"),o.lineMeasure=n("div",null,"CodeMirror-measure"),o.lineSpace=i("div",[o.measure,o.lineMeasure,o.selectionDiv,o.cursorDiv,o.lineDiv],null,"position: relative; outline: none");var l=i("div",[o.lineSpace],"CodeMirror-lines");o.mover=n("div",[l],null,"position: relative"),o.sizer=n("div",[o.mover],"CodeMirror-sizer"),o.sizerWidth=null,o.heightForcer=n("div",null,null,"position: absolute; height: "+Rl+"px; width: 1px;"),o.gutters=n("div",null,"CodeMirror-gutters"),o.lineGutter=null,o.scroller=n("div",[o.sizer,o.heightForcer,o.gutters],"CodeMirror-scroll"),o.scroller.setAttribute("tabIndex","-1"),o.wrapper=n("div",[o.scrollbarFiller,o.gutterFiller,o.scroller],"CodeMirror"),gl&&vl<8&&(o.gutters.style.zIndex=-1,o.scroller.style.paddingRight=0),ml||fl&&Tl||(o.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(o.wrapper):e(o.wrapper)),o.viewFrom=o.viewTo=t.first,o.reportedViewFrom=o.reportedViewTo=t.first,o.view=[],o.renderedView=null,o.externalMeasured=null,o.viewOffset=0,o.lastWrapHeight=o.lastWrapWidth=0,o.updateLineNumbers=null,o.nativeBarWidth=o.barHeight=o.barWidth=0,o.scrollbarsClipped=!1,o.lineNumWidth=o.lineNumInnerWidth=o.lineNumChars=null,o.alignWidgets=!1,o.cachedCharWidth=o.cachedTextHeight=o.cachedPaddingH=null,o.maxLine=null,o.maxLineLength=0,o.maxLineChanged=!1,o.wheelDX=o.wheelDY=o.wheelStartX=o.wheelStartY=null,o.shift=!1,o.selForContextMenu=null,o.activeTouch=null,r.init(o)}function M(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var r=e;!r.lines;)for(var n=0;;++n){var i=r.children[n],o=i.chunkSize();if(t<o){r=i;break}t-=o}return r.lines[t]}function N(e,t,r){var n=[],i=t.line;return e.iter(t.line,r.line+1,function(e){var o=e.text;i==r.line&&(o=o.slice(0,r.ch)),i==t.line&&(o=o.slice(t.ch)),n.push(o),++i}),n}function O(e,t,r){var n=[];return e.iter(t,r,function(e){n.push(e.text)}),n}function A(e,t){var r=t-e.height;if(r)for(var n=e;n;n=n.parent)n.height+=r}function W(e){if(null==e.parent)return null;for(var t=e.parent,r=h(t.lines,e),n=t.parent;n;t=n,n=n.parent)for(var i=0;n.children[i]!=t;++i)r+=n.children[i].chunkSize();return r+t.first}function D(e,t){var r=e.first;e:do{for(var n=0;n<e.children.length;++n){var i=e.children[n],o=i.height;if(t<o){e=i;continue e}t-=o,r+=i.chunkSize()}return r}while(!e.lines);for(var l=0;l<e.lines.length;++l){var s=e.lines[l].height;if(t<s)break;t-=s}return r+l}function H(e,t){return t>=e.first&&t<e.first+e.size}function F(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function P(e,t,r){if(void 0===r&&(r=null),!(this instanceof P))return new P(e,t,r);this.line=e,this.ch=t,this.sticky=r}function E(e,t){return e.line-t.line||e.ch-t.ch}function z(e,t){return e.sticky==t.sticky&&0==E(e,t)}function I(e){return P(e.line,e.ch)}function R(e,t){return E(e,t)<0?t:e}function B(e,t){return E(e,t)<0?e:t}function G(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function U(e,t){if(t.line<e.first)return P(e.first,0);var r=e.first+e.size-1;return t.line>r?P(r,M(e,r).text.length):V(t,M(e,t.line).text.length)}function V(e,t){var r=e.ch;return null==r||r>t?P(e.line,t):r<0?P(e.line,0):e}function K(e,t){for(var r=[],n=0;n<t.length;n++)r[n]=U(e,t[n]);return r}function j(){Yl=!0}function X(){_l=!0}function Y(e,t,r){this.marker=e,this.from=t,this.to=r}function _(e,t){if(e)for(var r=0;r<e.length;++r){var n=e[r];if(n.marker==t)return n}}function $(e,t){for(var r,n=0;n<e.length;++n)e[n]!=t&&(r||(r=[])).push(e[n]);return r}function q(e,t){e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],t.marker.attachLine(e)}function Z(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker;if(null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t)||o.from==t&&"bookmark"==l.type&&(!r||!o.marker.insertLeft)){var s=null==o.to||(l.inclusiveRight?o.to>=t:o.to>t);(n||(n=[])).push(new Y(l,o.from,s?null:o.to))}}return n}function Q(e,t,r){var n;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker;if(null==o.to||(l.inclusiveRight?o.to>=t:o.to>t)||o.from==t&&"bookmark"==l.type&&(!r||o.marker.insertLeft)){var s=null==o.from||(l.inclusiveLeft?o.from<=t:o.from<t);(n||(n=[])).push(new Y(l,s?null:o.from-t,null==o.to?null:o.to-t))}}return n}function J(e,t){if(t.full)return null;var r=H(e,t.from.line)&&M(e,t.from.line).markedSpans,n=H(e,t.to.line)&&M(e,t.to.line).markedSpans;if(!r&&!n)return null;var i=t.from.ch,o=t.to.ch,l=0==E(t.from,t.to),s=Z(r,i,l),a=Q(n,o,l),u=1==t.text.length,c=g(t.text).length+(u?i:0);if(s)for(var f=0;f<s.length;++f){var h=s[f];if(null==h.to){var d=_(a,h.marker);d?u&&(h.to=null==d.to?null:d.to+c):h.to=i}}if(a)for(var p=0;p<a.length;++p){var v=a[p];null!=v.to&&(v.to+=c),null==v.from?_(s,v.marker)||(v.from=c,u&&(s||(s=[])).push(v)):(v.from+=c,u&&(s||(s=[])).push(v))}s&&(s=ee(s)),a&&a!=s&&(a=ee(a));var m=[s];if(!u){var y,b=t.text.length-2;if(b>0&&s)for(var w=0;w<s.length;++w)null==s[w].to&&(y||(y=[])).push(new Y(s[w].marker,null,null));for(var x=0;x<b;++x)m.push(y);m.push(a)}return m}function ee(e){for(var t=0;t<e.length;++t){var r=e[t];null!=r.from&&r.from==r.to&&!1!==r.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function te(e,t,r){var n=null;if(e.iter(t.line,r.line+1,function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var r=e.markedSpans[t].marker;!r.readOnly||n&&-1!=h(n,r)||(n||(n=[])).push(r)}}),!n)return null;for(var i=[{from:t,to:r}],o=0;o<n.length;++o)for(var l=n[o],s=l.find(0),a=0;a<i.length;++a){var u=i[a];if(!(E(u.to,s.from)<0||E(u.from,s.to)>0)){var c=[a,1],f=E(u.from,s.from),d=E(u.to,s.to);(f<0||!l.inclusiveLeft&&!f)&&c.push({from:u.from,to:s.from}),(d>0||!l.inclusiveRight&&!d)&&c.push({from:s.to,to:u.to}),i.splice.apply(i,c),a+=c.length-3}}return i}function re(e){var t=e.markedSpans;if(t){for(var r=0;r<t.length;++r)t[r].marker.detachLine(e);e.markedSpans=null}}function ne(e,t){if(t){for(var r=0;r<t.length;++r)t[r].marker.attachLine(e);e.markedSpans=t}}function ie(e){return e.inclusiveLeft?-1:0}function oe(e){return e.inclusiveRight?1:0}function le(e,t){var r=e.lines.length-t.lines.length;if(0!=r)return r;var n=e.find(),i=t.find(),o=E(n.from,i.from)||ie(e)-ie(t);if(o)return-o;var l=E(n.to,i.to)||oe(e)-oe(t);return l||t.id-e.id}function se(e,t){var r,n=_l&&e.markedSpans;if(n)for(var i=void 0,o=0;o<n.length;++o)(i=n[o]).marker.collapsed&&null==(t?i.from:i.to)&&(!r||le(r,i.marker)<0)&&(r=i.marker);return r}function ae(e){return se(e,!0)}function ue(e){return se(e,!1)}function ce(e,t,r,n,i){var o=M(e,t),l=_l&&o.markedSpans;if(l)for(var s=0;s<l.length;++s){var a=l[s];if(a.marker.collapsed){var u=a.marker.find(0),c=E(u.from,r)||ie(a.marker)-ie(i),f=E(u.to,n)||oe(a.marker)-oe(i);if(!(c>=0&&f<=0||c<=0&&f>=0)&&(c<=0&&(a.marker.inclusiveRight&&i.inclusiveLeft?E(u.to,r)>=0:E(u.to,r)>0)||c>=0&&(a.marker.inclusiveRight&&i.inclusiveLeft?E(u.from,n)<=0:E(u.from,n)<0)))return!0}}}function fe(e){for(var t;t=ae(e);)e=t.find(-1,!0).line;return e}function he(e){for(var t;t=ue(e);)e=t.find(1,!0).line;return e}function de(e){for(var t,r;t=ue(e);)e=t.find(1,!0).line,(r||(r=[])).push(e);return r}function pe(e,t){var r=M(e,t),n=fe(r);return r==n?t:W(n)}function ge(e,t){if(t>e.lastLine())return t;var r,n=M(e,t);if(!ve(e,n))return t;for(;r=ue(n);)n=r.find(1,!0).line;return W(n)+1}function ve(e,t){var r=_l&&t.markedSpans;if(r)for(var n=void 0,i=0;i<r.length;++i)if((n=r[i]).marker.collapsed){if(null==n.from)return!0;if(!n.marker.widgetNode&&0==n.from&&n.marker.inclusiveLeft&&me(e,t,n))return!0}}function me(e,t,r){if(null==r.to){var n=r.marker.find(1,!0);return me(e,n.line,_(n.line.markedSpans,r.marker))}if(r.marker.inclusiveRight&&r.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==r.to&&(null==i.to||i.to!=r.from)&&(i.marker.inclusiveLeft||r.marker.inclusiveRight)&&me(e,t,i))return!0}function ye(e){for(var t=0,r=(e=fe(e)).parent,n=0;n<r.lines.length;++n){var i=r.lines[n];if(i==e)break;t+=i.height}for(var o=r.parent;o;r=o,o=r.parent)for(var l=0;l<o.children.length;++l){var s=o.children[l];if(s==r)break;t+=s.height}return t}function be(e){if(0==e.height)return 0;for(var t,r=e.text.length,n=e;t=ae(n);){var i=t.find(0,!0);n=i.from.line,r+=i.from.ch-i.to.ch}for(n=e;t=ue(n);){var o=t.find(0,!0);r-=n.text.length-o.from.ch,r+=(n=o.to.line).text.length-o.to.ch}return r}function we(e){var t=e.display,r=e.doc;t.maxLine=M(r,r.first),t.maxLineLength=be(t.maxLine),t.maxLineChanged=!0,r.iter(function(e){var r=be(e);r>t.maxLineLength&&(t.maxLineLength=r,t.maxLine=e)})}function xe(e,t,r,n){if(!e)return n(t,r,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var l=e[o];(l.from<r&&l.to>t||t==r&&l.to==t)&&(n(Math.max(l.from,t),Math.min(l.to,r),1==l.level?"rtl":"ltr",o),i=!0)}i||n(t,r,"ltr")}function Ce(e,t,r){var n;$l=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==r?n=i:$l=i),o.from==t&&(o.from!=o.to&&"before"!=r?n=i:$l=i)}return null!=n?n:$l}function Se(e,t){var r=e.order;return null==r&&(r=e.order=ql(e.text,t)),r}function Le(e,t){return e._handlers&&e._handlers[t]||Zl}function ke(e,t,r){if(e.removeEventListener)e.removeEventListener(t,r,!1);else if(e.detachEvent)e.detachEvent("on"+t,r);else{var n=e._handlers,i=n&&n[t];if(i){var o=h(i,r);o>-1&&(n[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function Te(e,t){var r=Le(e,t);if(r.length)for(var n=Array.prototype.slice.call(arguments,2),i=0;i<r.length;++i)r[i].apply(null,n)}function Me(e,t,r){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),Te(e,r||t.type,e,t),He(t)||t.codemirrorIgnore}function Ne(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var r=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),n=0;n<t.length;++n)-1==h(r,t[n])&&r.push(t[n])}function Oe(e,t){return Le(e,t).length>0}function Ae(e){e.prototype.on=function(e,t){Ql(this,e,t)},e.prototype.off=function(e,t){ke(this,e,t)}}function We(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function De(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function He(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function Fe(e){We(e),De(e)}function Pe(e){return e.target||e.srcElement}function Ee(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),Ml&&e.ctrlKey&&1==t&&(t=3),t}function ze(e){if(null==zl){var t=n("span","​");r(e,n("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(zl=t.offsetWidth<=1&&t.offsetHeight>2&&!(gl&&vl<8))}var i=zl?n("span","​"):n("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return i.setAttribute("cm-text",""),i}function Ie(e){if(null!=Il)return Il;var n=r(e,document.createTextNode("AخA")),i=Wl(n,0,1).getBoundingClientRect(),o=Wl(n,1,2).getBoundingClientRect();return t(e),!(!i||i.left==i.right)&&(Il=o.right-i.right<3)}function Re(e){if(null!=ns)return ns;var t=r(e,n("span","x")),i=t.getBoundingClientRect(),o=Wl(t,0,1).getBoundingClientRect();return ns=Math.abs(i.left-o.left)>1}function Be(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),is[e]=t}function Ge(e){if("string"==typeof e&&os.hasOwnProperty(e))e=os[e];else if(e&&"string"==typeof e.name&&os.hasOwnProperty(e.name)){var t=os[e.name];"string"==typeof t&&(t={name:t}),(e=b(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Ge("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Ge("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Ue(e,t){t=Ge(t);var r=is[t.name];if(!r)return Ue(e,"text/plain");var n=r(e,t);if(ls.hasOwnProperty(t.name)){var i=ls[t.name];for(var o in i)i.hasOwnProperty(o)&&(n.hasOwnProperty(o)&&(n["_"+o]=n[o]),n[o]=i[o])}if(n.name=t.name,t.helperType&&(n.helperType=t.helperType),t.modeProps)for(var l in t.modeProps)n[l]=t.modeProps[l];return n}function Ve(e,t){c(t,ls.hasOwnProperty(e)?ls[e]:ls[e]={})}function Ke(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var r={};for(var n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r}function je(e,t){for(var r;e.innerMode&&(r=e.innerMode(t))&&r.mode!=e;)t=r.state,e=r.mode;return r||{mode:e,state:t}}function Xe(e,t,r){return!e.startState||e.startState(t,r)}function Ye(e,t,r,n){var i=[e.state.modeGen],o={};tt(e,t.text,e.doc.mode,r,function(e,t){return i.push(e,t)},o,n);for(var l=r.state,s=0;s<e.state.overlays.length;++s)!function(n){r.baseTokens=i;var s=e.state.overlays[n],a=1,u=0;r.state=!0,tt(e,t.text,s.mode,r,function(e,t){for(var r=a;u<e;){var n=i[a];n>e&&i.splice(a,1,e,i[a+1],n),a+=2,u=Math.min(e,n)}if(t)if(s.opaque)i.splice(r,a-r,e,"overlay "+t),a=r+2;else for(;r<a;r+=2){var o=i[r+1];i[r+1]=(o?o+" ":"")+"overlay "+t}},o),r.state=l,r.baseTokens=null,r.baseTokenPos=1}(s);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function _e(e,t,r){if(!t.styles||t.styles[0]!=e.state.modeGen){var n=$e(e,W(t)),i=t.text.length>e.options.maxHighlightLength&&Ke(e.doc.mode,n.state),o=Ye(e,t,n);i&&(n.state=i),t.stateAfter=n.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),r===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function $e(e,t,r){var n=e.doc,i=e.display;if(!n.mode.startState)return new us(n,!0,t);var o=rt(e,t,r),l=o>n.first&&M(n,o-1).stateAfter,s=l?us.fromSaved(n,l,o):new us(n,Xe(n.mode),o);return n.iter(o,t,function(r){qe(e,r.text,s);var n=s.line;r.stateAfter=n==t-1||n%5==0||n>=i.viewFrom&&n<i.viewTo?s.save():null,s.nextLine()}),r&&(n.modeFrontier=s.line),s}function qe(e,t,r,n){var i=e.doc.mode,o=new ss(t,e.options.tabSize,r);for(o.start=o.pos=n||0,""==t&&Ze(i,r.state);!o.eol();)Qe(i,o,r.state),o.start=o.pos}function Ze(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var r=je(e,t);return r.mode.blankLine?r.mode.blankLine(r.state):void 0}}function Qe(e,t,r,n){for(var i=0;i<10;i++){n&&(n[0]=je(e,r).mode);var o=e.token(t,r);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}function Je(e,t,r,n){var i,o,l=e.doc,s=l.mode,a=M(l,(t=U(l,t)).line),u=$e(e,t.line,r),c=new ss(a.text,e.options.tabSize,u);for(n&&(o=[]);(n||c.pos<t.ch)&&!c.eol();)c.start=c.pos,i=Qe(s,c,u.state),n&&o.push(new cs(c,i,Ke(l.mode,u.state)));return n?o:new cs(c,i,u.state)}function et(e,t){if(e)for(;;){var r=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!r)break;e=e.slice(0,r.index)+e.slice(r.index+r[0].length);var n=r[1]?"bgClass":"textClass";null==t[n]?t[n]=r[2]:new RegExp("(?:^|s)"+r[2]+"(?:$|s)").test(t[n])||(t[n]+=" "+r[2])}return e}function tt(e,t,r,n,i,o,l){var s=r.flattenSpans;null==s&&(s=e.options.flattenSpans);var a,u=0,c=null,f=new ss(t,e.options.tabSize,n),h=e.options.addModeClass&&[null];for(""==t&&et(Ze(r,n.state),o);!f.eol();){if(f.pos>e.options.maxHighlightLength?(s=!1,l&&qe(e,t,n,f.pos),f.pos=t.length,a=null):a=et(Qe(r,f,n.state,h),o),h){var d=h[0].name;d&&(a="m-"+(a?d+" "+a:d))}if(!s||c!=a){for(;u<f.start;)i(u=Math.min(f.start,u+5e3),c);c=a}f.start=f.pos}for(;u<f.pos;){var p=Math.min(f.pos,u+5e3);i(p,c),u=p}}function rt(e,t,r){for(var n,i,o=e.doc,l=r?-1:t-(e.doc.mode.innerMode?1e3:100),s=t;s>l;--s){if(s<=o.first)return o.first;var a=M(o,s-1),u=a.stateAfter;if(u&&(!r||s+(u instanceof as?u.lookAhead:0)<=o.modeFrontier))return s;var c=f(a.text,null,e.options.tabSize);(null==i||n>c)&&(i=s-1,n=c)}return i}function nt(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var r=e.first,n=t-1;n>r;n--){var i=M(e,n).stateAfter;if(i&&(!(i instanceof as)||n+i.lookAhead<t)){r=n+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,r)}}function it(e,t,r,n){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),re(e),ne(e,r);var i=n?n(e):1;i!=e.height&&A(e,i)}function ot(e){e.parent=null,re(e)}function lt(e,t){if(!e||/^\s*$/.test(e))return null;var r=t.addModeClass?ps:ds;return r[e]||(r[e]=e.replace(/\S+/g,"cm-$&"))}function st(e,t){var r=i("span",null,null,ml?"padding-right: .1px":null),n={pre:i("pre",[r],"CodeMirror-line"),content:r,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:(gl||ml)&&e.getOption("lineWrapping")};t.measure={};for(var o=0;o<=(t.rest?t.rest.length:0);o++){var l=o?t.rest[o-1]:t.line,s=void 0;n.pos=0,n.addToken=ut,Ie(e.display.measure)&&(s=Se(l,e.doc.direction))&&(n.addToken=ft(n.addToken,s)),n.map=[],dt(l,n,_e(e,l,t!=e.display.externalMeasured&&W(l))),l.styleClasses&&(l.styleClasses.bgClass&&(n.bgClass=a(l.styleClasses.bgClass,n.bgClass||"")),l.styleClasses.textClass&&(n.textClass=a(l.styleClasses.textClass,n.textClass||""))),0==n.map.length&&n.map.push(0,0,n.content.appendChild(ze(e.display.measure))),0==o?(t.measure.map=n.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(n.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(ml){var u=n.content.lastChild;(/\bcm-tab\b/.test(u.className)||u.querySelector&&u.querySelector(".cm-tab"))&&(n.content.className="cm-tab-wrap-hack")}return Te(e,"renderLine",e,t.line,n.pre),n.pre.className&&(n.textClass=a(n.pre.className,n.textClass||"")),n}function at(e){var t=n("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function ut(e,t,r,i,o,l,s){if(t){var a,u=e.splitSpaces?ct(t,e.trailingSpace):t,c=e.cm.state.specialChars,f=!1;if(c.test(t)){a=document.createDocumentFragment();for(var h=0;;){c.lastIndex=h;var d=c.exec(t),g=d?d.index-h:t.length-h;if(g){var v=document.createTextNode(u.slice(h,h+g));gl&&vl<9?a.appendChild(n("span",[v])):a.appendChild(v),e.map.push(e.pos,e.pos+g,v),e.col+=g,e.pos+=g}if(!d)break;h+=g+1;var m=void 0;if("\t"==d[0]){var y=e.cm.options.tabSize,b=y-e.col%y;(m=a.appendChild(n("span",p(b),"cm-tab"))).setAttribute("role","presentation"),m.setAttribute("cm-text","\t"),e.col+=b}else"\r"==d[0]||"\n"==d[0]?((m=a.appendChild(n("span","\r"==d[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",d[0]),e.col+=1):((m=e.cm.options.specialCharPlaceholder(d[0])).setAttribute("cm-text",d[0]),gl&&vl<9?a.appendChild(n("span",[m])):a.appendChild(m),e.col+=1);e.map.push(e.pos,e.pos+1,m),e.pos++}}else e.col+=t.length,a=document.createTextNode(u),e.map.push(e.pos,e.pos+t.length,a),gl&&vl<9&&(f=!0),e.pos+=t.length;if(e.trailingSpace=32==u.charCodeAt(t.length-1),r||i||o||f||s){var w=r||"";i&&(w+=i),o&&(w+=o);var x=n("span",[a],w,s);return l&&(x.title=l),e.content.appendChild(x)}e.content.appendChild(a)}}function ct(e,t){if(e.length>1&&!/  /.test(e))return e;for(var r=t,n="",i=0;i<e.length;i++){var o=e.charAt(i);" "!=o||!r||i!=e.length-1&&32!=e.charCodeAt(i+1)||(o=" "),n+=o,r=" "==o}return n}function ft(e,t){return function(r,n,i,o,l,s,a){i=i?i+" cm-force-border":"cm-force-border";for(var u=r.pos,c=u+n.length;;){for(var f=void 0,h=0;h<t.length&&!((f=t[h]).to>u&&f.from<=u);h++);if(f.to>=c)return e(r,n,i,o,l,s,a);e(r,n.slice(0,f.to-u),i,o,null,s,a),o=null,n=n.slice(f.to-u),u=f.to}}}function ht(e,t,r,n){var i=!n&&r.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!n&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",r.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function dt(e,t,r){var n=e.markedSpans,i=e.text,o=0;if(n)for(var l,s,a,u,c,f,h,d=i.length,p=0,g=1,v="",m=0;;){if(m==p){a=u=c=f=s="",h=null,m=1/0;for(var y=[],b=void 0,w=0;w<n.length;++w){var x=n[w],C=x.marker;"bookmark"==C.type&&x.from==p&&C.widgetNode?y.push(C):x.from<=p&&(null==x.to||x.to>p||C.collapsed&&x.to==p&&x.from==p)?(null!=x.to&&x.to!=p&&m>x.to&&(m=x.to,u=""),C.className&&(a+=" "+C.className),C.css&&(s=(s?s+";":"")+C.css),C.startStyle&&x.from==p&&(c+=" "+C.startStyle),C.endStyle&&x.to==m&&(b||(b=[])).push(C.endStyle,x.to),C.title&&!f&&(f=C.title),C.collapsed&&(!h||le(h.marker,C)<0)&&(h=x)):x.from>p&&m>x.from&&(m=x.from)}if(b)for(var S=0;S<b.length;S+=2)b[S+1]==m&&(u+=" "+b[S]);if(!h||h.from==p)for(var L=0;L<y.length;++L)ht(t,0,y[L]);if(h&&(h.from||0)==p){if(ht(t,(null==h.to?d+1:h.to)-p,h.marker,null==h.from),null==h.to)return;h.to==p&&(h=!1)}}if(p>=d)break;for(var k=Math.min(d,m);;){if(v){var T=p+v.length;if(!h){var M=T>k?v.slice(0,k-p):v;t.addToken(t,M,l?l+a:a,c,p+M.length==m?u:"",f,s)}if(T>=k){v=v.slice(k-p),p=k;break}p=T,c=""}v=i.slice(o,o=r[g++]),l=lt(r[g++],t.cm.options)}}else for(var N=1;N<r.length;N+=2)t.addToken(t,i.slice(o,o=r[N]),lt(r[N+1],t.cm.options))}function pt(e,t,r){this.line=t,this.rest=de(t),this.size=this.rest?W(g(this.rest))-r+1:1,this.node=this.text=null,this.hidden=ve(e,t)}function gt(e,t,r){for(var n,i=[],o=t;o<r;o=n){var l=new pt(e.doc,M(e.doc,o),o);n=o+l.size,i.push(l)}return i}function vt(e){gs?gs.ops.push(e):e.ownsGroup=gs={ops:[e],delayedCallbacks:[]}}function mt(e){var t=e.delayedCallbacks,r=0;do{for(;r<t.length;r++)t[r].call(null);for(var n=0;n<e.ops.length;n++){var i=e.ops[n];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(r<t.length)}function yt(e,t){var r=e.ownsGroup;if(r)try{mt(r)}finally{gs=null,t(r)}}function bt(e,t){var r=Le(e,t);if(r.length){var n,i=Array.prototype.slice.call(arguments,2);gs?n=gs.delayedCallbacks:vs?n=vs:(n=vs=[],setTimeout(wt,0));for(var o=0;o<r.length;++o)!function(e){n.push(function(){return r[e].apply(null,i)})}(o)}}function wt(){var e=vs;vs=null;for(var t=0;t<e.length;++t)e[t]()}function xt(e,t,r,n){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?kt(e,t):"gutter"==o?Mt(e,t,r,n):"class"==o?Tt(e,t):"widget"==o&&Nt(e,t,n)}t.changes=null}function Ct(e){return e.node==e.text&&(e.node=n("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),gl&&vl<8&&(e.node.style.zIndex=2)),e.node}function St(e,t){var r=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(r&&(r+=" CodeMirror-linebackground"),t.background)r?t.background.className=r:(t.background.parentNode.removeChild(t.background),t.background=null);else if(r){var i=Ct(t);t.background=i.insertBefore(n("div",null,r),i.firstChild),e.display.input.setUneditable(t.background)}}function Lt(e,t){var r=e.display.externalMeasured;return r&&r.line==t.line?(e.display.externalMeasured=null,t.measure=r.measure,r.built):st(e,t)}function kt(e,t){var r=t.text.className,n=Lt(e,t);t.text==t.node&&(t.node=n.pre),t.text.parentNode.replaceChild(n.pre,t.text),t.text=n.pre,n.bgClass!=t.bgClass||n.textClass!=t.textClass?(t.bgClass=n.bgClass,t.textClass=n.textClass,Tt(e,t)):r&&(t.text.className=r)}function Tt(e,t){St(e,t),t.line.wrapClass?Ct(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var r=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=r||""}function Mt(e,t,r,i){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var o=Ct(t);t.gutterBackground=n("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?i.fixedPos:-i.gutterTotalWidth)+"px; width: "+i.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),o.insertBefore(t.gutterBackground,t.text)}var l=t.line.gutterMarkers;if(e.options.lineNumbers||l){var s=Ct(t),a=t.gutter=n("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?i.fixedPos:-i.gutterTotalWidth)+"px");if(e.display.input.setUneditable(a),s.insertBefore(a,t.text),t.line.gutterClass&&(a.className+=" "+t.line.gutterClass),!e.options.lineNumbers||l&&l["CodeMirror-linenumbers"]||(t.lineNumber=a.appendChild(n("div",F(e.options,r),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+i.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),l)for(var u=0;u<e.options.gutters.length;++u){var c=e.options.gutters[u],f=l.hasOwnProperty(c)&&l[c];f&&a.appendChild(n("div",[f],"CodeMirror-gutter-elt","left: "+i.gutterLeft[c]+"px; width: "+i.gutterWidth[c]+"px"))}}}function Nt(e,t,r){t.alignable&&(t.alignable=null);for(var n=t.node.firstChild,i=void 0;n;n=i)i=n.nextSibling,"CodeMirror-linewidget"==n.className&&t.node.removeChild(n);At(e,t,r)}function Ot(e,t,r,n){var i=Lt(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),Tt(e,t),Mt(e,t,r,n),At(e,t,n),t.node}function At(e,t,r){if(Wt(e,t.line,t,r,!0),t.rest)for(var n=0;n<t.rest.length;n++)Wt(e,t.rest[n],t,r,!1)}function Wt(e,t,r,i,o){if(t.widgets)for(var l=Ct(r),s=0,a=t.widgets;s<a.length;++s){var u=a[s],c=n("div",[u.node],"CodeMirror-linewidget");u.handleMouseEvents||c.setAttribute("cm-ignore-events","true"),Dt(u,c,r,i),e.display.input.setUneditable(c),o&&u.above?l.insertBefore(c,r.gutter||r.text):l.appendChild(c),bt(u,"redraw")}}function Dt(e,t,r,n){if(e.noHScroll){(r.alignable||(r.alignable=[])).push(t);var i=n.wrapperWidth;t.style.left=n.fixedPos+"px",e.coverGutter||(i-=n.gutterTotalWidth,t.style.paddingLeft=n.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-n.gutterTotalWidth+"px"))}function Ht(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!o(document.body,e.node)){var i="position: relative;";e.coverGutter&&(i+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(i+="width: "+t.display.wrapper.clientWidth+"px;"),r(t.display.measure,n("div",[e.node],null,i))}return e.height=e.node.parentNode.offsetHeight}function Ft(e,t){for(var r=Pe(t);r!=e.wrapper;r=r.parentNode)if(!r||1==r.nodeType&&"true"==r.getAttribute("cm-ignore-events")||r.parentNode==e.sizer&&r!=e.mover)return!0}function Pt(e){return e.lineSpace.offsetTop}function Et(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function zt(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=r(e.measure,n("pre","x")),i=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,o={left:parseInt(i.paddingLeft),right:parseInt(i.paddingRight)};return isNaN(o.left)||isNaN(o.right)||(e.cachedPaddingH=o),o}function It(e){return Rl-e.display.nativeBarWidth}function Rt(e){return e.display.scroller.clientWidth-It(e)-e.display.barWidth}function Bt(e){return e.display.scroller.clientHeight-It(e)-e.display.barHeight}function Gt(e,t,r){var n=e.options.lineWrapping,i=n&&Rt(e);if(!t.measure.heights||n&&t.measure.width!=i){var o=t.measure.heights=[];if(n){t.measure.width=i;for(var l=t.text.firstChild.getClientRects(),s=0;s<l.length-1;s++){var a=l[s],u=l[s+1];Math.abs(a.bottom-u.bottom)>2&&o.push((a.bottom+u.top)/2-r.top)}}o.push(r.bottom-r.top)}}function Ut(e,t,r){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var n=0;n<e.rest.length;n++)if(e.rest[n]==t)return{map:e.measure.maps[n],cache:e.measure.caches[n]};for(var i=0;i<e.rest.length;i++)if(W(e.rest[i])>r)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}function Vt(e,t){var n=W(t=fe(t)),i=e.display.externalMeasured=new pt(e.doc,t,n);i.lineN=n;var o=i.built=st(e,i);return i.text=o.pre,r(e.display.lineMeasure,o.pre),i}function Kt(e,t,r,n){return Yt(e,Xt(e,t),r,n)}function jt(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[Lr(e,t)];var r=e.display.externalMeasured;return r&&t>=r.lineN&&t<r.lineN+r.size?r:void 0}function Xt(e,t){var r=W(t),n=jt(e,r);n&&!n.text?n=null:n&&n.changes&&(xt(e,n,r,br(e)),e.curOp.forceUpdate=!0),n||(n=Vt(e,t));var i=Ut(n,t,r);return{line:t,view:n,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Yt(e,t,r,n,i){t.before&&(r=-1);var o,l=r+(n||"");return t.cache.hasOwnProperty(l)?o=t.cache[l]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(Gt(e,t.view,t.rect),t.hasHeights=!0),(o=qt(e,t,r,n)).bogus||(t.cache[l]=o)),{left:o.left,right:o.right,top:i?o.rtop:o.top,bottom:i?o.rbottom:o.bottom}}function _t(e,t,r){for(var n,i,o,l,s,a,u=0;u<e.length;u+=3)if(s=e[u],a=e[u+1],t<s?(i=0,o=1,l="left"):t<a?o=(i=t-s)+1:(u==e.length-3||t==a&&e[u+3]>t)&&(i=(o=a-s)-1,t>=a&&(l="right")),null!=i){if(n=e[u+2],s==a&&r==(n.insertLeft?"left":"right")&&(l=r),"left"==r&&0==i)for(;u&&e[u-2]==e[u-3]&&e[u-1].insertLeft;)n=e[2+(u-=3)],l="left";if("right"==r&&i==a-s)for(;u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft;)n=e[(u+=3)+2],l="right";break}return{node:n,start:i,end:o,collapse:l,coverStart:s,coverEnd:a}}function $t(e,t){var r=ms;if("left"==t)for(var n=0;n<e.length&&(r=e[n]).left==r.right;n++);else for(var i=e.length-1;i>=0&&(r=e[i]).left==r.right;i--);return r}function qt(e,t,r,n){var i,o=_t(t.map,r,n),l=o.node,s=o.start,a=o.end,u=o.collapse;if(3==l.nodeType){for(var c=0;c<4;c++){for(;s&&S(t.line.text.charAt(o.coverStart+s));)--s;for(;o.coverStart+a<o.coverEnd&&S(t.line.text.charAt(o.coverStart+a));)++a;if((i=gl&&vl<9&&0==s&&a==o.coverEnd-o.coverStart?l.parentNode.getBoundingClientRect():$t(Wl(l,s,a).getClientRects(),n)).left||i.right||0==s)break;a=s,s-=1,u="right"}gl&&vl<11&&(i=Zt(e.display.measure,i))}else{s>0&&(u=n="right");var f;i=e.options.lineWrapping&&(f=l.getClientRects()).length>1?f["right"==n?f.length-1:0]:l.getBoundingClientRect()}if(gl&&vl<9&&!s&&(!i||!i.left&&!i.right)){var h=l.parentNode.getClientRects()[0];i=h?{left:h.left,right:h.left+yr(e.display),top:h.top,bottom:h.bottom}:ms}for(var d=i.top-t.rect.top,p=i.bottom-t.rect.top,g=(d+p)/2,v=t.view.measure.heights,m=0;m<v.length-1&&!(g<v[m]);m++);var y=m?v[m-1]:0,b=v[m],w={left:("right"==u?i.right:i.left)-t.rect.left,right:("left"==u?i.left:i.right)-t.rect.left,top:y,bottom:b};return i.left||i.right||(w.bogus=!0),e.options.singleCursorHeightPerLine||(w.rtop=d,w.rbottom=p),w}function Zt(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!Re(e))return t;var r=screen.logicalXDPI/screen.deviceXDPI,n=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*r,right:t.right*r,top:t.top*n,bottom:t.bottom*n}}function Qt(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function Jt(e){e.display.externalMeasure=null,t(e.display.lineMeasure);for(var r=0;r<e.display.view.length;r++)Qt(e.display.view[r])}function er(e){Jt(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function tr(){return bl&&kl?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function rr(){return bl&&kl?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function nr(e){var t=0;if(e.widgets)for(var r=0;r<e.widgets.length;++r)e.widgets[r].above&&(t+=Ht(e.widgets[r]));return t}function ir(e,t,r,n,i){if(!i){var o=nr(t);r.top+=o,r.bottom+=o}if("line"==n)return r;n||(n="local");var l=ye(t);if("local"==n?l+=Pt(e.display):l-=e.display.viewOffset,"page"==n||"window"==n){var s=e.display.lineSpace.getBoundingClientRect();l+=s.top+("window"==n?0:rr());var a=s.left+("window"==n?0:tr());r.left+=a,r.right+=a}return r.top+=l,r.bottom+=l,r}function or(e,t,r){if("div"==r)return t;var n=t.left,i=t.top;if("page"==r)n-=tr(),i-=rr();else if("local"==r||!r){var o=e.display.sizer.getBoundingClientRect();n+=o.left,i+=o.top}var l=e.display.lineSpace.getBoundingClientRect();return{left:n-l.left,top:i-l.top}}function lr(e,t,r,n,i){return n||(n=M(e.doc,t.line)),ir(e,n,Kt(e,n,t.ch,i),r)}function sr(e,t,r,n,i,o){function l(t,l){var s=Yt(e,i,t,l?"right":"left",o);return l?s.left=s.right:s.right=s.left,ir(e,n,s,r)}function s(e,t,r){var n=1==a[t].level;return l(r?e-1:e,n!=r)}n=n||M(e.doc,t.line),i||(i=Xt(e,n));var a=Se(n,e.doc.direction),u=t.ch,c=t.sticky;if(u>=n.text.length?(u=n.text.length,c="before"):u<=0&&(u=0,c="after"),!a)return l("before"==c?u-1:u,"before"==c);var f=Ce(a,u,c),h=$l,d=s(u,f,"before"==c);return null!=h&&(d.other=s(u,h,"before"!=c)),d}function ar(e,t){var r=0;t=U(e.doc,t),e.options.lineWrapping||(r=yr(e.display)*t.ch);var n=M(e.doc,t.line),i=ye(n)+Pt(e.display);return{left:r,right:r,top:i,bottom:i+n.height}}function ur(e,t,r,n,i){var o=P(e,t,r);return o.xRel=i,n&&(o.outside=!0),o}function cr(e,t,r){var n=e.doc;if((r+=e.display.viewOffset)<0)return ur(n.first,0,null,!0,-1);var i=D(n,r),o=n.first+n.size-1;if(i>o)return ur(n.first+n.size-1,M(n,o).text.length,null,!0,1);t<0&&(t=0);for(var l=M(n,i);;){var s=pr(e,l,i,t,r),a=ue(l),u=a&&a.find(0,!0);if(!a||!(s.ch>u.from.ch||s.ch==u.from.ch&&s.xRel>0))return s;i=W(l=u.to.line)}}function fr(e,t,r,n){n-=nr(t);var i=t.text.length,o=k(function(t){return Yt(e,r,t-1).bottom<=n},i,0);return i=k(function(t){return Yt(e,r,t).top>n},o,i),{begin:o,end:i}}function hr(e,t,r,n){return r||(r=Xt(e,t)),fr(e,t,r,ir(e,t,Yt(e,r,n),"line").top)}function dr(e,t,r,n){return!(e.bottom<=r)&&(e.top>r||(n?e.left:e.right)>t)}function pr(e,t,r,n,i){i-=ye(t);var o=Xt(e,t),l=nr(t),s=0,a=t.text.length,u=!0,c=Se(t,e.doc.direction);if(c){var f=(e.options.lineWrapping?vr:gr)(e,t,r,o,c,n,i);s=(u=1!=f.level)?f.from:f.to-1,a=u?f.to:f.from-1}var h,d,p=null,g=null,v=k(function(t){var r=Yt(e,o,t);return r.top+=l,r.bottom+=l,!!dr(r,n,i,!1)&&(r.top<=i&&r.left<=n&&(p=t,g=r),!0)},s,a),m=!1;if(g){var y=n-g.left<g.right-n,b=y==u;v=p+(b?0:1),d=b?"after":"before",h=y?g.left:g.right}else{u||v!=a&&v!=s||v++,d=0==v?"after":v==t.text.length?"before":Yt(e,o,v-(u?1:0)).bottom+l<=i==u?"after":"before";var w=sr(e,P(r,v,d),"line",t,o);h=w.left,m=i<w.top||i>=w.bottom}return v=L(t.text,v,1),ur(r,v,d,m,n-h)}function gr(e,t,r,n,i,o,l){var s=k(function(s){var a=i[s],u=1!=a.level;return dr(sr(e,P(r,u?a.to:a.from,u?"before":"after"),"line",t,n),o,l,!0)},0,i.length-1),a=i[s];if(s>0){var u=1!=a.level,c=sr(e,P(r,u?a.from:a.to,u?"after":"before"),"line",t,n);dr(c,o,l,!0)&&c.top>l&&(a=i[s-1])}return a}function vr(e,t,r,n,i,o,l){var s=fr(e,t,n,l),a=s.begin,u=s.end;/\s/.test(t.text.charAt(u-1))&&u--;for(var c=null,f=null,h=0;h<i.length;h++){var d=i[h];if(!(d.from>=u||d.to<=a)){var p=Yt(e,n,1!=d.level?Math.min(u,d.to)-1:Math.max(a,d.from)).right,g=p<o?o-p+1e9:p-o;(!c||f>g)&&(c=d,f=g)}}return c||(c=i[i.length-1]),c.from<a&&(c={from:a,to:c.to,level:c.level}),c.to>u&&(c={from:c.from,to:u,level:c.level}),c}function mr(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==hs){hs=n("pre");for(var i=0;i<49;++i)hs.appendChild(document.createTextNode("x")),hs.appendChild(n("br"));hs.appendChild(document.createTextNode("x"))}r(e.measure,hs);var o=hs.offsetHeight/50;return o>3&&(e.cachedTextHeight=o),t(e.measure),o||1}function yr(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=n("span","xxxxxxxxxx"),i=n("pre",[t]);r(e.measure,i);var o=t.getBoundingClientRect(),l=(o.right-o.left)/10;return l>2&&(e.cachedCharWidth=l),l||10}function br(e){for(var t=e.display,r={},n={},i=t.gutters.clientLeft,o=t.gutters.firstChild,l=0;o;o=o.nextSibling,++l)r[e.options.gutters[l]]=o.offsetLeft+o.clientLeft+i,n[e.options.gutters[l]]=o.clientWidth;return{fixedPos:wr(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:r,gutterWidth:n,wrapperWidth:t.wrapper.clientWidth}}function wr(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function xr(e){var t=mr(e.display),r=e.options.lineWrapping,n=r&&Math.max(5,e.display.scroller.clientWidth/yr(e.display)-3);return function(i){if(ve(e.doc,i))return 0;var o=0;if(i.widgets)for(var l=0;l<i.widgets.length;l++)i.widgets[l].height&&(o+=i.widgets[l].height);return r?o+(Math.ceil(i.text.length/n)||1)*t:o+t}}function Cr(e){var t=e.doc,r=xr(e);t.iter(function(e){var t=r(e);t!=e.height&&A(e,t)})}function Sr(e,t,r,n){var i=e.display;if(!r&&"true"==Pe(t).getAttribute("cm-not-content"))return null;var o,l,s=i.lineSpace.getBoundingClientRect();try{o=t.clientX-s.left,l=t.clientY-s.top}catch(t){return null}var a,u=cr(e,o,l);if(n&&1==u.xRel&&(a=M(e.doc,u.line).text).length==u.ch){var c=f(a,a.length,e.options.tabSize)-a.length;u=P(u.line,Math.max(0,Math.round((o-zt(e.display).left)/yr(e.display))-c))}return u}function Lr(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var r=e.display.view,n=0;n<r.length;n++)if((t-=r[n].size)<0)return n}function kr(e){e.display.input.showSelection(e.display.input.prepareSelection())}function Tr(e,t){void 0===t&&(t=!0);for(var r=e.doc,n={},i=n.cursors=document.createDocumentFragment(),o=n.selection=document.createDocumentFragment(),l=0;l<r.sel.ranges.length;l++)if(t||l!=r.sel.primIndex){var s=r.sel.ranges[l];if(!(s.from().line>=e.display.viewTo||s.to().line<e.display.viewFrom)){var a=s.empty();(a||e.options.showCursorWhenSelecting)&&Mr(e,s.head,i),a||Or(e,s,o)}}return n}function Mr(e,t,r){var i=sr(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),o=r.appendChild(n("div"," ","CodeMirror-cursor"));if(o.style.left=i.left+"px",o.style.top=i.top+"px",o.style.height=Math.max(0,i.bottom-i.top)*e.options.cursorHeight+"px",i.other){var l=r.appendChild(n("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));l.style.display="",l.style.left=i.other.left+"px",l.style.top=i.other.top+"px",l.style.height=.85*(i.other.bottom-i.other.top)+"px"}}function Nr(e,t){return e.top-t.top||e.left-t.left}function Or(e,t,r){function i(e,t,r,i){t<0&&(t=0),t=Math.round(t),i=Math.round(i),a.appendChild(n("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==r?f-e:r)+"px;\n                             height: "+(i-t)+"px"))}function o(t,r,n){function o(r,n){return lr(e,P(t,r),"div",d,n)}function l(t,r,n){var i=hr(e,d,null,t),l="ltr"==r==("after"==n)?"left":"right";return o("after"==n?i.begin:i.end-(/\s/.test(d.text.charAt(i.end-1))?2:1),l)[l]}var a,u,d=M(s,t),p=d.text.length,g=Se(d,s.direction);return xe(g,r||0,null==n?p:n,function(e,t,s,d){var v="ltr"==s,m=o(e,v?"left":"right"),y=o(t-1,v?"right":"left"),b=null==r&&0==e,w=null==n&&t==p,x=0==d,C=!g||d==g.length-1;if(y.top-m.top<=3){var S=(h?w:b)&&C,L=(h?b:w)&&x?c:(v?m:y).left,k=S?f:(v?y:m).right;i(L,m.top,k-L,m.bottom)}else{var T,M,N,O;v?(T=h&&b&&x?c:m.left,M=h?f:l(e,s,"before"),N=h?c:l(t,s,"after"),O=h&&w&&C?f:y.right):(T=h?l(e,s,"before"):c,M=!h&&b&&x?f:m.right,N=!h&&w&&C?c:y.left,O=h?l(t,s,"after"):f),i(T,m.top,M-T,m.bottom),m.bottom<y.top&&i(c,m.bottom,null,y.top),i(N,y.top,O-N,y.bottom)}(!a||Nr(m,a)<0)&&(a=m),Nr(y,a)<0&&(a=y),(!u||Nr(m,u)<0)&&(u=m),Nr(y,u)<0&&(u=y)}),{start:a,end:u}}var l=e.display,s=e.doc,a=document.createDocumentFragment(),u=zt(e.display),c=u.left,f=Math.max(l.sizerWidth,Rt(e)-l.sizer.offsetLeft)-u.right,h="ltr"==s.direction,d=t.from(),p=t.to();if(d.line==p.line)o(d.line,d.ch,p.ch);else{var g=M(s,d.line),v=M(s,p.line),m=fe(g)==fe(v),y=o(d.line,d.ch,m?g.text.length+1:null).end,b=o(p.line,m?0:null,p.ch).start;m&&(y.top<b.top-2?(i(y.right,y.top,null,y.bottom),i(c,b.top,b.left,b.bottom)):i(y.right,y.top,b.left-y.right,y.bottom)),y.bottom<b.top&&i(c,y.bottom,null,b.top)}r.appendChild(a)}function Ar(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var r=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval(function(){return t.cursorDiv.style.visibility=(r=!r)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function Wr(e){e.state.focused||(e.display.input.focus(),Hr(e))}function Dr(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,Fr(e))},100)}function Hr(e,t){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(Te(e,"focus",e,t),e.state.focused=!0,s(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),ml&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),Ar(e))}function Fr(e,t){e.state.delayingBlurEvent||(e.state.focused&&(Te(e,"blur",e,t),e.state.focused=!1,Fl(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function Pr(e){for(var t=e.display,r=t.lineDiv.offsetTop,n=0;n<t.view.length;n++){var i=t.view[n],o=void 0;if(!i.hidden){if(gl&&vl<8){var l=i.node.offsetTop+i.node.offsetHeight;o=l-r,r=l}else{var s=i.node.getBoundingClientRect();o=s.bottom-s.top}var a=i.line.height-o;if(o<2&&(o=mr(t)),(a>.005||a<-.005)&&(A(i.line,o),Er(i.line),i.rest))for(var u=0;u<i.rest.length;u++)Er(i.rest[u])}}}function Er(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t)e.widgets[t].height=e.widgets[t].node.parentNode.offsetHeight}function zr(e,t,r){var n=r&&null!=r.top?Math.max(0,r.top):e.scroller.scrollTop;n=Math.floor(n-Pt(e));var i=r&&null!=r.bottom?r.bottom:n+e.wrapper.clientHeight,o=D(t,n),l=D(t,i);if(r&&r.ensure){var s=r.ensure.from.line,a=r.ensure.to.line;s<o?(o=s,l=D(t,ye(M(t,s))+e.wrapper.clientHeight)):Math.min(a,t.lastLine())>=l&&(o=D(t,ye(M(t,a))-e.wrapper.clientHeight),l=a)}return{from:o,to:Math.max(l,o+1)}}function Ir(e){var t=e.display,r=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var n=wr(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=n+"px",l=0;l<r.length;l++)if(!r[l].hidden){e.options.fixedGutter&&(r[l].gutter&&(r[l].gutter.style.left=o),r[l].gutterBackground&&(r[l].gutterBackground.style.left=o));var s=r[l].alignable;if(s)for(var a=0;a<s.length;a++)s[a].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=n+i+"px")}}function Rr(e){if(!e.options.lineNumbers)return!1;var t=e.doc,r=F(e.options,t.first+t.size-1),i=e.display;if(r.length!=i.lineNumChars){var o=i.measure.appendChild(n("div",[n("div",r)],"CodeMirror-linenumber CodeMirror-gutter-elt")),l=o.firstChild.offsetWidth,s=o.offsetWidth-l;return i.lineGutter.style.width="",i.lineNumInnerWidth=Math.max(l,i.lineGutter.offsetWidth-s)+1,i.lineNumWidth=i.lineNumInnerWidth+s,i.lineNumChars=i.lineNumInnerWidth?r.length:-1,i.lineGutter.style.width=i.lineNumWidth+"px",Wn(e),!0}return!1}function Br(e,t){if(!Me(e,"scrollCursorIntoView")){var r=e.display,i=r.sizer.getBoundingClientRect(),o=null;if(t.top+i.top<0?o=!0:t.bottom+i.top>(window.innerHeight||document.documentElement.clientHeight)&&(o=!1),null!=o&&!Sl){var l=n("div","​",null,"position: absolute;\n                         top: "+(t.top-r.viewOffset-Pt(e.display))+"px;\n                         height: "+(t.bottom-t.top+It(e)+r.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(l),l.scrollIntoView(o),e.display.lineSpace.removeChild(l)}}}function Gr(e,t,r,n){null==n&&(n=0);var i;e.options.lineWrapping||t!=r||(r="before"==(t=t.ch?P(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t).sticky?P(t.line,t.ch+1,"before"):t);for(var o=0;o<5;o++){var l=!1,s=sr(e,t),a=r&&r!=t?sr(e,r):s,u=Vr(e,i={left:Math.min(s.left,a.left),top:Math.min(s.top,a.top)-n,right:Math.max(s.left,a.left),bottom:Math.max(s.bottom,a.bottom)+n}),c=e.doc.scrollTop,f=e.doc.scrollLeft;if(null!=u.scrollTop&&(qr(e,u.scrollTop),Math.abs(e.doc.scrollTop-c)>1&&(l=!0)),null!=u.scrollLeft&&(Qr(e,u.scrollLeft),Math.abs(e.doc.scrollLeft-f)>1&&(l=!0)),!l)break}return i}function Ur(e,t){var r=Vr(e,t);null!=r.scrollTop&&qr(e,r.scrollTop),null!=r.scrollLeft&&Qr(e,r.scrollLeft)}function Vr(e,t){var r=e.display,n=mr(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:r.scroller.scrollTop,o=Bt(e),l={};t.bottom-t.top>o&&(t.bottom=t.top+o);var s=e.doc.height+Et(r),a=t.top<n,u=t.bottom>s-n;if(t.top<i)l.scrollTop=a?0:t.top;else if(t.bottom>i+o){var c=Math.min(t.top,(u?s:t.bottom)-o);c!=i&&(l.scrollTop=c)}var f=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:r.scroller.scrollLeft,h=Rt(e)-(e.options.fixedGutter?r.gutters.offsetWidth:0),d=t.right-t.left>h;return d&&(t.right=t.left+h),t.left<10?l.scrollLeft=0:t.left<f?l.scrollLeft=Math.max(0,t.left-(d?0:10)):t.right>h+f-3&&(l.scrollLeft=t.right+(d?0:10)-h),l}function Kr(e,t){null!=t&&(_r(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function jr(e){_r(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function Xr(e,t,r){null==t&&null==r||_r(e),null!=t&&(e.curOp.scrollLeft=t),null!=r&&(e.curOp.scrollTop=r)}function Yr(e,t){_r(e),e.curOp.scrollToPos=t}function _r(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,$r(e,ar(e,t.from),ar(e,t.to),t.margin))}function $r(e,t,r,n){var i=Vr(e,{left:Math.min(t.left,r.left),top:Math.min(t.top,r.top)-n,right:Math.max(t.right,r.right),bottom:Math.max(t.bottom,r.bottom)+n});Xr(e,i.scrollLeft,i.scrollTop)}function qr(e,t){Math.abs(e.doc.scrollTop-t)<2||(fl||On(e,{top:t}),Zr(e,t,!0),fl&&On(e),Cn(e,100))}function Zr(e,t,r){t=Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t),(e.display.scroller.scrollTop!=t||r)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function Qr(e,t,r,n){t=Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth),(r?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!n||(e.doc.scrollLeft=t,Ir(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function Jr(e){var t=e.display,r=t.gutters.offsetWidth,n=Math.round(e.doc.height+Et(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?r:0,docHeight:n,scrollHeight:n+It(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:r}}function en(e,t){t||(t=Jr(e));var r=e.display.barWidth,n=e.display.barHeight;tn(e,t);for(var i=0;i<4&&r!=e.display.barWidth||n!=e.display.barHeight;i++)r!=e.display.barWidth&&e.options.lineWrapping&&Pr(e),tn(e,Jr(e)),r=e.display.barWidth,n=e.display.barHeight}function tn(e,t){var r=e.display,n=r.scrollbars.update(t);r.sizer.style.paddingRight=(r.barWidth=n.right)+"px",r.sizer.style.paddingBottom=(r.barHeight=n.bottom)+"px",r.heightForcer.style.borderBottom=n.bottom+"px solid transparent",n.right&&n.bottom?(r.scrollbarFiller.style.display="block",r.scrollbarFiller.style.height=n.bottom+"px",r.scrollbarFiller.style.width=n.right+"px"):r.scrollbarFiller.style.display="",n.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(r.gutterFiller.style.display="block",r.gutterFiller.style.height=n.bottom+"px",r.gutterFiller.style.width=t.gutterWidth+"px"):r.gutterFiller.style.display=""}function rn(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&Fl(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new ws[e.options.scrollbarStyle](function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),Ql(t,"mousedown",function(){e.state.focused&&setTimeout(function(){return e.display.input.focus()},0)}),t.setAttribute("cm-not-content","true")},function(t,r){"horizontal"==r?Qr(e,t):qr(e,t)},e),e.display.scrollbars.addClass&&s(e.display.wrapper,e.display.scrollbars.addClass)}function nn(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:null,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++xs},vt(e.curOp)}function on(e){yt(e.curOp,function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;ln(e)})}function ln(e){for(var t=e.ops,r=0;r<t.length;r++)sn(t[r]);for(var n=0;n<t.length;n++)an(t[n]);for(var i=0;i<t.length;i++)un(t[i]);for(var o=0;o<t.length;o++)cn(t[o]);for(var l=0;l<t.length;l++)fn(t[l])}function sn(e){var t=e.cm,r=t.display;Ln(t),e.updateMaxLine&&we(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<r.viewFrom||e.scrollToPos.to.line>=r.viewTo)||r.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new Cs(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function an(e){e.updatedDisplay=e.mustUpdate&&Mn(e.cm,e.update)}function un(e){var t=e.cm,r=t.display;e.updatedDisplay&&Pr(t),e.barMeasure=Jr(t),r.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=Kt(t,r.maxLine,r.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(r.scroller.clientWidth,r.sizer.offsetLeft+e.adjustWidthTo+It(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,r.sizer.offsetLeft+e.adjustWidthTo-Rt(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=r.input.prepareSelection())}function cn(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&Qr(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var r=e.focus&&e.focus==l();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,r),(e.updatedDisplay||e.startHeight!=t.doc.height)&&en(t,e.barMeasure),e.updatedDisplay&&Dn(t,e.barMeasure),e.selectionChanged&&Ar(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),r&&Wr(e.cm)}function fn(e){var t=e.cm,r=t.display,n=t.doc;e.updatedDisplay&&Nn(t,e.update),null==r.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(r.wheelStartX=r.wheelStartY=null),null!=e.scrollTop&&Zr(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&Qr(t,e.scrollLeft,!0,!0),e.scrollToPos&&Br(t,Gr(t,U(n,e.scrollToPos.from),U(n,e.scrollToPos.to),e.scrollToPos.margin));var i=e.maybeHiddenMarkers,o=e.maybeUnhiddenMarkers;if(i)for(var l=0;l<i.length;++l)i[l].lines.length||Te(i[l],"hide");if(o)for(var s=0;s<o.length;++s)o[s].lines.length&&Te(o[s],"unhide");r.wrapper.offsetHeight&&(n.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&Te(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function hn(e,t){if(e.curOp)return t();nn(e);try{return t()}finally{on(e)}}function dn(e,t){return function(){if(e.curOp)return t.apply(e,arguments);nn(e);try{return t.apply(e,arguments)}finally{on(e)}}}function pn(e){return function(){if(this.curOp)return e.apply(this,arguments);nn(this);try{return e.apply(this,arguments)}finally{on(this)}}}function gn(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);nn(t);try{return e.apply(this,arguments)}finally{on(t)}}}function vn(e,t,r,n){null==t&&(t=e.doc.first),null==r&&(r=e.doc.first+e.doc.size),n||(n=0);var i=e.display;if(n&&r<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)_l&&pe(e.doc,t)<i.viewTo&&yn(e);else if(r<=i.viewFrom)_l&&ge(e.doc,r+n)>i.viewFrom?yn(e):(i.viewFrom+=n,i.viewTo+=n);else if(t<=i.viewFrom&&r>=i.viewTo)yn(e);else if(t<=i.viewFrom){var o=bn(e,r,r+n,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=n):yn(e)}else if(r>=i.viewTo){var l=bn(e,t,t,-1);l?(i.view=i.view.slice(0,l.index),i.viewTo=l.lineN):yn(e)}else{var s=bn(e,t,t,-1),a=bn(e,r,r+n,1);s&&a?(i.view=i.view.slice(0,s.index).concat(gt(e,s.lineN,a.lineN)).concat(i.view.slice(a.index)),i.viewTo+=n):yn(e)}var u=i.externalMeasured;u&&(r<u.lineN?u.lineN+=n:t<u.lineN+u.size&&(i.externalMeasured=null))}function mn(e,t,r){e.curOp.viewChanged=!0;var n=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(n.externalMeasured=null),!(t<n.viewFrom||t>=n.viewTo)){var o=n.view[Lr(e,t)];if(null!=o.node){var l=o.changes||(o.changes=[]);-1==h(l,r)&&l.push(r)}}}function yn(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function bn(e,t,r,n){var i,o=Lr(e,t),l=e.display.view;if(!_l||r==e.doc.first+e.doc.size)return{index:o,lineN:r};for(var s=e.display.viewFrom,a=0;a<o;a++)s+=l[a].size;if(s!=t){if(n>0){if(o==l.length-1)return null;i=s+l[o].size-t,o++}else i=s-t;t+=i,r+=i}for(;pe(e.doc,r)!=r;){if(o==(n<0?0:l.length-1))return null;r+=n*l[o-(n<0?1:0)].size,o+=n}return{index:o,lineN:r}}function wn(e,t,r){var n=e.display;0==n.view.length||t>=n.viewTo||r<=n.viewFrom?(n.view=gt(e,t,r),n.viewFrom=t):(n.viewFrom>t?n.view=gt(e,t,n.viewFrom).concat(n.view):n.viewFrom<t&&(n.view=n.view.slice(Lr(e,t))),n.viewFrom=t,n.viewTo<r?n.view=n.view.concat(gt(e,n.viewTo,r)):n.viewTo>r&&(n.view=n.view.slice(0,Lr(e,r)))),n.viewTo=r}function xn(e){for(var t=e.display.view,r=0,n=0;n<t.length;n++){var i=t[n];i.hidden||i.node&&!i.changes||++r}return r}function Cn(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,u(Sn,e))}function Sn(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var r=+new Date+e.options.workTime,n=$e(e,t.highlightFrontier),i=[];t.iter(n.line,Math.min(t.first+t.size,e.display.viewTo+500),function(o){if(n.line>=e.display.viewFrom){var l=o.styles,s=o.text.length>e.options.maxHighlightLength?Ke(t.mode,n.state):null,a=Ye(e,o,n,!0);s&&(n.state=s),o.styles=a.styles;var u=o.styleClasses,c=a.classes;c?o.styleClasses=c:u&&(o.styleClasses=null);for(var f=!l||l.length!=o.styles.length||u!=c&&(!u||!c||u.bgClass!=c.bgClass||u.textClass!=c.textClass),h=0;!f&&h<l.length;++h)f=l[h]!=o.styles[h];f&&i.push(n.line),o.stateAfter=n.save(),n.nextLine()}else o.text.length<=e.options.maxHighlightLength&&qe(e,o.text,n),o.stateAfter=n.line%5==0?n.save():null,n.nextLine();if(+new Date>r)return Cn(e,e.options.workDelay),!0}),t.highlightFrontier=n.line,t.modeFrontier=Math.max(t.modeFrontier,n.line),i.length&&hn(e,function(){for(var t=0;t<i.length;t++)mn(e,i[t],"text")})}}function Ln(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=It(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=It(e)+"px",t.scrollbarsClipped=!0)}function kn(e){if(e.hasFocus())return null;var t=l();if(!t||!o(e.display.lineDiv,t))return null;var r={activeElt:t};if(window.getSelection){var n=window.getSelection();n.anchorNode&&n.extend&&o(e.display.lineDiv,n.anchorNode)&&(r.anchorNode=n.anchorNode,r.anchorOffset=n.anchorOffset,r.focusNode=n.focusNode,r.focusOffset=n.focusOffset)}return r}function Tn(e){if(e&&e.activeElt&&e.activeElt!=l()&&(e.activeElt.focus(),e.anchorNode&&o(document.body,e.anchorNode)&&o(document.body,e.focusNode))){var t=window.getSelection(),r=document.createRange();r.setEnd(e.anchorNode,e.anchorOffset),r.collapse(!1),t.removeAllRanges(),t.addRange(r),t.extend(e.focusNode,e.focusOffset)}}function Mn(e,r){var n=e.display,i=e.doc;if(r.editorIsHidden)return yn(e),!1;if(!r.force&&r.visible.from>=n.viewFrom&&r.visible.to<=n.viewTo&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo)&&n.renderedView==n.view&&0==xn(e))return!1;Rr(e)&&(yn(e),r.dims=br(e));var o=i.first+i.size,l=Math.max(r.visible.from-e.options.viewportMargin,i.first),s=Math.min(o,r.visible.to+e.options.viewportMargin);n.viewFrom<l&&l-n.viewFrom<20&&(l=Math.max(i.first,n.viewFrom)),n.viewTo>s&&n.viewTo-s<20&&(s=Math.min(o,n.viewTo)),_l&&(l=pe(e.doc,l),s=ge(e.doc,s));var a=l!=n.viewFrom||s!=n.viewTo||n.lastWrapHeight!=r.wrapperHeight||n.lastWrapWidth!=r.wrapperWidth;wn(e,l,s),n.viewOffset=ye(M(e.doc,n.viewFrom)),e.display.mover.style.top=n.viewOffset+"px";var u=xn(e);if(!a&&0==u&&!r.force&&n.renderedView==n.view&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo))return!1;var c=kn(e);return u>4&&(n.lineDiv.style.display="none"),An(e,n.updateLineNumbers,r.dims),u>4&&(n.lineDiv.style.display=""),n.renderedView=n.view,Tn(c),t(n.cursorDiv),t(n.selectionDiv),n.gutters.style.height=n.sizer.style.minHeight=0,a&&(n.lastWrapHeight=r.wrapperHeight,n.lastWrapWidth=r.wrapperWidth,Cn(e,400)),n.updateLineNumbers=null,!0}function Nn(e,t){for(var r=t.viewport,n=!0;(n&&e.options.lineWrapping&&t.oldDisplayWidth!=Rt(e)||(r&&null!=r.top&&(r={top:Math.min(e.doc.height+Et(e.display)-Bt(e),r.top)}),t.visible=zr(e.display,e.doc,r),!(t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)))&&Mn(e,t);n=!1){Pr(e);var i=Jr(e);kr(e),en(e,i),Dn(e,i),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function On(e,t){var r=new Cs(e,t);if(Mn(e,r)){Pr(e),Nn(e,r);var n=Jr(e);kr(e),en(e,n),Dn(e,n),r.finish()}}function An(e,r,n){function i(t){var r=t.nextSibling;return ml&&Ml&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),r}for(var o=e.display,l=e.options.lineNumbers,s=o.lineDiv,a=s.firstChild,u=o.view,c=o.viewFrom,f=0;f<u.length;f++){var d=u[f];if(d.hidden);else if(d.node&&d.node.parentNode==s){for(;a!=d.node;)a=i(a);var p=l&&null!=r&&r<=c&&d.lineNumber;d.changes&&(h(d.changes,"gutter")>-1&&(p=!1),xt(e,d,c,n)),p&&(t(d.lineNumber),d.lineNumber.appendChild(document.createTextNode(F(e.options,c)))),a=d.node.nextSibling}else{var g=Ot(e,d,c,n);s.insertBefore(g,a)}c+=d.size}for(;a;)a=i(a)}function Wn(e){var t=e.display.gutters.offsetWidth;e.display.sizer.style.marginLeft=t+"px"}function Dn(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+It(e)+"px"}function Hn(e){var r=e.display.gutters,i=e.options.gutters;t(r);for(var o=0;o<i.length;++o){var l=i[o],s=r.appendChild(n("div",null,"CodeMirror-gutter "+l));"CodeMirror-linenumbers"==l&&(e.display.lineGutter=s,s.style.width=(e.display.lineNumWidth||1)+"px")}r.style.display=o?"":"none",Wn(e)}function Fn(e){var t=h(e.gutters,"CodeMirror-linenumbers");-1==t&&e.lineNumbers?e.gutters=e.gutters.concat(["CodeMirror-linenumbers"]):t>-1&&!e.lineNumbers&&(e.gutters=e.gutters.slice(0),e.gutters.splice(t,1))}function Pn(e){var t=e.wheelDeltaX,r=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==r&&e.detail&&e.axis==e.VERTICAL_AXIS?r=e.detail:null==r&&(r=e.wheelDelta),{x:t,y:r}}function En(e){var t=Pn(e);return t.x*=Ls,t.y*=Ls,t}function zn(e,t){var r=Pn(t),n=r.x,i=r.y,o=e.display,l=o.scroller,s=l.scrollWidth>l.clientWidth,a=l.scrollHeight>l.clientHeight;if(n&&s||i&&a){if(i&&Ml&&ml)e:for(var u=t.target,c=o.view;u!=l;u=u.parentNode)for(var f=0;f<c.length;f++)if(c[f].node==u){e.display.currentWheelTarget=u;break e}if(n&&!fl&&!wl&&null!=Ls)return i&&a&&qr(e,Math.max(0,l.scrollTop+i*Ls)),Qr(e,Math.max(0,l.scrollLeft+n*Ls)),(!i||i&&a)&&We(t),void(o.wheelStartX=null);if(i&&null!=Ls){var h=i*Ls,d=e.doc.scrollTop,p=d+o.wrapper.clientHeight;h<0?d=Math.max(0,d+h-50):p=Math.min(e.doc.height,p+h+50),On(e,{top:d,bottom:p})}Ss<20&&(null==o.wheelStartX?(o.wheelStartX=l.scrollLeft,o.wheelStartY=l.scrollTop,o.wheelDX=n,o.wheelDY=i,setTimeout(function(){if(null!=o.wheelStartX){var e=l.scrollLeft-o.wheelStartX,t=l.scrollTop-o.wheelStartY,r=t&&o.wheelDY&&t/o.wheelDY||e&&o.wheelDX&&e/o.wheelDX;o.wheelStartX=o.wheelStartY=null,r&&(Ls=(Ls*Ss+r)/(Ss+1),++Ss)}},200)):(o.wheelDX+=n,o.wheelDY+=i))}}function In(e,t){var r=e[t];e.sort(function(e,t){return E(e.from(),t.from())}),t=h(e,r);for(var n=1;n<e.length;n++){var i=e[n],o=e[n-1];if(E(o.to(),i.from())>=0){var l=B(o.from(),i.from()),s=R(o.to(),i.to()),a=o.empty()?i.from()==i.head:o.from()==o.head;n<=t&&--t,e.splice(--n,2,new Ts(a?s:l,a?l:s))}}return new ks(e,t)}function Rn(e,t){return new ks([new Ts(e,t||e)],0)}function Bn(e){return e.text?P(e.from.line+e.text.length-1,g(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function Gn(e,t){if(E(e,t.from)<0)return e;if(E(e,t.to)<=0)return Bn(t);var r=e.line+t.text.length-(t.to.line-t.from.line)-1,n=e.ch;return e.line==t.to.line&&(n+=Bn(t).ch-t.to.ch),P(r,n)}function Un(e,t){for(var r=[],n=0;n<e.sel.ranges.length;n++){var i=e.sel.ranges[n];r.push(new Ts(Gn(i.anchor,t),Gn(i.head,t)))}return In(r,e.sel.primIndex)}function Vn(e,t,r){return e.line==t.line?P(r.line,e.ch-t.ch+r.ch):P(r.line+(e.line-t.line),e.ch)}function Kn(e,t,r){for(var n=[],i=P(e.first,0),o=i,l=0;l<t.length;l++){var s=t[l],a=Vn(s.from,i,o),u=Vn(Bn(s),i,o);if(i=s.to,o=u,"around"==r){var c=e.sel.ranges[l],f=E(c.head,c.anchor)<0;n[l]=new Ts(f?u:a,f?a:u)}else n[l]=new Ts(a,a)}return new ks(n,e.sel.primIndex)}function jn(e){e.doc.mode=Ue(e.options,e.doc.modeOption),Xn(e)}function Xn(e){e.doc.iter(function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,Cn(e,100),e.state.modeGen++,e.curOp&&vn(e)}function Yn(e,t){return 0==t.from.ch&&0==t.to.ch&&""==g(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function _n(e,t,r,n){function i(e){return r?r[e]:null}function o(e,r,i){it(e,r,i,n),bt(e,"change",e,t)}function l(e,t){for(var r=[],o=e;o<t;++o)r.push(new fs(u[o],i(o),n));return r}var s=t.from,a=t.to,u=t.text,c=M(e,s.line),f=M(e,a.line),h=g(u),d=i(u.length-1),p=a.line-s.line;if(t.full)e.insert(0,l(0,u.length)),e.remove(u.length,e.size-u.length);else if(Yn(e,t)){var v=l(0,u.length-1);o(f,f.text,d),p&&e.remove(s.line,p),v.length&&e.insert(s.line,v)}else if(c==f)if(1==u.length)o(c,c.text.slice(0,s.ch)+h+c.text.slice(a.ch),d);else{var m=l(1,u.length-1);m.push(new fs(h+c.text.slice(a.ch),d,n)),o(c,c.text.slice(0,s.ch)+u[0],i(0)),e.insert(s.line+1,m)}else if(1==u.length)o(c,c.text.slice(0,s.ch)+u[0]+f.text.slice(a.ch),i(0)),e.remove(s.line+1,p);else{o(c,c.text.slice(0,s.ch)+u[0],i(0)),o(f,h+f.text.slice(a.ch),d);var y=l(1,u.length-1);p>1&&e.remove(s.line+1,p-1),e.insert(s.line+1,y)}bt(e,"change",e,t)}function $n(e,t,r){function n(e,i,o){if(e.linked)for(var l=0;l<e.linked.length;++l){var s=e.linked[l];if(s.doc!=i){var a=o&&s.sharedHist;r&&!a||(t(s.doc,a),n(s.doc,e,a))}}}n(e,null,!0)}function qn(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,Cr(e),jn(e),Zn(e),e.options.lineWrapping||we(e),e.options.mode=t.modeOption,vn(e)}function Zn(e){("rtl"==e.doc.direction?s:Fl)(e.display.lineDiv,"CodeMirror-rtl")}function Qn(e){hn(e,function(){Zn(e),vn(e)})}function Jn(e){this.done=[],this.undone=[],this.undoDepth=1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e||1}function ei(e,t){var r={from:I(t.from),to:Bn(t),text:N(e,t.from,t.to)};return si(e,r,t.from.line,t.to.line+1),$n(e,function(e){return si(e,r,t.from.line,t.to.line+1)},!0),r}function ti(e){for(;e.length&&g(e).ranges;)e.pop()}function ri(e,t){return t?(ti(e.done),g(e.done)):e.done.length&&!g(e.done).ranges?g(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),g(e.done)):void 0}function ni(e,t,r,n){var i=e.history;i.undone.length=0;var o,l,s=+new Date;if((i.lastOp==n||i.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&e.cm&&i.lastModTime>s-e.cm.options.historyEventDelay||"*"==t.origin.charAt(0)))&&(o=ri(i,i.lastOp==n)))l=g(o.changes),0==E(t.from,t.to)&&0==E(t.from,l.to)?l.to=Bn(t):o.changes.push(ei(e,t));else{var a=g(i.done);for(a&&a.ranges||li(e.sel,i.done),o={changes:[ei(e,t)],generation:i.generation},i.done.push(o);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(r),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=s,i.lastOp=i.lastSelOp=n,i.lastOrigin=i.lastSelOrigin=t.origin,l||Te(e,"historyAdded")}function ii(e,t,r,n){var i=t.charAt(0);return"*"==i||"+"==i&&r.ranges.length==n.ranges.length&&r.somethingSelected()==n.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function oi(e,t,r,n){var i=e.history,o=n&&n.origin;r==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||ii(e,o,g(i.done),t))?i.done[i.done.length-1]=t:li(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=r,n&&!1!==n.clearRedo&&ti(i.undone)}function li(e,t){var r=g(t);r&&r.ranges&&r.equals(e)||t.push(e)}function si(e,t,r,n){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,r),Math.min(e.first+e.size,n),function(r){r.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=r.markedSpans),++o})}function ai(e){if(!e)return null;for(var t,r=0;r<e.length;++r)e[r].marker.explicitlyCleared?t||(t=e.slice(0,r)):t&&t.push(e[r]);return t?t.length?t:null:e}function ui(e,t){var r=t["spans_"+e.id];if(!r)return null;for(var n=[],i=0;i<t.text.length;++i)n.push(ai(r[i]));return n}function ci(e,t){var r=ui(e,t),n=J(e,t);if(!r)return n;if(!n)return r;for(var i=0;i<r.length;++i){var o=r[i],l=n[i];if(o&&l)e:for(var s=0;s<l.length;++s){for(var a=l[s],u=0;u<o.length;++u)if(o[u].marker==a.marker)continue e;o.push(a)}else l&&(r[i]=l)}return r}function fi(e,t,r){for(var n=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges)n.push(r?ks.prototype.deepCopy.call(o):o);else{var l=o.changes,s=[];n.push({changes:s});for(var a=0;a<l.length;++a){var u=l[a],c=void 0;if(s.push({from:u.from,to:u.to,text:u.text}),t)for(var f in u)(c=f.match(/^spans_(\d+)$/))&&h(t,Number(c[1]))>-1&&(g(s)[f]=u[f],delete u[f])}}}return n}function hi(e,t,r,n){if(n){var i=e.anchor;if(r){var o=E(t,i)<0;o!=E(r,i)<0?(i=t,t=r):o!=E(t,r)<0&&(t=r)}return new Ts(i,t)}return new Ts(r||t,t)}function di(e,t,r,n,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),bi(e,new ks([hi(e.sel.primary(),t,r,i)],0),n)}function pi(e,t,r){for(var n=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)n[o]=hi(e.sel.ranges[o],t[o],null,i);bi(e,In(n,e.sel.primIndex),r)}function gi(e,t,r,n){var i=e.sel.ranges.slice(0);i[t]=r,bi(e,In(i,e.sel.primIndex),n)}function vi(e,t,r,n){bi(e,Rn(t,r),n)}function mi(e,t,r){var n={ranges:t.ranges,update:function(t){var r=this;this.ranges=[];for(var n=0;n<t.length;n++)r.ranges[n]=new Ts(U(e,t[n].anchor),U(e,t[n].head))},origin:r&&r.origin};return Te(e,"beforeSelectionChange",e,n),e.cm&&Te(e.cm,"beforeSelectionChange",e.cm,n),n.ranges!=t.ranges?In(n.ranges,n.ranges.length-1):t}function yi(e,t,r){var n=e.history.done,i=g(n);i&&i.ranges?(n[n.length-1]=t,wi(e,t,r)):bi(e,t,r)}function bi(e,t,r){wi(e,t,r),oi(e,e.sel,e.cm?e.cm.curOp.id:NaN,r)}function wi(e,t,r){(Oe(e,"beforeSelectionChange")||e.cm&&Oe(e.cm,"beforeSelectionChange"))&&(t=mi(e,t,r)),xi(e,Si(e,t,r&&r.bias||(E(t.primary().head,e.sel.primary().head)<0?-1:1),!0)),r&&!1===r.scroll||!e.cm||jr(e.cm)}function xi(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=e.cm.curOp.selectionChanged=!0,Ne(e.cm)),bt(e,"cursorActivity",e))}function Ci(e){xi(e,Si(e,e.sel,null,!1))}function Si(e,t,r,n){for(var i,o=0;o<t.ranges.length;o++){var l=t.ranges[o],s=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],a=ki(e,l.anchor,s&&s.anchor,r,n),u=ki(e,l.head,s&&s.head,r,n);(i||a!=l.anchor||u!=l.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new Ts(a,u))}return i?In(i,t.primIndex):t}function Li(e,t,r,n,i){var o=M(e,t.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var s=o.markedSpans[l],a=s.marker;if((null==s.from||(a.inclusiveLeft?s.from<=t.ch:s.from<t.ch))&&(null==s.to||(a.inclusiveRight?s.to>=t.ch:s.to>t.ch))){if(i&&(Te(a,"beforeCursorEnter"),a.explicitlyCleared)){if(o.markedSpans){--l;continue}break}if(!a.atomic)continue;if(r){var u=a.find(n<0?1:-1),c=void 0;if((n<0?a.inclusiveRight:a.inclusiveLeft)&&(u=Ti(e,u,-n,u&&u.line==t.line?o:null)),u&&u.line==t.line&&(c=E(u,r))&&(n<0?c<0:c>0))return Li(e,u,t,n,i)}var f=a.find(n<0?-1:1);return(n<0?a.inclusiveLeft:a.inclusiveRight)&&(f=Ti(e,f,n,f.line==t.line?o:null)),f?Li(e,f,t,n,i):null}}return t}function ki(e,t,r,n,i){var o=n||1,l=Li(e,t,r,o,i)||!i&&Li(e,t,r,o,!0)||Li(e,t,r,-o,i)||!i&&Li(e,t,r,-o,!0);return l||(e.cantEdit=!0,P(e.first,0))}function Ti(e,t,r,n){return r<0&&0==t.ch?t.line>e.first?U(e,P(t.line-1)):null:r>0&&t.ch==(n||M(e,t.line)).text.length?t.line<e.first+e.size-1?P(t.line+1,0):null:new P(t.line,t.ch+r)}function Mi(e){e.setSelection(P(e.firstLine(),0),P(e.lastLine()),Gl)}function Ni(e,t,r){var n={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return n.canceled=!0}};return r&&(n.update=function(t,r,i,o){t&&(n.from=U(e,t)),r&&(n.to=U(e,r)),i&&(n.text=i),void 0!==o&&(n.origin=o)}),Te(e,"beforeChange",e,n),e.cm&&Te(e.cm,"beforeChange",e.cm,n),n.canceled?null:{from:n.from,to:n.to,text:n.text,origin:n.origin}}function Oi(e,t,r){if(e.cm){if(!e.cm.curOp)return dn(e.cm,Oi)(e,t,r);if(e.cm.state.suppressEdits)return}if(!(Oe(e,"beforeChange")||e.cm&&Oe(e.cm,"beforeChange"))||(t=Ni(e,t,!0))){var n=Yl&&!r&&te(e,t.from,t.to);if(n)for(var i=n.length-1;i>=0;--i)Ai(e,{from:n[i].from,to:n[i].to,text:i?[""]:t.text,origin:t.origin});else Ai(e,t)}}function Ai(e,t){if(1!=t.text.length||""!=t.text[0]||0!=E(t.from,t.to)){var r=Un(e,t);ni(e,t,r,e.cm?e.cm.curOp.id:NaN),Hi(e,t,r,J(e,t));var n=[];$n(e,function(e,r){r||-1!=h(n,e.history)||(Ii(e.history,t),n.push(e.history)),Hi(e,t,null,J(e,t))})}}function Wi(e,t,r){if(!e.cm||!e.cm.state.suppressEdits||r){for(var n,i=e.history,o=e.sel,l="undo"==t?i.done:i.undone,s="undo"==t?i.undone:i.done,a=0;a<l.length&&(n=l[a],r?!n.ranges||n.equals(e.sel):n.ranges);a++);if(a!=l.length){for(i.lastOrigin=i.lastSelOrigin=null;(n=l.pop()).ranges;){if(li(n,s),r&&!n.equals(e.sel))return void bi(e,n,{clearRedo:!1});o=n}var u=[];li(o,s),s.push({changes:u,generation:i.generation}),i.generation=n.generation||++i.maxGeneration;for(var c=Oe(e,"beforeChange")||e.cm&&Oe(e.cm,"beforeChange"),f=n.changes.length-1;f>=0;--f){var d=function(r){var i=n.changes[r];if(i.origin=t,c&&!Ni(e,i,!1))return l.length=0,{};u.push(ei(e,i));var o=r?Un(e,i):g(l);Hi(e,i,o,ci(e,i)),!r&&e.cm&&e.cm.scrollIntoView({from:i.from,to:Bn(i)});var s=[];$n(e,function(e,t){t||-1!=h(s,e.history)||(Ii(e.history,i),s.push(e.history)),Hi(e,i,null,ci(e,i))})}(f);if(d)return d.v}}}}function Di(e,t){if(0!=t&&(e.first+=t,e.sel=new ks(v(e.sel.ranges,function(e){return new Ts(P(e.anchor.line+t,e.anchor.ch),P(e.head.line+t,e.head.ch))}),e.sel.primIndex),e.cm)){vn(e.cm,e.first,e.first-t,t);for(var r=e.cm.display,n=r.viewFrom;n<r.viewTo;n++)mn(e.cm,n,"gutter")}}function Hi(e,t,r,n){if(e.cm&&!e.cm.curOp)return dn(e.cm,Hi)(e,t,r,n);if(t.to.line<e.first)Di(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);Di(e,i),t={from:P(e.first,0),to:P(t.to.line+i,t.to.ch),text:[g(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:P(o,M(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=N(e,t.from,t.to),r||(r=Un(e,t)),e.cm?Fi(e.cm,t,n):_n(e,t,n),wi(e,r,Gl)}}function Fi(e,t,r){var n=e.doc,i=e.display,o=t.from,l=t.to,s=!1,a=o.line;e.options.lineWrapping||(a=W(fe(M(n,o.line))),n.iter(a,l.line+1,function(e){if(e==i.maxLine)return s=!0,!0})),n.sel.contains(t.from,t.to)>-1&&Ne(e),_n(n,t,r,xr(e)),e.options.lineWrapping||(n.iter(a,o.line+t.text.length,function(e){var t=be(e);t>i.maxLineLength&&(i.maxLine=e,i.maxLineLength=t,i.maxLineChanged=!0,s=!1)}),s&&(e.curOp.updateMaxLine=!0)),nt(n,o.line),Cn(e,400);var u=t.text.length-(l.line-o.line)-1;t.full?vn(e):o.line!=l.line||1!=t.text.length||Yn(e.doc,t)?vn(e,o.line,l.line+1,u):mn(e,o.line,"text");var c=Oe(e,"changes"),f=Oe(e,"change");if(f||c){var h={from:o,to:l,text:t.text,removed:t.removed,origin:t.origin};f&&bt(e,"change",e,h),c&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(h)}e.display.selForContextMenu=null}function Pi(e,t,r,n,i){if(n||(n=r),E(n,r)<0){var o;r=(o=[n,r])[0],n=o[1]}"string"==typeof t&&(t=e.splitLines(t)),Oi(e,{from:r,to:n,text:t,origin:i})}function Ei(e,t,r,n){r<e.line?e.line+=n:t<e.line&&(e.line=t,e.ch=0)}function zi(e,t,r,n){for(var i=0;i<e.length;++i){var o=e[i],l=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var s=0;s<o.ranges.length;s++)Ei(o.ranges[s].anchor,t,r,n),Ei(o.ranges[s].head,t,r,n)}else{for(var a=0;a<o.changes.length;++a){var u=o.changes[a];if(r<u.from.line)u.from=P(u.from.line+n,u.from.ch),u.to=P(u.to.line+n,u.to.ch);else if(t<=u.to.line){l=!1;break}}l||(e.splice(0,i+1),i=0)}}}function Ii(e,t){var r=t.from.line,n=t.to.line,i=t.text.length-(n-r)-1;zi(e.done,r,n,i),zi(e.undone,r,n,i)}function Ri(e,t,r,n){var i=t,o=t;return"number"==typeof t?o=M(e,G(e,t)):i=W(t),null==i?null:(n(o,i)&&e.cm&&mn(e.cm,i,r),o)}function Bi(e){var t=this;this.lines=e,this.parent=null;for(var r=0,n=0;n<e.length;++n)e[n].parent=t,r+=e[n].height;this.height=r}function Gi(e){var t=this;this.children=e;for(var r=0,n=0,i=0;i<e.length;++i){var o=e[i];r+=o.chunkSize(),n+=o.height,o.parent=t}this.size=r,this.height=n,this.parent=null}function Ui(e,t,r){ye(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Kr(e,r)}function Vi(e,t,r,n){var i=new Ms(e,r,n),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),Ri(e,t,"widget",function(t){var r=t.widgets||(t.widgets=[]);if(null==i.insertAt?r.push(i):r.splice(Math.min(r.length-1,Math.max(0,i.insertAt)),0,i),i.line=t,o&&!ve(e,t)){var n=ye(t)<e.scrollTop;A(t,t.height+Ht(i)),n&&Kr(o,i.height),o.curOp.forceUpdate=!0}return!0}),bt(o,"lineWidgetAdded",o,i,"number"==typeof t?t:W(t)),i}function Ki(e,t,r,n,o){if(n&&n.shared)return ji(e,t,r,n,o);if(e.cm&&!e.cm.curOp)return dn(e.cm,Ki)(e,t,r,n,o);var l=new Os(e,o),s=E(t,r);if(n&&c(n,l,!1),s>0||0==s&&!1!==l.clearWhenEmpty)return l;if(l.replacedWith&&(l.collapsed=!0,l.widgetNode=i("span",[l.replacedWith],"CodeMirror-widget"),n.handleMouseEvents||l.widgetNode.setAttribute("cm-ignore-events","true"),n.insertLeft&&(l.widgetNode.insertLeft=!0)),l.collapsed){if(ce(e,t.line,t,r,l)||t.line!=r.line&&ce(e,r.line,t,r,l))throw new Error("Inserting collapsed marker partially overlapping an existing one");X()}l.addToHistory&&ni(e,{from:t,to:r,origin:"markText"},e.sel,NaN);var a,u=t.line,f=e.cm;if(e.iter(u,r.line+1,function(e){f&&l.collapsed&&!f.options.lineWrapping&&fe(e)==f.display.maxLine&&(a=!0),l.collapsed&&u!=t.line&&A(e,0),q(e,new Y(l,u==t.line?t.ch:null,u==r.line?r.ch:null)),++u}),l.collapsed&&e.iter(t.line,r.line+1,function(t){ve(e,t)&&A(t,0)}),l.clearOnEnter&&Ql(l,"beforeCursorEnter",function(){return l.clear()}),l.readOnly&&(j(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),l.collapsed&&(l.id=++Ns,l.atomic=!0),f){if(a&&(f.curOp.updateMaxLine=!0),l.collapsed)vn(f,t.line,r.line+1);else if(l.className||l.title||l.startStyle||l.endStyle||l.css)for(var h=t.line;h<=r.line;h++)mn(f,h,"text");l.atomic&&Ci(f.doc),bt(f,"markerAdded",f,l)}return l}function ji(e,t,r,n,i){(n=c(n)).shared=!1;var o=[Ki(e,t,r,n,i)],l=o[0],s=n.widgetNode;return $n(e,function(e){s&&(n.widgetNode=s.cloneNode(!0)),o.push(Ki(e,U(e,t),U(e,r),n,i));for(var a=0;a<e.linked.length;++a)if(e.linked[a].isParent)return;l=g(o)}),new As(o,l)}function Xi(e){return e.findMarks(P(e.first,0),e.clipPos(P(e.lastLine())),function(e){return e.parent})}function Yi(e,t){for(var r=0;r<t.length;r++){var n=t[r],i=n.find(),o=e.clipPos(i.from),l=e.clipPos(i.to);if(E(o,l)){var s=Ki(e,o,l,n.primary,n.primary.type);n.markers.push(s),s.parent=n}}}function _i(e){for(var t=0;t<e.length;t++)!function(t){var r=e[t],n=[r.primary.doc];$n(r.primary.doc,function(e){return n.push(e)});for(var i=0;i<r.markers.length;i++){var o=r.markers[i];-1==h(n,o.doc)&&(o.parent=null,r.markers.splice(i--,1))}}(t)}function $i(e){var t=this;if(Qi(t),!Me(t,e)&&!Ft(t.display,e)){We(e),gl&&(Hs=+new Date);var r=Sr(t,e,!0),n=e.dataTransfer.files;if(r&&!t.isReadOnly())if(n&&n.length&&window.FileReader&&window.File)for(var i=n.length,o=Array(i),l=0,s=function(e,n){if(!t.options.allowDropFileTypes||-1!=h(t.options.allowDropFileTypes,e.type)){var s=new FileReader;s.onload=dn(t,function(){var e=s.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(e)&&(e=""),o[n]=e,++l==i){var a={from:r=U(t.doc,r),to:r,text:t.doc.splitLines(o.join(t.doc.lineSeparator())),origin:"paste"};Oi(t.doc,a),yi(t.doc,Rn(r,Bn(a)))}}),s.readAsText(e)}},a=0;a<i;++a)s(n[a],a);else{if(t.state.draggingText&&t.doc.sel.contains(r)>-1)return t.state.draggingText(e),void setTimeout(function(){return t.display.input.focus()},20);try{var u=e.dataTransfer.getData("Text");if(u){var c;if(t.state.draggingText&&!t.state.draggingText.copy&&(c=t.listSelections()),wi(t.doc,Rn(r,r)),c)for(var f=0;f<c.length;++f)Pi(t.doc,"",c[f].anchor,c[f].head,"drag");t.replaceSelection(u,"around","paste"),t.display.input.focus()}}catch(e){}}}}function qi(e,t){if(gl&&(!e.state.draggingText||+new Date-Hs<100))Fe(t);else if(!Me(e,t)&&!Ft(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!xl)){var r=n("img",null,null,"position: fixed; left: 0; top: 0;");r.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",wl&&(r.width=r.height=1,e.display.wrapper.appendChild(r),r._top=r.offsetTop),t.dataTransfer.setDragImage(r,0,0),wl&&r.parentNode.removeChild(r)}}function Zi(e,t){var i=Sr(e,t);if(i){var o=document.createDocumentFragment();Mr(e,i,o),e.display.dragCursor||(e.display.dragCursor=n("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),r(e.display.dragCursor,o)}}function Qi(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Ji(e){if(document.getElementsByClassName)for(var t=document.getElementsByClassName("CodeMirror"),r=0;r<t.length;r++){var n=t[r].CodeMirror;n&&e(n)}}function eo(){Fs||(to(),Fs=!0)}function to(){var e;Ql(window,"resize",function(){null==e&&(e=setTimeout(function(){e=null,Ji(ro)},100))}),Ql(window,"blur",function(){return Ji(Fr)})}function ro(e){var t=e.display;t.lastWrapHeight==t.wrapper.clientHeight&&t.lastWrapWidth==t.wrapper.clientWidth||(t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize())}function no(e){var t=e.split(/-(?!$)/);e=t[t.length-1];for(var r,n,i,o,l=0;l<t.length-1;l++){var s=t[l];if(/^(cmd|meta|m)$/i.test(s))o=!0;else if(/^a(lt)?$/i.test(s))r=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else{if(!/^s(hift)?$/i.test(s))throw new Error("Unrecognized modifier name: "+s);i=!0}}return r&&(e="Alt-"+e),n&&(e="Ctrl-"+e),o&&(e="Cmd-"+e),i&&(e="Shift-"+e),e}function io(e){var t={};for(var r in e)if(e.hasOwnProperty(r)){var n=e[r];if(/^(name|fallthrough|(de|at)tach)$/.test(r))continue;if("..."==n){delete e[r];continue}for(var i=v(r.split(" "),no),o=0;o<i.length;o++){var l=void 0,s=void 0;o==i.length-1?(s=i.join(" "),l=n):(s=i.slice(0,o+1).join(" "),l="...");var a=t[s];if(a){if(a!=l)throw new Error("Inconsistent bindings for "+s)}else t[s]=l}delete e[r]}for(var u in t)e[u]=t[u];return e}function oo(e,t,r,n){var i=(t=uo(t)).call?t.call(e,n):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&r(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return oo(e,t.fallthrough,r,n);for(var o=0;o<t.fallthrough.length;o++){var l=oo(e,t.fallthrough[o],r,n);if(l)return l}}}function lo(e){var t="string"==typeof e?e:Ps[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function so(e,t,r){var n=e;return t.altKey&&"Alt"!=n&&(e="Alt-"+e),(Dl?t.metaKey:t.ctrlKey)&&"Ctrl"!=n&&(e="Ctrl-"+e),(Dl?t.ctrlKey:t.metaKey)&&"Cmd"!=n&&(e="Cmd-"+e),!r&&t.shiftKey&&"Shift"!=n&&(e="Shift-"+e),e}function ao(e,t){if(wl&&34==e.keyCode&&e.char)return!1;var r=Ps[e.keyCode];return null!=r&&!e.altGraphKey&&so(r,e,t)}function uo(e){return"string"==typeof e?Rs[e]:e}function co(e,t){for(var r=e.doc.sel.ranges,n=[],i=0;i<r.length;i++){for(var o=t(r[i]);n.length&&E(o.from,g(n).to)<=0;){var l=n.pop();if(E(l.from,o.from)<0){o.from=l.from;break}}n.push(o)}hn(e,function(){for(var t=n.length-1;t>=0;t--)Pi(e.doc,"",n[t].from,n[t].to,"+delete");jr(e)})}function fo(e,t,r){var n=L(e.text,t+r,r);return n<0||n>e.text.length?null:n}function ho(e,t,r){var n=fo(e,t.ch,r);return null==n?null:new P(t.line,n,r<0?"after":"before")}function po(e,t,r,n,i){if(e){var o=Se(r,t.doc.direction);if(o){var l,s=i<0?g(o):o[0],a=i<0==(1==s.level)?"after":"before";if(s.level>0||"rtl"==t.doc.direction){var u=Xt(t,r);l=i<0?r.text.length-1:0;var c=Yt(t,u,l).top;l=k(function(e){return Yt(t,u,e).top==c},i<0==(1==s.level)?s.from:s.to-1,l),"before"==a&&(l=fo(r,l,1))}else l=i<0?s.to:s.from;return new P(n,l,a)}}return new P(n,i<0?r.text.length:0,i<0?"before":"after")}function go(e,t,r,n){var i=Se(t,e.doc.direction);if(!i)return ho(t,r,n);r.ch>=t.text.length?(r.ch=t.text.length,r.sticky="before"):r.ch<=0&&(r.ch=0,r.sticky="after");var o=Ce(i,r.ch,r.sticky),l=i[o];if("ltr"==e.doc.direction&&l.level%2==0&&(n>0?l.to>r.ch:l.from<r.ch))return ho(t,r,n);var s,a=function(e,r){return fo(t,e instanceof P?e.ch:e,r)},u=function(r){return e.options.lineWrapping?(s=s||Xt(e,t),hr(e,t,s,r)):{begin:0,end:t.text.length}},c=u("before"==r.sticky?a(r,-1):r.ch);if("rtl"==e.doc.direction||1==l.level){var f=1==l.level==n<0,h=a(r,f?1:-1);if(null!=h&&(f?h<=l.to&&h<=c.end:h>=l.from&&h>=c.begin)){var d=f?"before":"after";return new P(r.line,h,d)}}var p=function(e,t,n){for(var o=function(e,t){return t?new P(r.line,a(e,1),"before"):new P(r.line,e,"after")};e>=0&&e<i.length;e+=t){var l=i[e],s=t>0==(1!=l.level),u=s?n.begin:a(n.end,-1);if(l.from<=u&&u<l.to)return o(u,s);if(u=s?l.from:a(l.to,-1),n.begin<=u&&u<n.end)return o(u,s)}},g=p(o+n,n,c);if(g)return g;var v=n>0?c.end:a(c.begin,-1);return null==v||n>0&&v==t.text.length||!(g=p(n>0?0:i.length-1,n,u(v)))?null:g}function vo(e,t){var r=M(e.doc,t),n=fe(r);return n!=r&&(t=W(n)),po(!0,e,n,t,1)}function mo(e,t){var r=M(e.doc,t),n=he(r);return n!=r&&(t=W(n)),po(!0,e,r,t,-1)}function yo(e,t){var r=vo(e,t.line),n=M(e.doc,r.line),i=Se(n,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(0,n.text.search(/\S/)),l=t.line==r.line&&t.ch<=o&&t.ch;return P(r.line,l?0:o,r.sticky)}return r}function bo(e,t,r){if("string"==typeof t&&!(t=Bs[t]))return!1;e.display.input.ensurePolled();var n=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r&&(e.display.shift=!1),i=t(e)!=Bl}finally{e.display.shift=n,e.state.suppressEdits=!1}return i}function wo(e,t,r){for(var n=0;n<e.state.keyMaps.length;n++){var i=oo(t,e.state.keyMaps[n],r,e);if(i)return i}return e.options.extraKeys&&oo(t,e.options.extraKeys,r,e)||oo(t,e.options.keyMap,r,e)}function xo(e,t,r,n){var i=e.state.keySeq;if(i){if(lo(t))return"handled";Gs.set(50,function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())}),t=i+" "+t}var o=wo(e,t,n);return"multi"==o&&(e.state.keySeq=t),"handled"==o&&bt(e,"keyHandled",e,t,r),"handled"!=o&&"multi"!=o||(We(r),Ar(e)),i&&!o&&/\'$/.test(t)?(We(r),!0):!!o}function Co(e,t){var r=ao(t,!0);return!!r&&(t.shiftKey&&!e.state.keySeq?xo(e,"Shift-"+r,t,function(t){return bo(e,t,!0)})||xo(e,r,t,function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return bo(e,t)}):xo(e,r,t,function(t){return bo(e,t)}))}function So(e,t,r){return xo(e,"'"+r+"'",t,function(t){return bo(e,t,!0)})}function Lo(e){var t=this;if(t.curOp.focus=l(),!Me(t,e)){gl&&vl<11&&27==e.keyCode&&(e.returnValue=!1);var r=e.keyCode;t.display.shift=16==r||e.shiftKey;var n=Co(t,e);wl&&(Us=n?r:null,!n&&88==r&&!rs&&(Ml?e.metaKey:e.ctrlKey)&&t.replaceSelection("",null,"cut")),18!=r||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||ko(t)}}function ko(e){function t(e){18!=e.keyCode&&e.altKey||(Fl(r,"CodeMirror-crosshair"),ke(document,"keyup",t),ke(document,"mouseover",t))}var r=e.display.lineDiv;s(r,"CodeMirror-crosshair"),Ql(document,"keyup",t),Ql(document,"mouseover",t)}function To(e){16==e.keyCode&&(this.doc.sel.shift=!1),Me(this,e)}function Mo(e){var t=this;if(!(Ft(t.display,e)||Me(t,e)||e.ctrlKey&&!e.altKey||Ml&&e.metaKey)){var r=e.keyCode,n=e.charCode;if(wl&&r==Us)return Us=null,void We(e);if(!wl||e.which&&!(e.which<10)||!Co(t,e)){var i=String.fromCharCode(null==n?r:n);"\b"!=i&&(So(t,e,i)||t.display.input.onKeyPress(e))}}}function No(e,t){var r=+new Date;return js&&js.compare(r,e,t)?(Ks=js=null,"triple"):Ks&&Ks.compare(r,e,t)?(js=new Vs(r,e,t),Ks=null,"double"):(Ks=new Vs(r,e,t),js=null,"single")}function Oo(e){var t=this,r=t.display;if(!(Me(t,e)||r.activeTouch&&r.input.supportsTouch()))if(r.input.ensurePolled(),r.shift=e.shiftKey,Ft(r,e))ml||(r.scroller.draggable=!1,setTimeout(function(){return r.scroller.draggable=!0},100));else if(!Io(t,e)){var n=Sr(t,e),i=Ee(e),o=n?No(n,i):"single";window.focus(),1==i&&t.state.selectingText&&t.state.selectingText(e),n&&Ao(t,i,n,o,e)||(1==i?n?Do(t,n,o,e):Pe(e)==r.scroller&&We(e):2==i?(n&&di(t.doc,n),setTimeout(function(){return r.input.focus()},20)):3==i&&(Hl?Ro(t,e):Dr(t)))}}function Ao(e,t,r,n,i){var o="Click";return"double"==n?o="Double"+o:"triple"==n&&(o="Triple"+o),o=(1==t?"Left":2==t?"Middle":"Right")+o,xo(e,so(o,i),i,function(t){if("string"==typeof t&&(t=Bs[t]),!t)return!1;var n=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n=t(e,r)!=Bl}finally{e.state.suppressEdits=!1}return n})}function Wo(e,t,r){var n=e.getOption("configureMouse"),i=n?n(e,t,r):{};if(null==i.unit){var o=Nl?r.shiftKey&&r.metaKey:r.altKey;i.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}return(null==i.extend||e.doc.extend)&&(i.extend=e.doc.extend||r.shiftKey),null==i.addNew&&(i.addNew=Ml?r.metaKey:r.ctrlKey),null==i.moveOnDrag&&(i.moveOnDrag=!(Ml?r.altKey:r.ctrlKey)),i}function Do(e,t,r,n){gl?setTimeout(u(Wr,e),0):e.curOp.focus=l();var i,o=Wo(e,r,n),s=e.doc.sel;e.options.dragDrop&&Jl&&!e.isReadOnly()&&"single"==r&&(i=s.contains(t))>-1&&(E((i=s.ranges[i]).from(),t)<0||t.xRel>0)&&(E(i.to(),t)>0||t.xRel<0)?Ho(e,n,t,o):Po(e,n,t,o)}function Ho(e,t,r,n){var i=e.display,o=!1,l=dn(e,function(t){ml&&(i.scroller.draggable=!1),e.state.draggingText=!1,ke(document,"mouseup",l),ke(document,"mousemove",s),ke(i.scroller,"dragstart",a),ke(i.scroller,"drop",l),o||(We(t),n.addNew||di(e.doc,r,null,null,n.extend),ml||gl&&9==vl?setTimeout(function(){document.body.focus(),i.input.focus()},20):i.input.focus())}),s=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},a=function(){return o=!0};ml&&(i.scroller.draggable=!0),e.state.draggingText=l,l.copy=!n.moveOnDrag,i.scroller.dragDrop&&i.scroller.dragDrop(),Ql(document,"mouseup",l),Ql(document,"mousemove",s),Ql(i.scroller,"dragstart",a),Ql(i.scroller,"drop",l),Dr(e),setTimeout(function(){return i.input.focus()},20)}function Fo(e,t,r){if("char"==r)return new Ts(t,t);if("word"==r)return e.findWordAt(t);if("line"==r)return new Ts(P(t.line,0),U(e.doc,P(t.line+1,0)));var n=r(e,t);return new Ts(n.from,n.to)}function Po(e,t,r,n){function i(t){if(0!=E(m,t))if(m=t,"rectangle"==n.unit){for(var i=[],o=e.options.tabSize,l=f(M(u,r.line).text,r.ch,o),s=f(M(u,t.line).text,t.ch,o),a=Math.min(l,s),g=Math.max(l,s),v=Math.min(r.line,t.line),y=Math.min(e.lastLine(),Math.max(r.line,t.line));v<=y;v++){var b=M(u,v).text,w=d(b,a,o);a==g?i.push(new Ts(P(v,w),P(v,w))):b.length>w&&i.push(new Ts(P(v,w),P(v,d(b,g,o))))}i.length||i.push(new Ts(r,r)),bi(u,In(p.ranges.slice(0,h).concat(i),h),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var x,C=c,S=Fo(e,t,n.unit),L=C.anchor;E(S.anchor,L)>0?(x=S.head,L=B(C.from(),S.anchor)):(x=S.anchor,L=R(C.to(),S.head));var k=p.ranges.slice(0);k[h]=Eo(e,new Ts(U(u,L),x)),bi(u,In(k,h),Ul)}}function o(t){var r=++b,s=Sr(e,t,!0,"rectangle"==n.unit);if(s)if(0!=E(s,m)){e.curOp.focus=l(),i(s);var c=zr(a,u);(s.line>=c.to||s.line<c.from)&&setTimeout(dn(e,function(){b==r&&o(t)}),150)}else{var f=t.clientY<y.top?-20:t.clientY>y.bottom?20:0;f&&setTimeout(dn(e,function(){b==r&&(a.scroller.scrollTop+=f,o(t))}),50)}}function s(t){e.state.selectingText=!1,b=1/0,We(t),a.input.focus(),ke(document,"mousemove",w),ke(document,"mouseup",x),u.history.lastSelOrigin=null}var a=e.display,u=e.doc;We(t);var c,h,p=u.sel,g=p.ranges;if(n.addNew&&!n.extend?(h=u.sel.contains(r),c=h>-1?g[h]:new Ts(r,r)):(c=u.sel.primary(),h=u.sel.primIndex),"rectangle"==n.unit)n.addNew||(c=new Ts(r,r)),r=Sr(e,t,!0,!0),h=-1;else{var v=Fo(e,r,n.unit);c=n.extend?hi(c,v.anchor,v.head,n.extend):v}n.addNew?-1==h?(h=g.length,bi(u,In(g.concat([c]),h),{scroll:!1,origin:"*mouse"})):g.length>1&&g[h].empty()&&"char"==n.unit&&!n.extend?(bi(u,In(g.slice(0,h).concat(g.slice(h+1)),0),{scroll:!1,origin:"*mouse"}),p=u.sel):gi(u,h,c,Ul):(h=0,bi(u,new ks([c],0),Ul),p=u.sel);var m=r,y=a.wrapper.getBoundingClientRect(),b=0,w=dn(e,function(e){Ee(e)?o(e):s(e)}),x=dn(e,s);e.state.selectingText=x,Ql(document,"mousemove",w),Ql(document,"mouseup",x)}function Eo(e,t){var r=t.anchor,n=t.head,i=M(e.doc,r.line);if(0==E(r,n)&&r.sticky==n.sticky)return t;var o=Se(i);if(!o)return t;var l=Ce(o,r.ch,r.sticky),s=o[l];if(s.from!=r.ch&&s.to!=r.ch)return t;var a=l+(s.from==r.ch==(1!=s.level)?0:1);if(0==a||a==o.length)return t;var u;if(n.line!=r.line)u=(n.line-r.line)*("ltr"==e.doc.direction?1:-1)>0;else{var c=Ce(o,n.ch,n.sticky),f=c-l||(n.ch-r.ch)*(1==s.level?-1:1);u=c==a-1||c==a?f<0:f>0}var h=o[a+(u?-1:0)],d=u==(1==h.level),p=d?h.from:h.to,g=d?"after":"before";return r.ch==p&&r.sticky==g?t:new Ts(new P(r.line,p,g),n)}function zo(e,t,r,n){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch(t){return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;n&&We(t);var l=e.display,s=l.lineDiv.getBoundingClientRect();if(o>s.bottom||!Oe(e,r))return He(t);o-=s.top-l.viewOffset;for(var a=0;a<e.options.gutters.length;++a){var u=l.gutters.childNodes[a];if(u&&u.getBoundingClientRect().right>=i)return Te(e,r,e,D(e.doc,o),e.options.gutters[a],t),He(t)}}function Io(e,t){return zo(e,t,"gutterClick",!0)}function Ro(e,t){Ft(e.display,t)||Bo(e,t)||Me(e,t,"contextmenu")||e.display.input.onContextMenu(t)}function Bo(e,t){return!!Oe(e,"gutterContextMenu")&&zo(e,t,"gutterContextMenu",!1)}function Go(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),er(e)}function Uo(e){Hn(e),vn(e),Ir(e)}function Vo(e,t,r){if(!t!=!(r&&r!=Xs)){var n=e.display.dragFunctions,i=t?Ql:ke;i(e.display.scroller,"dragstart",n.start),i(e.display.scroller,"dragenter",n.enter),i(e.display.scroller,"dragover",n.over),i(e.display.scroller,"dragleave",n.leave),i(e.display.scroller,"drop",n.drop)}}function Ko(e){e.options.lineWrapping?(s(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(Fl(e.display.wrapper,"CodeMirror-wrap"),we(e)),Cr(e),vn(e),er(e),setTimeout(function(){return en(e)},100)}function jo(e,t){var r=this;if(!(this instanceof jo))return new jo(e,t);this.options=t=t?c(t):{},c(Ys,t,!1),Fn(t);var n=t.value;"string"==typeof n&&(n=new Ds(n,t.mode,null,t.lineSeparator,t.direction)),this.doc=n;var i=new jo.inputStyles[t.inputStyle](this),o=this.display=new T(e,n,i);o.wrapper.CodeMirror=this,Hn(this),Go(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),rn(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:!1,cutIncoming:!1,selectingText:!1,draggingText:!1,highlight:new El,keySeq:null,specialChars:null},t.autofocus&&!Tl&&o.input.focus(),gl&&vl<11&&setTimeout(function(){return r.display.input.reset(!0)},20),Xo(this),eo(),nn(this),this.curOp.forceUpdate=!0,qn(this,n),t.autofocus&&!Tl||this.hasFocus()?setTimeout(u(Hr,this),20):Fr(this);for(var l in _s)_s.hasOwnProperty(l)&&_s[l](r,t[l],Xs);Rr(this),t.finishInit&&t.finishInit(this);for(var s=0;s<$s.length;++s)$s[s](r);on(this),ml&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}function Xo(e){function t(){i.activeTouch&&(o=setTimeout(function(){return i.activeTouch=null},1e3),(l=i.activeTouch).end=+new Date)}function r(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}function n(e,t){if(null==t.left)return!0;var r=t.left-e.left,n=t.top-e.top;return r*r+n*n>400}var i=e.display;Ql(i.scroller,"mousedown",dn(e,Oo)),gl&&vl<11?Ql(i.scroller,"dblclick",dn(e,function(t){if(!Me(e,t)){var r=Sr(e,t);if(r&&!Io(e,t)&&!Ft(e.display,t)){We(t);var n=e.findWordAt(r);di(e.doc,n.anchor,n.head)}}})):Ql(i.scroller,"dblclick",function(t){return Me(e,t)||We(t)}),Hl||Ql(i.scroller,"contextmenu",function(t){return Ro(e,t)});var o,l={end:0};Ql(i.scroller,"touchstart",function(t){if(!Me(e,t)&&!r(t)&&!Io(e,t)){i.input.ensurePolled(),clearTimeout(o);var n=+new Date;i.activeTouch={start:n,moved:!1,prev:n-l.end<=300?l:null},1==t.touches.length&&(i.activeTouch.left=t.touches[0].pageX,i.activeTouch.top=t.touches[0].pageY)}}),Ql(i.scroller,"touchmove",function(){i.activeTouch&&(i.activeTouch.moved=!0)}),Ql(i.scroller,"touchend",function(r){var o=i.activeTouch;if(o&&!Ft(i,r)&&null!=o.left&&!o.moved&&new Date-o.start<300){var l,s=e.coordsChar(i.activeTouch,"page");l=!o.prev||n(o,o.prev)?new Ts(s,s):!o.prev.prev||n(o,o.prev.prev)?e.findWordAt(s):new Ts(P(s.line,0),U(e.doc,P(s.line+1,0))),e.setSelection(l.anchor,l.head),e.focus(),We(r)}t()}),Ql(i.scroller,"touchcancel",t),Ql(i.scroller,"scroll",function(){i.scroller.clientHeight&&(qr(e,i.scroller.scrollTop),Qr(e,i.scroller.scrollLeft,!0),Te(e,"scroll",e))}),Ql(i.scroller,"mousewheel",function(t){return zn(e,t)}),Ql(i.scroller,"DOMMouseScroll",function(t){return zn(e,t)}),Ql(i.wrapper,"scroll",function(){return i.wrapper.scrollTop=i.wrapper.scrollLeft=0}),i.dragFunctions={enter:function(t){Me(e,t)||Fe(t)},over:function(t){Me(e,t)||(Zi(e,t),Fe(t))},start:function(t){return qi(e,t)},drop:dn(e,$i),leave:function(t){Me(e,t)||Qi(e)}};var s=i.input.getField();Ql(s,"keyup",function(t){return To.call(e,t)}),Ql(s,"keydown",dn(e,Lo)),Ql(s,"keypress",dn(e,Mo)),Ql(s,"focus",function(t){return Hr(e,t)}),Ql(s,"blur",function(t){return Fr(e,t)})}function Yo(e,t,r,n){var i,o=e.doc;null==r&&(r="add"),"smart"==r&&(o.mode.indent?i=$e(e,t).state:r="prev");var l=e.options.tabSize,s=M(o,t),a=f(s.text,null,l);s.stateAfter&&(s.stateAfter=null);var u,c=s.text.match(/^\s*/)[0];if(n||/\S/.test(s.text)){if("smart"==r&&((u=o.mode.indent(i,s.text.slice(c.length),s.text))==Bl||u>150)){if(!n)return;r="prev"}}else u=0,r="not";"prev"==r?u=t>o.first?f(M(o,t-1).text,null,l):0:"add"==r?u=a+e.options.indentUnit:"subtract"==r?u=a-e.options.indentUnit:"number"==typeof r&&(u=a+r),u=Math.max(0,u);var h="",d=0;if(e.options.indentWithTabs)for(var g=Math.floor(u/l);g;--g)d+=l,h+="\t";if(d<u&&(h+=p(u-d)),h!=c)return Pi(o,h,P(t,0),P(t,c.length),"+input"),s.stateAfter=null,!0;for(var v=0;v<o.sel.ranges.length;v++){var m=o.sel.ranges[v];if(m.head.line==t&&m.head.ch<c.length){var y=P(t,c.length);gi(o,v,new Ts(y,y));break}}}function _o(e){qs=e}function $o(e,t,r,n,i){var o=e.doc;e.display.shift=!1,n||(n=o.sel);var l=e.state.pasteIncoming||"paste"==i,s=es(t),a=null;if(l&&n.ranges.length>1)if(qs&&qs.text.join("\n")==t){if(n.ranges.length%qs.text.length==0){a=[];for(var u=0;u<qs.text.length;u++)a.push(o.splitLines(qs.text[u]))}}else s.length==n.ranges.length&&e.options.pasteLinesPerSelection&&(a=v(s,function(e){return[e]}));for(var c,f=n.ranges.length-1;f>=0;f--){var h=n.ranges[f],d=h.from(),p=h.to();h.empty()&&(r&&r>0?d=P(d.line,d.ch-r):e.state.overwrite&&!l?p=P(p.line,Math.min(M(o,p.line).text.length,p.ch+g(s).length)):qs&&qs.lineWise&&qs.text.join("\n")==t&&(d=p=P(d.line,0))),c=e.curOp.updateInput;var m={from:d,to:p,text:a?a[f%a.length]:s,origin:i||(l?"paste":e.state.cutIncoming?"cut":"+input")};Oi(e.doc,m),bt(e,"inputRead",e,m)}t&&!l&&Zo(e,t),jr(e),e.curOp.updateInput=c,e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=!1}function qo(e,t){var r=e.clipboardData&&e.clipboardData.getData("Text");if(r)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||hn(t,function(){return $o(t,r,0,null,"paste")}),!0}function Zo(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var r=e.doc.sel,n=r.ranges.length-1;n>=0;n--){var i=r.ranges[n];if(!(i.head.ch>100||n&&r.ranges[n-1].head.line==i.head.line)){var o=e.getModeAt(i.head),l=!1;if(o.electricChars){for(var s=0;s<o.electricChars.length;s++)if(t.indexOf(o.electricChars.charAt(s))>-1){l=Yo(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(M(e.doc,i.head.line).text.slice(0,i.head.ch))&&(l=Yo(e,i.head.line,"smart"));l&&bt(e,"electricInput",e,i.head.line)}}}function Qo(e){for(var t=[],r=[],n=0;n<e.doc.sel.ranges.length;n++){var i=e.doc.sel.ranges[n].head.line,o={anchor:P(i,0),head:P(i+1,0)};r.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:r}}function Jo(e,t){e.setAttribute("autocorrect","off"),e.setAttribute("autocapitalize","off"),e.setAttribute("spellcheck",!!t)}function el(){var e=n("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"),t=n("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return ml?e.style.width="1000px":e.setAttribute("wrap","off"),Ll&&(e.style.border="1px solid black"),Jo(e),t}function tl(e,t,r,n,i){function o(){var n=t.line+r;return!(n<e.first||n>=e.first+e.size)&&(t=new P(n,t.ch,t.sticky),u=M(e,n))}function l(n){var l;if(null==(l=i?go(e.cm,u,t,r):ho(u,t,r))){if(n||!o())return!1;t=po(i,e.cm,u,t.line,r)}else t=l;return!0}var s=t,a=r,u=M(e,t.line);if("char"==n)l();else if("column"==n)l(!0);else if("word"==n||"group"==n)for(var c=null,f="group"==n,h=e.cm&&e.cm.getHelper(t,"wordChars"),d=!0;!(r<0)||l(!d);d=!1){var p=u.text.charAt(t.ch)||"\n",g=x(p,h)?"w":f&&"\n"==p?"n":!f||/\s/.test(p)?null:"p";if(!f||d||g||(g="s"),c&&c!=g){r<0&&(r=1,l(),t.sticky="after");break}if(g&&(c=g),r>0&&!l(!d))break}var v=ki(e,t,s,a,!0);return z(s,v)&&(v.hitSide=!0),v}function rl(e,t,r,n){var i,o=e.doc,l=t.left;if("page"==n){var s=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),a=Math.max(s-.5*mr(e.display),3);i=(r>0?t.bottom:t.top)+r*a}else"line"==n&&(i=r>0?t.bottom+3:t.top-3);for(var u;(u=cr(e,l,i)).outside;){if(r<0?i<=0:i>=o.height){u.hitSide=!0;break}i+=5*r}return u}function nl(e,t){var r=jt(e,t.line);if(!r||r.hidden)return null;var n=M(e.doc,t.line),i=Ut(r,n,t.line),o=Se(n,e.doc.direction),l="left";o&&(l=Ce(o,t.ch)%2?"right":"left");var s=_t(i.map,t.ch,l);return s.offset="right"==s.collapse?s.end:s.start,s}function il(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}function ol(e,t){return t&&(e.bad=!0),e}function ll(e,t,r,n,i){function o(e){return function(t){return t.id==e}}function l(){c&&(u+=f,c=!1)}function s(e){e&&(l(),u+=e)}function a(t){if(1==t.nodeType){var r=t.getAttribute("cm-text");if(null!=r)return void s(r||t.textContent.replace(/\u200b/g,""));var u,h=t.getAttribute("cm-marker");if(h){var d=e.findMarks(P(n,0),P(i+1,0),o(+h));return void(d.length&&(u=d[0].find(0))&&s(N(e.doc,u.from,u.to).join(f)))}if("false"==t.getAttribute("contenteditable"))return;var p=/^(pre|div|p)$/i.test(t.nodeName);p&&l();for(var g=0;g<t.childNodes.length;g++)a(t.childNodes[g]);p&&(c=!0)}else 3==t.nodeType&&s(t.nodeValue)}for(var u="",c=!1,f=e.doc.lineSeparator();a(t),t!=r;)t=t.nextSibling;return u}function sl(e,t,r){var n;if(t==e.display.lineDiv){if(!(n=e.display.lineDiv.childNodes[r]))return ol(e.clipPos(P(e.display.viewTo-1)),!0);t=null,r=0}else for(n=t;;n=n.parentNode){if(!n||n==e.display.lineDiv)return null;if(n.parentNode&&n.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==n)return al(o,t,r)}}function al(e,t,r){function n(t,r,n){for(var i=-1;i<(f?f.length:0);i++)for(var o=i<0?c.map:f[i],l=0;l<o.length;l+=3){var s=o[l+2];if(s==t||s==r){var a=W(i<0?e.line:e.rest[i]),u=o[l]+n;return(n<0||s!=t)&&(u=o[l+(n?1:0)]),P(a,u)}}}var i=e.text.firstChild,l=!1;if(!t||!o(i,t))return ol(P(W(e.line),0),!0);if(t==i&&(l=!0,t=i.childNodes[r],r=0,!t)){var s=e.rest?g(e.rest):e.line;return ol(P(W(s),s.text.length),l)}var a=3==t.nodeType?t:null,u=t;for(a||1!=t.childNodes.length||3!=t.firstChild.nodeType||(a=t.firstChild,r&&(r=a.nodeValue.length));u.parentNode!=i;)u=u.parentNode;var c=e.measure,f=c.maps,h=n(a,u,r);if(h)return ol(h,l);for(var d=u.nextSibling,p=a?a.nodeValue.length-r:0;d;d=d.nextSibling){if(h=n(d,d.firstChild,0))return ol(P(h.line,h.ch-p),l);p+=d.textContent.length}for(var v=u.previousSibling,m=r;v;v=v.previousSibling){if(h=n(v,v.firstChild,-1))return ol(P(h.line,h.ch+m),l);m+=v.textContent.length}}var ul=navigator.userAgent,cl=navigator.platform,fl=/gecko\/\d/i.test(ul),hl=/MSIE \d/.test(ul),dl=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(ul),pl=/Edge\/(\d+)/.exec(ul),gl=hl||dl||pl,vl=gl&&(hl?document.documentMode||6:+(pl||dl)[1]),ml=!pl&&/WebKit\//.test(ul),yl=ml&&/Qt\/\d+\.\d+/.test(ul),bl=!pl&&/Chrome\//.test(ul),wl=/Opera\//.test(ul),xl=/Apple Computer/.test(navigator.vendor),Cl=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(ul),Sl=/PhantomJS/.test(ul),Ll=!pl&&/AppleWebKit/.test(ul)&&/Mobile\/\w+/.test(ul),kl=/Android/.test(ul),Tl=Ll||kl||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(ul),Ml=Ll||/Mac/.test(cl),Nl=/\bCrOS\b/.test(ul),Ol=/win/i.test(cl),Al=wl&&ul.match(/Version\/(\d*\.\d*)/);Al&&(Al=Number(Al[1])),Al&&Al>=15&&(wl=!1,ml=!0);var Wl,Dl=Ml&&(yl||wl&&(null==Al||Al<12.11)),Hl=fl||gl&&vl>=9,Fl=function(t,r){var n=t.className,i=e(r).exec(n);if(i){var o=n.slice(i.index+i[0].length);t.className=n.slice(0,i.index)+(o?i[1]+o:"")}};Wl=document.createRange?function(e,t,r,n){var i=document.createRange();return i.setEnd(n||e,r),i.setStart(e,t),i}:function(e,t,r){var n=document.body.createTextRange();try{n.moveToElementText(e.parentNode)}catch(e){return n}return n.collapse(!0),n.moveEnd("character",r),n.moveStart("character",t),n};var Pl=function(e){e.select()};Ll?Pl=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:gl&&(Pl=function(e){try{e.select()}catch(e){}});var El=function(){this.id=null};El.prototype.set=function(e,t){clearTimeout(this.id),this.id=setTimeout(t,e)};var zl,Il,Rl=30,Bl={toString:function(){return"CodeMirror.Pass"}},Gl={scroll:!1},Ul={origin:"*mouse"},Vl={origin:"+move"},Kl=[""],jl=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/,Xl=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/,Yl=!1,_l=!1,$l=null,ql=function(){function e(e){return e<=247?r.charAt(e):1424<=e&&e<=1524?"R":1536<=e&&e<=1785?n.charAt(e-1536):1774<=e&&e<=2220?"r":8192<=e&&e<=8203?"w":8204==e?"b":"L"}function t(e,t,r){this.level=e,this.from=t,this.to=r}var r="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",n="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111",i=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,o=/[stwN]/,l=/[LRr]/,s=/[Lb1n]/,a=/[1n]/;return function(r,n){var u="ltr"==n?"L":"R";if(0==r.length||"ltr"==n&&!i.test(r))return!1;for(var c=r.length,f=[],h=0;h<c;++h)f.push(e(r.charCodeAt(h)));for(var d=0,p=u;d<c;++d){var v=f[d];"m"==v?f[d]=p:p=v}for(var m=0,y=u;m<c;++m){var b=f[m];"1"==b&&"r"==y?f[m]="n":l.test(b)&&(y=b,"r"==b&&(f[m]="R"))}for(var w=1,x=f[0];w<c-1;++w){var C=f[w];"+"==C&&"1"==x&&"1"==f[w+1]?f[w]="1":","!=C||x!=f[w+1]||"1"!=x&&"n"!=x||(f[w]=x),x=C}for(var S=0;S<c;++S){var L=f[S];if(","==L)f[S]="N";else if("%"==L){var k=void 0;for(k=S+1;k<c&&"%"==f[k];++k);for(var T=S&&"!"==f[S-1]||k<c&&"1"==f[k]?"1":"N",M=S;M<k;++M)f[M]=T;S=k-1}}for(var N=0,O=u;N<c;++N){var A=f[N];"L"==O&&"1"==A?f[N]="L":l.test(A)&&(O=A)}for(var W=0;W<c;++W)if(o.test(f[W])){var D=void 0;for(D=W+1;D<c&&o.test(f[D]);++D);for(var H="L"==(W?f[W-1]:u),F=H==("L"==(D<c?f[D]:u))?H?"L":"R":u,P=W;P<D;++P)f[P]=F;W=D-1}for(var E,z=[],I=0;I<c;)if(s.test(f[I])){var R=I;for(++I;I<c&&s.test(f[I]);++I);z.push(new t(0,R,I))}else{var B=I,G=z.length;for(++I;I<c&&"L"!=f[I];++I);for(var U=B;U<I;)if(a.test(f[U])){B<U&&z.splice(G,0,new t(1,B,U));var V=U;for(++U;U<I&&a.test(f[U]);++U);z.splice(G,0,new t(2,V,U)),B=U}else++U;B<I&&z.splice(G,0,new t(1,B,I))}return"ltr"==n&&(1==z[0].level&&(E=r.match(/^\s+/))&&(z[0].from=E[0].length,z.unshift(new t(0,0,E[0].length))),1==g(z).level&&(E=r.match(/\s+$/))&&(g(z).to-=E[0].length,z.push(new t(0,c-E[0].length,c)))),"rtl"==n?z.reverse():z}}(),Zl=[],Ql=function(e,t,r){if(e.addEventListener)e.addEventListener(t,r,!1);else if(e.attachEvent)e.attachEvent("on"+t,r);else{var n=e._handlers||(e._handlers={});n[t]=(n[t]||Zl).concat(r)}},Jl=function(){if(gl&&vl<9)return!1;var e=n("div");return"draggable"in e||"dragDrop"in e}(),es=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,r=[],n=e.length;t<=n;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),l=o.indexOf("\r");-1!=l?(r.push(o.slice(0,l)),t+=l+1):(r.push(o),t=i+1)}return r}:function(e){return e.split(/\r\n?|\n/)},ts=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},rs=function(){var e=n("div");return"oncopy"in e||(e.setAttribute("oncopy","return;"),"function"==typeof e.oncopy)}(),ns=null,is={},os={},ls={},ss=function(e,t,r){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=r};ss.prototype.eol=function(){return this.pos>=this.string.length},ss.prototype.sol=function(){return this.pos==this.lineStart},ss.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},ss.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},ss.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},ss.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},ss.prototype.eatSpace=function(){for(var e=this,t=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++e.pos;return this.pos>t},ss.prototype.skipToEnd=function(){this.pos=this.string.length},ss.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},ss.prototype.backUp=function(e){this.pos-=e},ss.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=f(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?f(this.string,this.lineStart,this.tabSize):0)},ss.prototype.indentation=function(){return f(this.string,null,this.tabSize)-(this.lineStart?f(this.string,this.lineStart,this.tabSize):0)},ss.prototype.match=function(e,t,r){if("string"!=typeof e){var n=this.string.slice(this.pos).match(e);return n&&n.index>0?null:(n&&!1!==t&&(this.pos+=n[0].length),n)}var i=function(e){return r?e.toLowerCase():e};if(i(this.string.substr(this.pos,e.length))==i(e))return!1!==t&&(this.pos+=e.length),!0},ss.prototype.current=function(){return this.string.slice(this.start,this.pos)},ss.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},ss.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},ss.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var as=function(e,t){this.state=e,this.lookAhead=t},us=function(e,t,r,n){this.state=t,this.doc=e,this.line=r,this.maxLookAhead=n||0,this.baseTokens=null,this.baseTokenPos=1};us.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},us.prototype.baseToken=function(e){var t=this;if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)t.baseTokenPos+=2;var r=this.baseTokens[this.baseTokenPos+1];return{type:r&&r.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},us.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},us.fromSaved=function(e,t,r){return t instanceof as?new us(e,Ke(e.mode,t.state),r,t.lookAhead):new us(e,Ke(e.mode,t),r)},us.prototype.save=function(e){var t=!1!==e?Ke(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new as(t,this.maxLookAhead):t};var cs=function(e,t,r){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=r},fs=function(e,t,r){this.text=e,ne(this,t),this.height=r?r(this):1};fs.prototype.lineNo=function(){return W(this)},Ae(fs);var hs,ds={},ps={},gs=null,vs=null,ms={left:0,right:0,top:0,bottom:0},ys=function(e,t,r){this.cm=r;var i=this.vert=n("div",[n("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),o=this.horiz=n("div",[n("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");e(i),e(o),Ql(i,"scroll",function(){i.clientHeight&&t(i.scrollTop,"vertical")}),Ql(o,"scroll",function(){o.clientWidth&&t(o.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,gl&&vl<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};ys.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,r=e.scrollHeight>e.clientHeight+1,n=e.nativeBarWidth;if(r){this.vert.style.display="block",this.vert.style.bottom=t?n+"px":"0";var i=e.viewHeight-(t?n:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=r?n+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(r?n:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==n&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:r?n:0,bottom:t?n:0}},ys.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},ys.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},ys.prototype.zeroWidthHack=function(){var e=Ml&&!Cl?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new El,this.disableVert=new El},ys.prototype.enableZeroWidthBar=function(e,t,r){function n(){var i=e.getBoundingClientRect();("vert"==r?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1))!=e?e.style.pointerEvents="none":t.set(1e3,n)}e.style.pointerEvents="auto",t.set(1e3,n)},ys.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var bs=function(){};bs.prototype.update=function(){return{bottom:0,right:0}},bs.prototype.setScrollLeft=function(){},bs.prototype.setScrollTop=function(){},bs.prototype.clear=function(){};var ws={native:ys,null:bs},xs=0,Cs=function(e,t,r){var n=e.display;this.viewport=t,this.visible=zr(n,e.doc,t),this.editorIsHidden=!n.wrapper.offsetWidth,this.wrapperHeight=n.wrapper.clientHeight,this.wrapperWidth=n.wrapper.clientWidth,this.oldDisplayWidth=Rt(e),this.force=r,this.dims=br(e),this.events=[]};Cs.prototype.signal=function(e,t){Oe(e,t)&&this.events.push(arguments)},Cs.prototype.finish=function(){for(var e=this,t=0;t<this.events.length;t++)Te.apply(null,e.events[t])};var Ss=0,Ls=null;gl?Ls=-.53:fl?Ls=15:bl?Ls=-.7:xl&&(Ls=-1/3);var ks=function(e,t){this.ranges=e,this.primIndex=t};ks.prototype.primary=function(){return this.ranges[this.primIndex]},ks.prototype.equals=function(e){var t=this;if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var r=0;r<this.ranges.length;r++){var n=t.ranges[r],i=e.ranges[r];if(!z(n.anchor,i.anchor)||!z(n.head,i.head))return!1}return!0},ks.prototype.deepCopy=function(){for(var e=this,t=[],r=0;r<this.ranges.length;r++)t[r]=new Ts(I(e.ranges[r].anchor),I(e.ranges[r].head));return new ks(t,this.primIndex)},ks.prototype.somethingSelected=function(){for(var e=this,t=0;t<this.ranges.length;t++)if(!e.ranges[t].empty())return!0;return!1},ks.prototype.contains=function(e,t){var r=this;t||(t=e);for(var n=0;n<this.ranges.length;n++){var i=r.ranges[n];if(E(t,i.from())>=0&&E(e,i.to())<=0)return n}return-1};var Ts=function(e,t){this.anchor=e,this.head=t};Ts.prototype.from=function(){return B(this.anchor,this.head)},Ts.prototype.to=function(){return R(this.anchor,this.head)},Ts.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},Bi.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var r=this,n=e,i=e+t;n<i;++n){var o=r.lines[n];r.height-=o.height,ot(o),bt(o,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,r){var n=this;this.height+=r,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var i=0;i<t.length;++i)t[i].parent=n},iterN:function(e,t,r){for(var n=this,i=e+t;e<i;++e)if(r(n.lines[e]))return!0}},Gi.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){var r=this;this.size-=t;for(var n=0;n<this.children.length;++n){var i=r.children[n],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e),s=i.height;if(i.removeInner(e,l),r.height-=s-i.height,o==l&&(r.children.splice(n--,1),i.parent=null),0==(t-=l))break;e=0}else e-=o}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof Bi))){var a=[];this.collapse(a),this.children=[new Bi(a)],this.children[0].parent=this}},collapse:function(e){for(var t=this,r=0;r<this.children.length;++r)t.children[r].collapse(e)},insertInner:function(e,t,r){var n=this;this.size+=t.length,this.height+=r;for(var i=0;i<this.children.length;++i){var o=n.children[i],l=o.chunkSize();if(e<=l){if(o.insertInner(e,t,r),o.lines&&o.lines.length>50){for(var s=o.lines.length%25+25,a=s;a<o.lines.length;){var u=new Bi(o.lines.slice(a,a+=25));o.height-=u.height,n.children.splice(++i,0,u),u.parent=n}o.lines=o.lines.slice(0,s),n.maybeSpill()}break}e-=l}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=new Gi(e.children.splice(e.children.length-5,5));if(e.parent){e.size-=t.size,e.height-=t.height;var r=h(e.parent.children,e);e.parent.children.splice(r+1,0,t)}else{var n=new Gi(e.children);n.parent=e,e.children=[n,t],e=n}t.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,r){for(var n=this,i=0;i<this.children.length;++i){var o=n.children[i],l=o.chunkSize();if(e<l){var s=Math.min(t,l-e);if(o.iterN(e,s,r))return!0;if(0==(t-=s))break;e=0}else e-=l}}};var Ms=function(e,t,r){var n=this;if(r)for(var i in r)r.hasOwnProperty(i)&&(n[i]=r[i]);this.doc=e,this.node=t};Ms.prototype.clear=function(){var e=this,t=this.doc.cm,r=this.line.widgets,n=this.line,i=W(n);if(null!=i&&r){for(var o=0;o<r.length;++o)r[o]==e&&r.splice(o--,1);r.length||(n.widgets=null);var l=Ht(this);A(n,Math.max(0,n.height-l)),t&&(hn(t,function(){Ui(t,n,-l),mn(t,i,"widget")}),bt(t,"lineWidgetCleared",t,this,i))}},Ms.prototype.changed=function(){var e=this,t=this.height,r=this.doc.cm,n=this.line;this.height=null;var i=Ht(this)-t;i&&(A(n,n.height+i),r&&hn(r,function(){r.curOp.forceUpdate=!0,Ui(r,n,i),bt(r,"lineWidgetChanged",r,e,W(n))}))},Ae(Ms);var Ns=0,Os=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++Ns};Os.prototype.clear=function(){var e=this;if(!this.explicitlyCleared){var t=this.doc.cm,r=t&&!t.curOp;if(r&&nn(t),Oe(this,"clear")){var n=this.find();n&&bt(this,"clear",n.from,n.to)}for(var i=null,o=null,l=0;l<this.lines.length;++l){var s=e.lines[l],a=_(s.markedSpans,e);t&&!e.collapsed?mn(t,W(s),"text"):t&&(null!=a.to&&(o=W(s)),null!=a.from&&(i=W(s))),s.markedSpans=$(s.markedSpans,a),null==a.from&&e.collapsed&&!ve(e.doc,s)&&t&&A(s,mr(t.display))}if(t&&this.collapsed&&!t.options.lineWrapping)for(var u=0;u<this.lines.length;++u){var c=fe(e.lines[u]),f=be(c);f>t.display.maxLineLength&&(t.display.maxLine=c,t.display.maxLineLength=f,t.display.maxLineChanged=!0)}null!=i&&t&&this.collapsed&&vn(t,i,o+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,t&&Ci(t.doc)),t&&bt(t,"markerCleared",t,this,i,o),r&&on(t),this.parent&&this.parent.clear()}},Os.prototype.find=function(e,t){var r=this;null==e&&"bookmark"==this.type&&(e=1);for(var n,i,o=0;o<this.lines.length;++o){var l=r.lines[o],s=_(l.markedSpans,r);if(null!=s.from&&(n=P(t?l:W(l),s.from),-1==e))return n;if(null!=s.to&&(i=P(t?l:W(l),s.to),1==e))return i}return n&&{from:n,to:i}},Os.prototype.changed=function(){var e=this,t=this.find(-1,!0),r=this,n=this.doc.cm;t&&n&&hn(n,function(){var i=t.line,o=W(t.line),l=jt(n,o);if(l&&(Qt(l),n.curOp.selectionChanged=n.curOp.forceUpdate=!0),n.curOp.updateMaxLine=!0,!ve(r.doc,i)&&null!=r.height){var s=r.height;r.height=null;var a=Ht(r)-s;a&&A(i,i.height+a)}bt(n,"markerChanged",n,e)})},Os.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=h(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},Os.prototype.detachLine=function(e){if(this.lines.splice(h(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},Ae(Os);var As=function(e,t){var r=this;this.markers=e,this.primary=t;for(var n=0;n<e.length;++n)e[n].parent=r};As.prototype.clear=function(){var e=this;if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var t=0;t<this.markers.length;++t)e.markers[t].clear();bt(this,"clear")}},As.prototype.find=function(e,t){return this.primary.find(e,t)},Ae(As);var Ws=0,Ds=function(e,t,r,n,i){if(!(this instanceof Ds))return new Ds(e,t,r,n,i);null==r&&(r=0),Gi.call(this,[new Bi([new fs("",null)])]),this.first=r,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=r;var o=P(r,0);this.sel=Rn(o),this.history=new Jn(null),this.id=++Ws,this.modeOption=t,this.lineSep=n,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),_n(this,{from:o,to:o,text:e}),bi(this,Rn(o),Gl)};Ds.prototype=b(Gi.prototype,{constructor:Ds,iter:function(e,t,r){r?this.iterN(e-this.first,t-e,r):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var r=0,n=0;n<t.length;++n)r+=t[n].height;this.insertInner(e-this.first,t,r)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=O(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:gn(function(e){var t=P(this.first,0),r=this.first+this.size-1;Oi(this,{from:t,to:P(r,M(this,r).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&Xr(this.cm,0,0),bi(this,Rn(t),Gl)}),replaceRange:function(e,t,r,n){Pi(this,e,t=U(this,t),r=r?U(this,r):t,n)},getRange:function(e,t,r){var n=N(this,U(this,e),U(this,t));return!1===r?n:n.join(r||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(H(this,e))return M(this,e)},getLineNumber:function(e){return W(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=M(this,e)),fe(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return U(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:gn(function(e,t,r){vi(this,U(this,"number"==typeof e?P(e,t||0):e),null,r)}),setSelection:gn(function(e,t,r){vi(this,U(this,e),U(this,t||e),r)}),extendSelection:gn(function(e,t,r){di(this,U(this,e),t&&U(this,t),r)}),extendSelections:gn(function(e,t){pi(this,K(this,e),t)}),extendSelectionsBy:gn(function(e,t){pi(this,K(this,v(this.sel.ranges,e)),t)}),setSelections:gn(function(e,t,r){var n=this;if(e.length){for(var i=[],o=0;o<e.length;o++)i[o]=new Ts(U(n,e[o].anchor),U(n,e[o].head));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),bi(this,In(i,t),r)}}),addSelection:gn(function(e,t,r){var n=this.sel.ranges.slice(0);n.push(new Ts(U(this,e),U(this,t||e))),bi(this,In(n,n.length-1),r)}),getSelection:function(e){for(var t,r=this,n=this.sel.ranges,i=0;i<n.length;i++){var o=N(r,n[i].from(),n[i].to());t=t?t.concat(o):o}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=this,r=[],n=this.sel.ranges,i=0;i<n.length;i++){var o=N(t,n[i].from(),n[i].to());!1!==e&&(o=o.join(e||t.lineSeparator())),r[i]=o}return r},replaceSelection:function(e,t,r){for(var n=[],i=0;i<this.sel.ranges.length;i++)n[i]=e;this.replaceSelections(n,t,r||"+input")},replaceSelections:gn(function(e,t,r){for(var n=this,i=[],o=this.sel,l=0;l<o.ranges.length;l++){var s=o.ranges[l];i[l]={from:s.from(),to:s.to(),text:n.splitLines(e[l]),origin:r}}for(var a=t&&"end"!=t&&Kn(this,i,t),u=i.length-1;u>=0;u--)Oi(n,i[u]);a?yi(this,a):this.cm&&jr(this.cm)}),undo:gn(function(){Wi(this,"undo")}),redo:gn(function(){Wi(this,"redo")}),undoSelection:gn(function(){Wi(this,"undo",!0)}),redoSelection:gn(function(){Wi(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,r=0,n=0;n<e.done.length;n++)e.done[n].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++r;return{undo:t,redo:r}},clearHistory:function(){this.history=new Jn(this.history.maxGeneration)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:fi(this.history.done),undone:fi(this.history.undone)}},setHistory:function(e){var t=this.history=new Jn(this.history.maxGeneration);t.done=fi(e.done.slice(0),null,!0),t.undone=fi(e.undone.slice(0),null,!0)},setGutterMarker:gn(function(e,t,r){return Ri(this,e,"gutter",function(e){var n=e.gutterMarkers||(e.gutterMarkers={});return n[t]=r,!r&&C(n)&&(e.gutterMarkers=null),!0})}),clearGutter:gn(function(e){var t=this;this.iter(function(r){r.gutterMarkers&&r.gutterMarkers[e]&&Ri(t,r,"gutter",function(){return r.gutterMarkers[e]=null,C(r.gutterMarkers)&&(r.gutterMarkers=null),!0})})}),lineInfo:function(e){var t;if("number"==typeof e){if(!H(this,e))return null;if(t=e,!(e=M(this,e)))return null}else if(null==(t=W(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:gn(function(t,r,n){return Ri(this,t,"gutter"==r?"gutter":"class",function(t){var i="text"==r?"textClass":"background"==r?"bgClass":"gutter"==r?"gutterClass":"wrapClass";if(t[i]){if(e(n).test(t[i]))return!1;t[i]+=" "+n}else t[i]=n;return!0})}),removeLineClass:gn(function(t,r,n){return Ri(this,t,"gutter"==r?"gutter":"class",function(t){var i="text"==r?"textClass":"background"==r?"bgClass":"gutter"==r?"gutterClass":"wrapClass",o=t[i];if(!o)return!1;if(null==n)t[i]=null;else{var l=o.match(e(n));if(!l)return!1;var s=l.index+l[0].length;t[i]=o.slice(0,l.index)+(l.index&&s!=o.length?" ":"")+o.slice(s)||null}return!0})}),addLineWidget:gn(function(e,t,r){return Vi(this,e,t,r)}),removeLineWidget:function(e){e.clear()},markText:function(e,t,r){return Ki(this,U(this,e),U(this,t),r,r&&r.type||"range")},setBookmark:function(e,t){var r={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return e=U(this,e),Ki(this,e,e,r,"bookmark")},findMarksAt:function(e){var t=[],r=M(this,(e=U(this,e)).line).markedSpans;if(r)for(var n=0;n<r.length;++n){var i=r[n];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,r){e=U(this,e),t=U(this,t);var n=[],i=e.line;return this.iter(e.line,t.line+1,function(o){var l=o.markedSpans;if(l)for(var s=0;s<l.length;s++){var a=l[s];null!=a.to&&i==e.line&&e.ch>=a.to||null==a.from&&i!=e.line||null!=a.from&&i==t.line&&a.from>=t.ch||r&&!r(a.marker)||n.push(a.marker.parent||a.marker)}++i}),n},getAllMarks:function(){var e=[];return this.iter(function(t){var r=t.markedSpans;if(r)for(var n=0;n<r.length;++n)null!=r[n].from&&e.push(r[n].marker)}),e},posFromIndex:function(e){var t,r=this.first,n=this.lineSeparator().length;return this.iter(function(i){var o=i.text.length+n;if(o>e)return t=e,!0;e-=o,++r}),U(this,P(r,t))},indexFromPos:function(e){var t=(e=U(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var r=this.lineSeparator().length;return this.iter(this.first,e.line,function(e){t+=e.text.length+r}),t},copy:function(e){var t=new Ds(O(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,r=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<r&&(r=e.to);var n=new Ds(O(this,t,r),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(n.history=this.history),(this.linked||(this.linked=[])).push({doc:n,sharedHist:e.sharedHist}),n.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],Yi(n,Xi(this)),n},unlinkDoc:function(e){var t=this;if(e instanceof jo&&(e=e.doc),this.linked)for(var r=0;r<this.linked.length;++r)if(t.linked[r].doc==e){t.linked.splice(r,1),e.unlinkDoc(t),_i(Xi(t));break}if(e.history==this.history){var n=[e.id];$n(e,function(e){return n.push(e.id)},!0),e.history=new Jn(null),e.history.done=fi(this.history.done,n),e.history.undone=fi(this.history.undone,n)}},iterLinkedDocs:function(e){$n(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):es(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:gn(function(e){"rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter(function(e){return e.order=null}),this.cm&&Qn(this.cm))})}),Ds.prototype.eachLine=Ds.prototype.iter;for(var Hs=0,Fs=!1,Ps={3:"Enter",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",127:"Delete",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Es=0;Es<10;Es++)Ps[Es+48]=Ps[Es+96]=String(Es);for(var zs=65;zs<=90;zs++)Ps[zs]=String.fromCharCode(zs);for(var Is=1;Is<=12;Is++)Ps[Is+111]=Ps[Is+63235]="F"+Is;var Rs={};Rs.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Rs.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Rs.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Rs.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Rs.default=Ml?Rs.macDefault:Rs.pcDefault;var Bs={selectAll:Mi,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),Gl)},killLine:function(e){return co(e,function(t){if(t.empty()){var r=M(e.doc,t.head.line).text.length;return t.head.ch==r&&t.head.line<e.lastLine()?{from:t.head,to:P(t.head.line+1,0)}:{from:t.head,to:P(t.head.line,r)}}return{from:t.from(),to:t.to()}})},deleteLine:function(e){return co(e,function(t){return{from:P(t.from().line,0),to:U(e.doc,P(t.to().line+1,0))}})},delLineLeft:function(e){return co(e,function(e){return{from:P(e.from().line,0),to:e.from()}})},delWrappedLineLeft:function(e){return co(e,function(t){var r=e.charCoords(t.head,"div").top+5;return{from:e.coordsChar({left:0,top:r},"div"),to:t.from()}})},delWrappedLineRight:function(e){return co(e,function(t){var r=e.charCoords(t.head,"div").top+5,n=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div");return{from:t.from(),to:n}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(P(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(P(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy(function(t){return vo(e,t.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy(function(t){return yo(e,t.head)},{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy(function(t){return mo(e,t.head.line)},{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy(function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div")},Vl)},goLineLeft:function(e){return e.extendSelectionsBy(function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:r},"div")},Vl)},goLineLeftSmart:function(e){return e.extendSelectionsBy(function(t){var r=e.cursorCoords(t.head,"div").top+5,n=e.coordsChar({left:0,top:r},"div");return n.ch<e.getLine(n.line).search(/\S/)?yo(e,t.head):n},Vl)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"char")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],r=e.listSelections(),n=e.options.tabSize,i=0;i<r.length;i++){var o=r[i].from(),l=f(e.getLine(o.line),o.ch,n);t.push(p(n-l%n))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return hn(e,function(){for(var t=e.listSelections(),r=[],n=0;n<t.length;n++)if(t[n].empty()){var i=t[n].head,o=M(e.doc,i.line).text;if(o)if(i.ch==o.length&&(i=new P(i.line,i.ch-1)),i.ch>0)i=new P(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),P(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var l=M(e.doc,i.line-1).text;l&&(i=new P(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+l.charAt(l.length-1),P(i.line-1,l.length-1),i,"+transpose"))}r.push(new Ts(i,i))}e.setSelections(r)})},newlineAndIndent:function(e){return hn(e,function(){for(var t=e.listSelections(),r=t.length-1;r>=0;r--)e.replaceRange(e.doc.lineSeparator(),t[r].anchor,t[r].head,"+input");t=e.listSelections();for(var n=0;n<t.length;n++)e.indentLine(t[n].from().line,null,!0);jr(e)})},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}},Gs=new El,Us=null,Vs=function(e,t,r){this.time=e,this.pos=t,this.button=r};Vs.prototype.compare=function(e,t,r){return this.time+400>e&&0==E(t,this.pos)&&r==this.button};var Ks,js,Xs={toString:function(){return"CodeMirror.Init"}},Ys={},_s={};jo.defaults=Ys,jo.optionHandlers=_s;var $s=[];jo.defineInitHook=function(e){return $s.push(e)};var qs=null,Zs=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new El,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};Zs.prototype.init=function(e){function t(e){if(!Me(i,e)){if(i.somethingSelected())_o({lineWise:!1,text:i.getSelections()}),"cut"==e.type&&i.replaceSelection("",null,"cut");else{if(!i.options.lineWiseCopyCut)return;var t=Qo(i);_o({lineWise:!0,text:t.text}),"cut"==e.type&&i.operation(function(){i.setSelections(t.ranges,0,Gl),i.replaceSelection("",null,"cut")})}if(e.clipboardData){e.clipboardData.clearData();var r=qs.text.join("\n");if(e.clipboardData.setData("Text",r),e.clipboardData.getData("Text")==r)return void e.preventDefault()}var l=el(),s=l.firstChild;i.display.lineSpace.insertBefore(l,i.display.lineSpace.firstChild),s.value=qs.text.join("\n");var a=document.activeElement;Pl(s),setTimeout(function(){i.display.lineSpace.removeChild(l),a.focus(),a==o&&n.showPrimarySelection()},50)}}var r=this,n=this,i=n.cm,o=n.div=e.lineDiv;Jo(o,i.options.spellcheck),Ql(o,"paste",function(e){Me(i,e)||qo(e,i)||vl<=11&&setTimeout(dn(i,function(){return r.updateFromDOM()}),20)}),Ql(o,"compositionstart",function(e){r.composing={data:e.data,done:!1}}),Ql(o,"compositionupdate",function(e){r.composing||(r.composing={data:e.data,done:!1})}),Ql(o,"compositionend",function(e){r.composing&&(e.data!=r.composing.data&&r.readFromDOMSoon(),r.composing.done=!0)}),Ql(o,"touchstart",function(){return n.forceCompositionEnd()}),Ql(o,"input",function(){r.composing||r.readFromDOMSoon()}),Ql(o,"copy",t),Ql(o,"cut",t)},Zs.prototype.prepareSelection=function(){var e=Tr(this.cm,!1);return e.focus=this.cm.state.focused,e},Zs.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},Zs.prototype.showPrimarySelection=function(){var e=window.getSelection(),t=this.cm,r=t.doc.sel.primary(),n=r.from(),i=r.to();if(t.display.viewTo==t.display.viewFrom||n.line>=t.display.viewTo||i.line<t.display.viewFrom)e.removeAllRanges();else{var o=sl(t,e.anchorNode,e.anchorOffset),l=sl(t,e.focusNode,e.focusOffset);if(!o||o.bad||!l||l.bad||0!=E(B(o,l),n)||0!=E(R(o,l),i)){var s=t.display.view,a=n.line>=t.display.viewFrom&&nl(t,n)||{node:s[0].measure.map[2],offset:0},u=i.line<t.display.viewTo&&nl(t,i);if(!u){var c=s[s.length-1].measure,f=c.maps?c.maps[c.maps.length-1]:c.map;u={node:f[f.length-1],offset:f[f.length-2]-f[f.length-3]}}if(a&&u){var h,d=e.rangeCount&&e.getRangeAt(0);try{h=Wl(a.node,a.offset,u.offset,u.node)}catch(e){}h&&(!fl&&t.state.focused?(e.collapse(a.node,a.offset),h.collapsed||(e.removeAllRanges(),e.addRange(h))):(e.removeAllRanges(),e.addRange(h)),d&&null==e.anchorNode?e.addRange(d):fl&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},Zs.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},Zs.prototype.showMultipleSelections=function(e){r(this.cm.display.cursorDiv,e.cursors),r(this.cm.display.selectionDiv,e.selection)},Zs.prototype.rememberSelection=function(){var e=window.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},Zs.prototype.selectionInEditor=function(){var e=window.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return o(this.div,t)},Zs.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()||this.showSelection(this.prepareSelection(),!0),this.div.focus())},Zs.prototype.blur=function(){this.div.blur()},Zs.prototype.getField=function(){return this.div},Zs.prototype.supportsTouch=function(){return!0},Zs.prototype.receivedFocus=function(){function e(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,e))}var t=this;this.selectionInEditor()?this.pollSelection():hn(this.cm,function(){return t.cm.curOp.selectionChanged=!0}),this.polling.set(this.cm.options.pollInterval,e)},Zs.prototype.selectionChanged=function(){var e=window.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},Zs.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=window.getSelection(),t=this.cm;if(kl&&bl&&this.cm.options.gutters.length&&il(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var r=sl(t,e.anchorNode,e.anchorOffset),n=sl(t,e.focusNode,e.focusOffset);r&&n&&hn(t,function(){bi(t.doc,Rn(r,n),Gl),(r.bad||n.bad)&&(t.curOp.selectionChanged=!0)})}}},Zs.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e=this.cm,t=e.display,r=e.doc.sel.primary(),n=r.from(),i=r.to();if(0==n.ch&&n.line>e.firstLine()&&(n=P(n.line-1,M(e.doc,n.line-1).length)),i.ch==M(e.doc,i.line).text.length&&i.line<e.lastLine()&&(i=P(i.line+1,0)),n.line<t.viewFrom||i.line>t.viewTo-1)return!1;var o,l,s;n.line==t.viewFrom||0==(o=Lr(e,n.line))?(l=W(t.view[0].line),s=t.view[0].node):(l=W(t.view[o].line),s=t.view[o-1].node.nextSibling);var a,u,c=Lr(e,i.line);if(c==t.view.length-1?(a=t.viewTo-1,u=t.lineDiv.lastChild):(a=W(t.view[c+1].line)-1,u=t.view[c+1].node.previousSibling),!s)return!1;for(var f=e.doc.splitLines(ll(e,s,u,l,a)),h=N(e.doc,P(l,0),P(a,M(e.doc,a).text.length));f.length>1&&h.length>1;)if(g(f)==g(h))f.pop(),h.pop(),a--;else{if(f[0]!=h[0])break;f.shift(),h.shift(),l++}for(var d=0,p=0,v=f[0],m=h[0],y=Math.min(v.length,m.length);d<y&&v.charCodeAt(d)==m.charCodeAt(d);)++d;for(var b=g(f),w=g(h),x=Math.min(b.length-(1==f.length?d:0),w.length-(1==h.length?d:0));p<x&&b.charCodeAt(b.length-p-1)==w.charCodeAt(w.length-p-1);)++p;if(1==f.length&&1==h.length&&l==n.line)for(;d&&d>n.ch&&b.charCodeAt(b.length-p-1)==w.charCodeAt(w.length-p-1);)d--,p++;f[f.length-1]=b.slice(0,b.length-p).replace(/^\u200b+/,""),f[0]=f[0].slice(d).replace(/\u200b+$/,"");var C=P(l,d),S=P(a,h.length?g(h).length-p:0);return f.length>1||f[0]||E(C,S)?(Pi(e.doc,f,C,S,"+input"),!0):void 0},Zs.prototype.ensurePolled=function(){this.forceCompositionEnd()},Zs.prototype.reset=function(){this.forceCompositionEnd()},Zs.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},Zs.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()},80))},Zs.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||hn(this.cm,function(){return vn(e.cm)})},Zs.prototype.setUneditable=function(e){e.contentEditable="false"},Zs.prototype.onKeyPress=function(e){0!=e.charCode&&(e.preventDefault(),this.cm.isReadOnly()||dn(this.cm,$o)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},Zs.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},Zs.prototype.onContextMenu=function(){},Zs.prototype.resetPosition=function(){},Zs.prototype.needsContentAttribute=!0;var Qs=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new El,this.hasSelection=!1,this.composing=null};Qs.prototype.init=function(e){function t(e){if(!Me(i,e)){if(i.somethingSelected())_o({lineWise:!1,text:i.getSelections()});else{if(!i.options.lineWiseCopyCut)return;var t=Qo(i);_o({lineWise:!0,text:t.text}),"cut"==e.type?i.setSelections(t.ranges,null,Gl):(n.prevInput="",l.value=t.text.join("\n"),Pl(l))}"cut"==e.type&&(i.state.cutIncoming=!0)}}var r=this,n=this,i=this.cm,o=this.wrapper=el(),l=this.textarea=o.firstChild;e.wrapper.insertBefore(o,e.wrapper.firstChild),Ll&&(l.style.width="0px"),Ql(l,"input",function(){gl&&vl>=9&&r.hasSelection&&(r.hasSelection=null),n.poll()}),Ql(l,"paste",function(e){Me(i,e)||qo(e,i)||(i.state.pasteIncoming=!0,n.fastPoll())}),Ql(l,"cut",t),Ql(l,"copy",t),Ql(e.scroller,"paste",function(t){Ft(e,t)||Me(i,t)||(i.state.pasteIncoming=!0,n.focus())}),Ql(e.lineSpace,"selectstart",function(t){Ft(e,t)||We(t)}),Ql(l,"compositionstart",function(){var e=i.getCursor("from");n.composing&&n.composing.range.clear(),n.composing={start:e,range:i.markText(e,i.getCursor("to"),{className:"CodeMirror-composing"})}}),Ql(l,"compositionend",function(){n.composing&&(n.poll(),n.composing.range.clear(),n.composing=null)})},Qs.prototype.prepareSelection=function(){var e=this.cm,t=e.display,r=e.doc,n=Tr(e);if(e.options.moveInputWithCursor){var i=sr(e,r.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),l=t.lineDiv.getBoundingClientRect();n.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+l.top-o.top)),n.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+l.left-o.left))}return n},Qs.prototype.showSelection=function(e){var t=this.cm.display;r(t.cursorDiv,e.cursors),r(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},Qs.prototype.reset=function(e){if(!this.contextMenuPending&&!this.composing){var t=this.cm;if(t.somethingSelected()){this.prevInput="";var r=t.getSelection();this.textarea.value=r,t.state.focused&&Pl(this.textarea),gl&&vl>=9&&(this.hasSelection=r)}else e||(this.prevInput=this.textarea.value="",gl&&vl>=9&&(this.hasSelection=null))}},Qs.prototype.getField=function(){return this.textarea},Qs.prototype.supportsTouch=function(){return!1},Qs.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!Tl||l()!=this.textarea))try{this.textarea.focus()}catch(e){}},Qs.prototype.blur=function(){this.textarea.blur()},Qs.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},Qs.prototype.receivedFocus=function(){this.slowPoll()},Qs.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},Qs.prototype.fastPoll=function(){function e(){r.poll()||t?(r.pollingFast=!1,r.slowPoll()):(t=!0,r.polling.set(60,e))}var t=!1,r=this;r.pollingFast=!0,r.polling.set(20,e)},Qs.prototype.poll=function(){var e=this,t=this.cm,r=this.textarea,n=this.prevInput;if(this.contextMenuPending||!t.state.focused||ts(r)&&!n&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=r.value;if(i==n&&!t.somethingSelected())return!1;if(gl&&vl>=9&&this.hasSelection===i||Ml&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||n||(n="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var l=0,s=Math.min(n.length,i.length);l<s&&n.charCodeAt(l)==i.charCodeAt(l);)++l;return hn(t,function(){$o(t,i.slice(l),n.length-l,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?r.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},Qs.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},Qs.prototype.onKeyPress=function(){gl&&vl>=9&&(this.hasSelection=null),this.fastPoll()},Qs.prototype.onContextMenu=function(e){function t(){if(null!=l.selectionStart){var e=i.somethingSelected(),t="​"+(e?l.value:"");l.value="⇚",l.value=t,n.prevInput=e?"":"​",l.selectionStart=1,l.selectionEnd=t.length,o.selForContextMenu=i.doc.sel}}function r(){if(n.contextMenuPending=!1,n.wrapper.style.cssText=c,l.style.cssText=u,gl&&vl<9&&o.scrollbars.setScrollTop(o.scroller.scrollTop=a),null!=l.selectionStart){(!gl||gl&&vl<9)&&t();var e=0,r=function(){o.selForContextMenu==i.doc.sel&&0==l.selectionStart&&l.selectionEnd>0&&"​"==n.prevInput?dn(i,Mi)(i):e++<10?o.detectingSelectAll=setTimeout(r,500):(o.selForContextMenu=null,o.input.reset())};o.detectingSelectAll=setTimeout(r,200)}}var n=this,i=n.cm,o=i.display,l=n.textarea,s=Sr(i,e),a=o.scroller.scrollTop;if(s&&!wl){i.options.resetSelectionOnContextMenu&&-1==i.doc.sel.contains(s)&&dn(i,bi)(i.doc,Rn(s),Gl);var u=l.style.cssText,c=n.wrapper.style.cssText;n.wrapper.style.cssText="position: absolute";var f=n.wrapper.getBoundingClientRect();l.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-f.top-5)+"px; left: "+(e.clientX-f.left-5)+"px;\n      z-index: 1000; background: "+(gl?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);";var h;if(ml&&(h=window.scrollY),o.input.focus(),ml&&window.scrollTo(null,h),o.input.reset(),i.somethingSelected()||(l.value=n.prevInput=" "),n.contextMenuPending=!0,o.selForContextMenu=i.doc.sel,clearTimeout(o.detectingSelectAll),gl&&vl>=9&&t(),Hl){Fe(e);var d=function(){ke(window,"mouseup",d),setTimeout(r,20)};Ql(window,"mouseup",d)}else setTimeout(r,50)}},Qs.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e},Qs.prototype.setUneditable=function(){},Qs.prototype.needsContentAttribute=!1,function(e){function t(t,n,i,o){e.defaults[t]=n,i&&(r[t]=o?function(e,t,r){r!=Xs&&i(e,t,r)}:i)}var r=e.optionHandlers;e.defineOption=t,e.Init=Xs,t("value","",function(e,t){return e.setValue(t)},!0),t("mode",null,function(e,t){e.doc.modeOption=t,jn(e)},!0),t("indentUnit",2,jn,!0),t("indentWithTabs",!1),t("smartIndent",!0),t("tabSize",4,function(e){Xn(e),er(e),vn(e)},!0),t("lineSeparator",null,function(e,t){if(e.doc.lineSep=t,t){var r=[],n=e.doc.first;e.doc.iter(function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,r.push(P(n,o))}n++});for(var i=r.length-1;i>=0;i--)Pi(e.doc,t,r[i],P(r[i].line,r[i].ch+t.length))}}),t("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b-\u200f\u2028\u2029\ufeff]/g,function(e,t,r){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),r!=Xs&&e.refresh()}),t("specialCharPlaceholder",at,function(e){return e.refresh()},!0),t("electricChars",!0),t("inputStyle",Tl?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),t("spellcheck",!1,function(e,t){return e.getInputField().spellcheck=t},!0),t("rtlMoveVisually",!Ol),t("wholeLineUpdateBefore",!0),t("theme","default",function(e){Go(e),Uo(e)},!0),t("keyMap","default",function(e,t,r){var n=uo(t),i=r!=Xs&&uo(r);i&&i.detach&&i.detach(e,n),n.attach&&n.attach(e,i||null)}),t("extraKeys",null),t("configureMouse",null),t("lineWrapping",!1,Ko,!0),t("gutters",[],function(e){Fn(e.options),Uo(e)},!0),t("fixedGutter",!0,function(e,t){e.display.gutters.style.left=t?wr(e.display)+"px":"0",e.refresh()},!0),t("coverGutterNextToScrollbar",!1,function(e){return en(e)},!0),t("scrollbarStyle","native",function(e){rn(e),en(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)},!0),t("lineNumbers",!1,function(e){Fn(e.options),Uo(e)},!0),t("firstLineNumber",1,Uo,!0),t("lineNumberFormatter",function(e){return e},Uo,!0),t("showCursorWhenSelecting",!1,kr,!0),t("resetSelectionOnContextMenu",!0),t("lineWiseCopyCut",!0),t("pasteLinesPerSelection",!0),t("readOnly",!1,function(e,t){"nocursor"==t&&(Fr(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)}),t("disableInput",!1,function(e,t){t||e.display.input.reset()},!0),t("dragDrop",!0,Vo),t("allowDropFileTypes",null),t("cursorBlinkRate",530),t("cursorScrollMargin",0),t("cursorHeight",1,kr,!0),t("singleCursorHeightPerLine",!0,kr,!0),t("workTime",100),t("workDelay",100),t("flattenSpans",!0,Xn,!0),t("addModeClass",!1,Xn,!0),t("pollInterval",100),t("undoDepth",200,function(e,t){return e.doc.history.undoDepth=t}),t("historyEventDelay",1250),t("viewportMargin",10,function(e){return e.refresh()},!0),t("maxHighlightLength",1e4,Xn,!0),t("moveInputWithCursor",!0,function(e,t){t||e.display.input.resetPosition()}),t("tabindex",null,function(e,t){return e.display.input.getField().tabIndex=t||""}),t("autofocus",null),t("direction","ltr",function(e,t){return e.doc.setDirection(t)},!0)}(jo),function(e){var t=e.optionHandlers,r=e.helpers={};e.prototype={constructor:e,focus:function(){window.focus(),this.display.input.focus()},setOption:function(e,r){var n=this.options,i=n[e];n[e]==r&&"mode"!=e||(n[e]=r,t.hasOwnProperty(e)&&dn(this,t[e])(this,r,i),Te(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](uo(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,r=0;r<t.length;++r)if(t[r]==e||t[r].name==e)return t.splice(r,1),!0},addOverlay:pn(function(t,r){var n=t.token?t:e.getMode(this.options,t);if(n.startState)throw new Error("Overlays may not be stateful.");m(this.state.overlays,{mode:n,modeSpec:t,opaque:r&&r.opaque,priority:r&&r.priority||0},function(e){return e.priority}),this.state.modeGen++,vn(this)}),removeOverlay:pn(function(e){for(var t=this,r=this.state.overlays,n=0;n<r.length;++n){var i=r[n].modeSpec;if(i==e||"string"==typeof e&&i.name==e)return r.splice(n,1),t.state.modeGen++,void vn(t)}}),indentLine:pn(function(e,t,r){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),H(this.doc,e)&&Yo(this,e,t,r)}),indentSelection:pn(function(e){for(var t=this,r=this.doc.sel.ranges,n=-1,i=0;i<r.length;i++){var o=r[i];if(o.empty())o.head.line>n&&(Yo(t,o.head.line,e,!0),n=o.head.line,i==t.doc.sel.primIndex&&jr(t));else{var l=o.from(),s=o.to(),a=Math.max(n,l.line);n=Math.min(t.lastLine(),s.line-(s.ch?0:1))+1;for(var u=a;u<n;++u)Yo(t,u,e);var c=t.doc.sel.ranges;0==l.ch&&r.length==c.length&&c[i].from().ch>0&&gi(t.doc,i,new Ts(l,c[i].to()),Gl)}}}),getTokenAt:function(e,t){return Je(this,e,t)},getLineTokens:function(e,t){return Je(this,P(e),t,!0)},getTokenTypeAt:function(e){e=U(this.doc,e);var t,r=_e(this,M(this.doc,e.line)),n=0,i=(r.length-1)/2,o=e.ch;if(0==o)t=r[2];else for(;;){var l=n+i>>1;if((l?r[2*l-1]:0)>=o)i=l;else{if(!(r[2*l+1]<o)){t=r[2*l+2];break}n=l+1}}var s=t?t.indexOf("overlay "):-1;return s<0?t:0==s?null:t.slice(0,s-1)},getModeAt:function(t){var r=this.doc.mode;return r.innerMode?e.innerMode(r,this.getTokenAt(t).state).mode:r},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var n=this,i=[];if(!r.hasOwnProperty(t))return i;var o=r[t],l=this.getModeAt(e);if("string"==typeof l[t])o[l[t]]&&i.push(o[l[t]]);else if(l[t])for(var s=0;s<l[t].length;s++){var a=o[l[t][s]];a&&i.push(a)}else l.helperType&&o[l.helperType]?i.push(o[l.helperType]):o[l.name]&&i.push(o[l.name]);for(var u=0;u<o._global.length;u++){var c=o._global[u];c.pred(l,n)&&-1==h(i,c.val)&&i.push(c.val)}return i},getStateAfter:function(e,t){var r=this.doc;return e=G(r,null==e?r.first+r.size-1:e),$e(this,e+1,t).state},cursorCoords:function(e,t){var r,n=this.doc.sel.primary();return r=null==e?n.head:"object"==typeof e?U(this.doc,e):e?n.from():n.to(),sr(this,r,t||"page")},charCoords:function(e,t){return lr(this,U(this.doc,e),t||"page")},coordsChar:function(e,t){return e=or(this,e,t||"page"),cr(this,e.left,e.top)},lineAtHeight:function(e,t){return e=or(this,{top:e,left:0},t||"page").top,D(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,r){var n,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),n=M(this.doc,e)}else n=e;return ir(this,n,{top:0,left:0},t||"page",r||i).top+(i?this.doc.height-ye(n):0)},defaultTextHeight:function(){return mr(this.display)},defaultCharWidth:function(){return yr(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,r,n,i){var o=this.display,l=(e=sr(this,U(this.doc,e))).bottom,s=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),o.sizer.appendChild(t),"over"==n)l=e.top;else if("above"==n||"near"==n){var a=Math.max(o.wrapper.clientHeight,this.doc.height),u=Math.max(o.sizer.clientWidth,o.lineSpace.clientWidth);("above"==n||e.bottom+t.offsetHeight>a)&&e.top>t.offsetHeight?l=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=a&&(l=e.bottom),s+t.offsetWidth>u&&(s=u-t.offsetWidth)}t.style.top=l+"px",t.style.left=t.style.right="","right"==i?(s=o.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?s=0:"middle"==i&&(s=(o.sizer.clientWidth-t.offsetWidth)/2),t.style.left=s+"px"),r&&Ur(this,{left:s,top:l,right:s+t.offsetWidth,bottom:l+t.offsetHeight})},triggerOnKeyDown:pn(Lo),triggerOnKeyPress:pn(Mo),triggerOnKeyUp:To,triggerOnMouseDown:pn(Oo),execCommand:function(e){if(Bs.hasOwnProperty(e))return Bs[e].call(null,this)},triggerElectric:pn(function(e){Zo(this,e)}),findPosH:function(e,t,r,n){var i=this,o=1;t<0&&(o=-1,t=-t);for(var l=U(this.doc,e),s=0;s<t&&!(l=tl(i.doc,l,o,r,n)).hitSide;++s);return l},moveH:pn(function(e,t){var r=this;this.extendSelectionsBy(function(n){return r.display.shift||r.doc.extend||n.empty()?tl(r.doc,n.head,e,t,r.options.rtlMoveVisually):e<0?n.from():n.to()},Vl)}),deleteH:pn(function(e,t){var r=this.doc.sel,n=this.doc;r.somethingSelected()?n.replaceSelection("",null,"+delete"):co(this,function(r){var i=tl(n,r.head,e,t,!1);return e<0?{from:i,to:r.head}:{from:r.head,to:i}})}),findPosV:function(e,t,r,n){var i=this,o=1,l=n;t<0&&(o=-1,t=-t);for(var s=U(this.doc,e),a=0;a<t;++a){var u=sr(i,s,"div");if(null==l?l=u.left:u.left=l,(s=rl(i,u,o,r)).hitSide)break}return s},moveV:pn(function(e,t){var r=this,n=this.doc,i=[],o=!this.display.shift&&!n.extend&&n.sel.somethingSelected();if(n.extendSelectionsBy(function(l){if(o)return e<0?l.from():l.to();var s=sr(r,l.head,"div");null!=l.goalColumn&&(s.left=l.goalColumn),i.push(s.left);var a=rl(r,s,e,t);return"page"==t&&l==n.sel.primary()&&Kr(r,lr(r,a,"div").top-s.top),a},Vl),i.length)for(var l=0;l<n.sel.ranges.length;l++)n.sel.ranges[l].goalColumn=i[l]}),findWordAt:function(e){var t=M(this.doc,e.line).text,r=e.ch,n=e.ch;if(t){var i=this.getHelper(e,"wordChars");"before"!=e.sticky&&n!=t.length||!r?++n:--r;for(var o=t.charAt(r),l=x(o,i)?function(e){return x(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!x(e)};r>0&&l(t.charAt(r-1));)--r;for(;n<t.length&&l(t.charAt(n));)++n}return new Ts(P(e.line,r),P(e.line,n))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?s(this.display.cursorDiv,"CodeMirror-overwrite"):Fl(this.display.cursorDiv,"CodeMirror-overwrite"),Te(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==l()},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:pn(function(e,t){Xr(this,e,t)}),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-It(this)-this.display.barHeight,width:e.scrollWidth-It(this)-this.display.barWidth,clientHeight:Bt(this),clientWidth:Rt(this)}},scrollIntoView:pn(function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:P(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?Yr(this,e):$r(this,e.from,e.to,e.margin)}),setSize:pn(function(e,t){var r=this,n=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&Jt(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){mn(r,i,"widget");break}++i}),this.curOp.forceUpdate=!0,Te(this,"refresh",this)}),operation:function(e){return hn(this,e)},startOperation:function(){return nn(this)},endOperation:function(){return on(this)},refresh:pn(function(){var e=this.display.cachedTextHeight;vn(this),this.curOp.forceUpdate=!0,er(this),Xr(this,this.doc.scrollLeft,this.doc.scrollTop),Wn(this),(null==e||Math.abs(e-mr(this.display))>.5)&&Cr(this),Te(this,"refresh",this)}),swapDoc:pn(function(e){var t=this.doc;return t.cm=null,qn(this,e),er(this),this.display.input.reset(),Xr(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,bt(this,"swapDoc",this,t),t}),getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},Ae(e),e.registerHelper=function(t,n,i){r.hasOwnProperty(t)||(r[t]=e[t]={_global:[]}),r[t][n]=i},e.registerGlobalHelper=function(t,n,i,o){e.registerHelper(t,n,o),r[t]._global.push({pred:i,val:o})}}(jo);var Js="iter insert remove copy getEditor constructor".split(" ");for(var ea in Ds.prototype)Ds.prototype.hasOwnProperty(ea)&&h(Js,ea)<0&&(jo.prototype[ea]=function(e){return function(){return e.apply(this.doc,arguments)}}(Ds.prototype[ea]));return Ae(Ds),jo.inputStyles={textarea:Qs,contenteditable:Zs},jo.defineMode=function(e){jo.defaults.mode||"null"==e||(jo.defaults.mode=e),Be.apply(this,arguments)},jo.defineMIME=function(e,t){os[e]=t},jo.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),jo.defineMIME("text/plain","null"),jo.defineExtension=function(e,t){jo.prototype[e]=t},jo.defineDocExtension=function(e,t){Ds.prototype[e]=t},jo.fromTextArea=function(e,t){function r(){e.value=a.getValue()}if(t=t?c(t):{},t.value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var n=l();t.autofocus=n==e||null!=e.getAttribute("autofocus")&&n==document.body}var i;if(e.form&&(Ql(e.form,"submit",r),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var s=o.submit=function(){r(),o.submit=i,o.submit(),o.submit=s}}catch(e){}}t.finishInit=function(t){t.save=r,t.getTextArea=function(){return e},t.toTextArea=function(){t.toTextArea=isNaN,r(),e.parentNode.removeChild(t.getWrapperElement()),e.style.display="",e.form&&(ke(e.form,"submit",r),"function"==typeof e.form.submit&&(e.form.submit=i))}},e.style.display="none";var a=jo(function(t){return e.parentNode.insertBefore(t,e.nextSibling)},t);return a},function(e){e.off=ke,e.on=Ql,e.wheelEventPixels=En,e.Doc=Ds,e.splitLines=es,e.countColumn=f,e.findColumn=d,e.isWordChar=w,e.Pass=Bl,e.signal=Te,e.Line=fs,e.changeEnd=Bn,e.scrollbarModel=ws,e.Pos=P,e.cmpPos=E,e.modes=is,e.mimeModes=os,e.resolveMode=Ge,e.getMode=Ue,e.modeExtensions=ls,e.extendMode=Ve,e.copyState=Ke,e.startState=Xe,e.innerMode=je,e.commands=Bs,e.keyMap=Rs,e.keyName=ao,e.isModifierKey=lo,e.lookupKey=oo,e.normalizeKeyMap=io,e.StringStream=ss,e.SharedTextMarker=As,e.TextMarker=Os,e.LineWidget=Ms,e.e_preventDefault=We,e.e_stopPropagation=De,e.e_stop=Fe,e.addClass=s,e.contains=o,e.rmClass=Fl,e.keyNames=Ps}(jo),jo.version="5.31.0",jo});

},{}],2:[function(require,module,exports){
"use strict";function identity(t){return t}function factory(t,e,n){function o(t,e){var n=f.hasOwnProperty(e)?f[e]:null;_.hasOwnProperty(e)&&_invariant("OVERRIDE_BASE"===n,"ReactClassInterface: You are attempting to override `%s` from your class specification. Ensure that your method names do not overlap with React methods.",e),t&&_invariant("DEFINE_MANY"===n||"DEFINE_MANY_MERGED"===n,"ReactClassInterface: You are attempting to define `%s` on your component more than once. This conflict may be due to a mixin.",e)}function i(t,n){if(n){_invariant("function"!=typeof n,"ReactClass: You're attempting to use a component class or function as a mixin. Instead, just use a regular object."),_invariant(!e(n),"ReactClass: You're attempting to use a component as a mixin. Instead, just use a regular object.");var i=t.prototype,a=i.__reactAutoBindPairs;n.hasOwnProperty(MIXINS_KEY)&&y.mixins(t,n.mixins);for(var r in n)if(n.hasOwnProperty(r)&&r!==MIXINS_KEY){var c=n[r],u=i.hasOwnProperty(r);if(o(u,r),y.hasOwnProperty(r))y[r](t,c);else{var l=f.hasOwnProperty(r);if("function"==typeof c&&!l&&!u&&!1!==n.autobind)a.push(r,c),i[r]=c;else if(u){var E=f[r];_invariant(l&&("DEFINE_MANY_MERGED"===E||"DEFINE_MANY"===E),"ReactClass: Unexpected spec policy %s for key %s when mixing in component specs.",E,r),"DEFINE_MANY_MERGED"===E?i[r]=s(i[r],c):"DEFINE_MANY"===E&&(i[r]=p(i[r],c))}else i[r]=c}}}else;}function a(t,e){if(e)for(var n in e){var o=e[n];e.hasOwnProperty(n)&&(_invariant(!(n in y),'ReactClass: You are attempting to define a reserved property, `%s`, that shouldn\'t be on the "statics" key. Define it as an instance property instead; it will still be accessible on the constructor.',n),_invariant(!(n in t),"ReactClass: You are attempting to define `%s` on your component more than once. This conflict may be due to a mixin.",n),t[n]=o)}}function r(t,e){_invariant(t&&e&&"object"==typeof t&&"object"==typeof e,"mergeIntoWithNoDuplicateKeys(): Cannot merge non-objects.");for(var n in e)e.hasOwnProperty(n)&&(_invariant(void 0===t[n],"mergeIntoWithNoDuplicateKeys(): Tried to merge two objects with the same key: `%s`. This conflict may be due to a mixin; in particular, this may be caused by two getInitialState() or getDefaultProps() methods returning objects with clashing keys.",n),t[n]=e[n]);return t}function s(t,e){return function(){var n=t.apply(this,arguments),o=e.apply(this,arguments);if(null==n)return o;if(null==o)return n;var i={};return r(i,n),r(i,o),i}}function p(t,e){return function(){t.apply(this,arguments),e.apply(this,arguments)}}function c(t,e){var n=e.bind(t);return n}function u(t){for(var e=t.__reactAutoBindPairs,n=0;n<e.length;n+=2){var o=e[n],i=e[n+1];t[o]=c(t,i)}}var l=[],f={mixins:"DEFINE_MANY",statics:"DEFINE_MANY",propTypes:"DEFINE_MANY",contextTypes:"DEFINE_MANY",childContextTypes:"DEFINE_MANY",getDefaultProps:"DEFINE_MANY_MERGED",getInitialState:"DEFINE_MANY_MERGED",getChildContext:"DEFINE_MANY_MERGED",render:"DEFINE_ONCE",componentWillMount:"DEFINE_MANY",componentDidMount:"DEFINE_MANY",componentWillReceiveProps:"DEFINE_MANY",shouldComponentUpdate:"DEFINE_ONCE",componentWillUpdate:"DEFINE_MANY",componentDidUpdate:"DEFINE_MANY",componentWillUnmount:"DEFINE_MANY",updateComponent:"OVERRIDE_BASE"},y={displayName:function(t,e){t.displayName=e},mixins:function(t,e){if(e)for(var n=0;n<e.length;n++)i(t,e[n])},childContextTypes:function(t,e){t.childContextTypes=_assign({},t.childContextTypes,e)},contextTypes:function(t,e){t.contextTypes=_assign({},t.contextTypes,e)},getDefaultProps:function(t,e){t.getDefaultProps?t.getDefaultProps=s(t.getDefaultProps,e):t.getDefaultProps=e},propTypes:function(t,e){t.propTypes=_assign({},t.propTypes,e)},statics:function(t,e){a(t,e)},autobind:function(){}},E={componentDidMount:function(){this.__isMounted=!0}},m={componentWillUnmount:function(){this.__isMounted=!1}},_={replaceState:function(t,e){this.updater.enqueueReplaceState(this,t,e)},isMounted:function(){return!!this.__isMounted}},h=function(){};return _assign(h.prototype,t.prototype,_),function(t){var e=identity(function(t,o,i){this.__reactAutoBindPairs.length&&u(this),this.props=t,this.context=o,this.refs=emptyObject,this.updater=i||n,this.state=null;var a=this.getInitialState?this.getInitialState():null;_invariant("object"==typeof a&&!Array.isArray(a),"%s.getInitialState(): must return an object or null",e.displayName||"ReactCompositeComponent"),this.state=a});e.prototype=new h,e.prototype.constructor=e,e.prototype.__reactAutoBindPairs=[],l.forEach(i.bind(null,e)),i(e,E),i(e,t),i(e,m),e.getDefaultProps&&(e.defaultProps=e.getDefaultProps()),_invariant(e.prototype.render,"createClass(...): Class specification must implement a `render` method.");for(var o in f)e.prototype[o]||(e.prototype[o]=null);return e}}var _assign=require("object-assign"),emptyObject=require("fbjs/lib/emptyObject"),_invariant=require("fbjs/lib/invariant"),warning,MIXINS_KEY="mixins",ReactPropTypeLocationNames;ReactPropTypeLocationNames={},module.exports=factory;

},{"fbjs/lib/emptyObject":4,"fbjs/lib/invariant":5,"object-assign":35}],3:[function(require,module,exports){
"use strict";var React=require("react"),factory=require("./factory");if(void 0===React)throw Error("create-react-class could not find the React object. If you are using script tags, make sure that React is being loaded before create-react-class.");var ReactNoopUpdateQueue=(new React.Component).updater;module.exports=factory(React.Component,React.isValidElement,ReactNoopUpdateQueue);

},{"./factory":2,"react":"react"}],4:[function(require,module,exports){
"use strict";var emptyObject={};module.exports=emptyObject;

},{}],5:[function(require,module,exports){
"use strict";function invariant(e,n,r,i,a,o,t,d){if(validateFormat(n),!e){var f;if(void 0===n)f=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var l=[r,i,a,o,t,d],v=0;(f=new Error(n.replace(/%s/g,function(){return l[v++]}))).name="Invariant Violation"}throw f.framesToPop=1,f}}var validateFormat=function(e){};module.exports=invariant;

},{}],6:[function(require,module,exports){
"use strict";var emptyFunction=require("./emptyFunction"),EventListener={listen:function(e,t,n){return e.addEventListener?(e.addEventListener(t,n,!1),{remove:function(){e.removeEventListener(t,n,!1)}}):e.attachEvent?(e.attachEvent("on"+t,n),{remove:function(){e.detachEvent("on"+t,n)}}):void 0},capture:function(e,t,n){return e.addEventListener?(e.addEventListener(t,n,!0),{remove:function(){e.removeEventListener(t,n,!0)}}):{remove:emptyFunction}},registerDefault:function(){}};module.exports=EventListener;

},{"./emptyFunction":9}],7:[function(require,module,exports){
"use strict";var canUseDOM=!("undefined"==typeof window||!window.document||!window.document.createElement),ExecutionEnvironment={canUseDOM:canUseDOM,canUseWorkers:"undefined"!=typeof Worker,canUseEventListeners:canUseDOM&&!(!window.addEventListener&&!window.attachEvent),canUseViewport:canUseDOM&&!!window.screen,isInWorker:!canUseDOM};module.exports=ExecutionEnvironment;

},{}],8:[function(require,module,exports){
"use strict";function containsNode(o,e){return!(!o||!e)&&(o===e||!isTextNode(o)&&(isTextNode(e)?containsNode(o,e.parentNode):"contains"in o?o.contains(e):!!o.compareDocumentPosition&&!!(16&o.compareDocumentPosition(e))))}var isTextNode=require("./isTextNode");module.exports=containsNode;

},{"./isTextNode":15}],9:[function(require,module,exports){
"use strict";function makeEmptyFunction(t){return function(){return t}}var emptyFunction=function(){};emptyFunction.thatReturns=makeEmptyFunction,emptyFunction.thatReturnsFalse=makeEmptyFunction(!1),emptyFunction.thatReturnsTrue=makeEmptyFunction(!0),emptyFunction.thatReturnsNull=makeEmptyFunction(null),emptyFunction.thatReturnsThis=function(){return this},emptyFunction.thatReturnsArgument=function(t){return t},module.exports=emptyFunction;

},{}],10:[function(require,module,exports){
"use strict";var emptyObject={};module.exports=emptyObject;

},{}],11:[function(require,module,exports){
"use strict";function focusNode(o){try{o.focus()}catch(o){}}module.exports=focusNode;

},{}],12:[function(require,module,exports){
"use strict";function getActiveElement(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}module.exports=getActiveElement;

},{}],13:[function(require,module,exports){
"use strict";function invariant(e,n,r,i,a,o,t,d){if(validateFormat(n),!e){var f;if(void 0===n)f=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var l=[r,i,a,o,t,d],v=0;(f=new Error(n.replace(/%s/g,function(){return l[v++]}))).name="Invariant Violation"}throw f.framesToPop=1,f}}var validateFormat=function(e){};module.exports=invariant;

},{}],14:[function(require,module,exports){
"use strict";function isNode(e){var o=(e?e.ownerDocument||e:document).defaultView||window;return!(!e||!("function"==typeof o.Node?e instanceof o.Node:"object"==typeof e&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName))}module.exports=isNode;

},{}],15:[function(require,module,exports){
"use strict";function isTextNode(e){return isNode(e)&&3==e.nodeType}var isNode=require("./isNode");module.exports=isTextNode;

},{"./isNode":14}],16:[function(require,module,exports){
"use strict";function is(t,e){return t===e?0!==t||0!==e||1/t==1/e:t!==t&&e!==e}function shallowEqual(t,e){if(is(t,e))return!0;if("object"!=typeof t||null===t||"object"!=typeof e||null===e)return!1;var r=Object.keys(t),n=Object.keys(e);if(r.length!==n.length)return!1;for(var l=0;l<r.length;l++)if(!hasOwnProperty.call(e,r[l])||!is(t[r[l]],e[r[l]]))return!1;return!0}var hasOwnProperty=Object.prototype.hasOwnProperty;module.exports=shallowEqual;

},{}],17:[function(require,module,exports){
"use strict";var REACT_STATICS={childContextTypes:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,mixins:!0,propTypes:!0,type:!0},KNOWN_STATICS={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},defineProperty=Object.defineProperty,getOwnPropertyNames=Object.getOwnPropertyNames,getOwnPropertySymbols=Object.getOwnPropertySymbols,getOwnPropertyDescriptor=Object.getOwnPropertyDescriptor,getPrototypeOf=Object.getPrototypeOf,objectPrototype=getPrototypeOf&&getPrototypeOf(Object);module.exports=function e(t,r,o){if("string"!=typeof r){if(objectPrototype){var p=getPrototypeOf(r);p&&p!==objectPrototype&&e(t,p,o)}var y=getOwnPropertyNames(r);getOwnPropertySymbols&&(y=y.concat(getOwnPropertySymbols(r)));for(var n=0;n<y.length;++n){var O=y[n];if(!(REACT_STATICS[O]||KNOWN_STATICS[O]||o&&o[O])){var P=getOwnPropertyDescriptor(r,O);try{defineProperty(t,O,P)}catch(e){}}}return t}return t};

},{}],18:[function(require,module,exports){
"use strict";var invariant=function(e,n,r,i,o,a,t,f){if(!e){var s;if(void 0===n)s=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var d=[r,i,o,a,t,f],l=0;(s=new Error(n.replace(/%s/g,function(){return d[l++]}))).name="Invariant Violation"}throw s.framesToPop=1,s}};module.exports=invariant;

},{}],19:[function(require,module,exports){
(function (global){
function debounce(e,t,r){function o(t){var r=l,o=b;return l=b=void 0,y=t,v=e.apply(o,r)}function n(e){return y=e,m=setTimeout(a,t),p?o(e):v}function i(e){var r=t-(e-j);return O?nativeMin(r,s-(e-y)):r}function u(e){var r=e-j;return void 0===j||r>=t||r<0||O&&e-y>=s}function a(){var e=now();if(u(e))return f(e);m=setTimeout(a,i(e))}function f(e){return m=void 0,d&&l?o(e):(l=b=void 0,v)}function c(){var e=now(),r=u(e);if(l=arguments,b=this,j=e,r){if(void 0===m)return n(j);if(O)return m=setTimeout(a,t),o(j)}return void 0===m&&(m=setTimeout(a,t)),v}var l,b,s,v,m,j,y=0,p=!1,O=!1,d=!0;if("function"!=typeof e)throw new TypeError(FUNC_ERROR_TEXT);return t=toNumber(t)||0,isObject(r)&&(p=!!r.leading,s=(O="maxWait"in r)?nativeMax(toNumber(r.maxWait)||0,t):s,d="trailing"in r?!!r.trailing:d),c.cancel=function(){void 0!==m&&clearTimeout(m),y=0,l=j=b=m=void 0},c.flush=function(){return void 0===m?v:f(now())},c}function isObject(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function isObjectLike(e){return!!e&&"object"==typeof e}function isSymbol(e){return"symbol"==typeof e||isObjectLike(e)&&objectToString.call(e)==symbolTag}function toNumber(e){if("number"==typeof e)return e;if(isSymbol(e))return NAN;if(isObject(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=isObject(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(reTrim,"");var r=reIsBinary.test(e);return r||reIsOctal.test(e)?freeParseInt(e.slice(2),r?2:8):reIsBadHex.test(e)?NAN:+e}var FUNC_ERROR_TEXT="Expected a function",NAN=NaN,symbolTag="[object Symbol]",reTrim=/^\s+|\s+$/g,reIsBadHex=/^[-+]0x[0-9a-f]+$/i,reIsBinary=/^0b[01]+$/i,reIsOctal=/^0o[0-7]+$/i,freeParseInt=parseInt,freeGlobal="object"==typeof global&&global&&global.Object===Object&&global,freeSelf="object"==typeof self&&self&&self.Object===Object&&self,root=freeGlobal||freeSelf||Function("return this")(),objectProto=Object.prototype,objectToString=objectProto.toString,nativeMax=Math.max,nativeMin=Math.min,now=function(){return root.Date.now()};module.exports=debounce;

}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})

},{}],20:[function(require,module,exports){
(function (global){
function arrayFilter(e,t){for(var a=-1,r=null==e?0:e.length,n=0,o=[];++a<r;){var s=e[a];t(s,a,e)&&(o[n++]=s)}return o}function arrayPush(e,t){for(var a=-1,r=t.length,n=e.length;++a<r;)e[n+a]=t[a];return e}function arraySome(e,t){for(var a=-1,r=null==e?0:e.length;++a<r;)if(t(e[a],a,e))return!0;return!1}function baseTimes(e,t){for(var a=-1,r=Array(e);++a<e;)r[a]=t(a);return r}function baseUnary(e){return function(t){return e(t)}}function cacheHas(e,t){return e.has(t)}function getValue(e,t){return null==e?void 0:e[t]}function mapToArray(e){var t=-1,a=Array(e.size);return e.forEach(function(e,r){a[++t]=[r,e]}),a}function overArg(e,t){return function(a){return e(t(a))}}function setToArray(e){var t=-1,a=Array(e.size);return e.forEach(function(e){a[++t]=e}),a}function Hash(e){var t=-1,a=null==e?0:e.length;for(this.clear();++t<a;){var r=e[t];this.set(r[0],r[1])}}function hashClear(){this.__data__=nativeCreate?nativeCreate(null):{},this.size=0}function hashDelete(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}function hashGet(e){var t=this.__data__;if(nativeCreate){var a=t[e];return a===HASH_UNDEFINED?void 0:a}return hasOwnProperty.call(t,e)?t[e]:void 0}function hashHas(e){var t=this.__data__;return nativeCreate?void 0!==t[e]:hasOwnProperty.call(t,e)}function hashSet(e,t){var a=this.__data__;return this.size+=this.has(e)?0:1,a[e]=nativeCreate&&void 0===t?HASH_UNDEFINED:t,this}function ListCache(e){var t=-1,a=null==e?0:e.length;for(this.clear();++t<a;){var r=e[t];this.set(r[0],r[1])}}function listCacheClear(){this.__data__=[],this.size=0}function listCacheDelete(e){var t=this.__data__,a=assocIndexOf(t,e);return!(a<0)&&(a==t.length-1?t.pop():splice.call(t,a,1),--this.size,!0)}function listCacheGet(e){var t=this.__data__,a=assocIndexOf(t,e);return a<0?void 0:t[a][1]}function listCacheHas(e){return assocIndexOf(this.__data__,e)>-1}function listCacheSet(e,t){var a=this.__data__,r=assocIndexOf(a,e);return r<0?(++this.size,a.push([e,t])):a[r][1]=t,this}function MapCache(e){var t=-1,a=null==e?0:e.length;for(this.clear();++t<a;){var r=e[t];this.set(r[0],r[1])}}function mapCacheClear(){this.size=0,this.__data__={hash:new Hash,map:new(Map||ListCache),string:new Hash}}function mapCacheDelete(e){var t=getMapData(this,e).delete(e);return this.size-=t?1:0,t}function mapCacheGet(e){return getMapData(this,e).get(e)}function mapCacheHas(e){return getMapData(this,e).has(e)}function mapCacheSet(e,t){var a=getMapData(this,e),r=a.size;return a.set(e,t),this.size+=a.size==r?0:1,this}function SetCache(e){var t=-1,a=null==e?0:e.length;for(this.__data__=new MapCache;++t<a;)this.add(e[t])}function setCacheAdd(e){return this.__data__.set(e,HASH_UNDEFINED),this}function setCacheHas(e){return this.__data__.has(e)}function Stack(e){var t=this.__data__=new ListCache(e);this.size=t.size}function stackClear(){this.__data__=new ListCache,this.size=0}function stackDelete(e){var t=this.__data__,a=t.delete(e);return this.size=t.size,a}function stackGet(e){return this.__data__.get(e)}function stackHas(e){return this.__data__.has(e)}function stackSet(e,t){var a=this.__data__;if(a instanceof ListCache){var r=a.__data__;if(!Map||r.length<LARGE_ARRAY_SIZE-1)return r.push([e,t]),this.size=++a.size,this;a=this.__data__=new MapCache(r)}return a.set(e,t),this.size=a.size,this}function arrayLikeKeys(e,t){var a=isArray(e),r=!a&&isArguments(e),n=!a&&!r&&isBuffer(e),o=!a&&!r&&!n&&isTypedArray(e),s=a||r||n||o,i=s?baseTimes(e.length,String):[],c=i.length;for(var u in e)!t&&!hasOwnProperty.call(e,u)||s&&("length"==u||n&&("offset"==u||"parent"==u)||o&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||isIndex(u,c))||i.push(u);return i}function assocIndexOf(e,t){for(var a=e.length;a--;)if(eq(e[a][0],t))return a;return-1}function baseGetAllKeys(e,t,a){var r=t(e);return isArray(e)?r:arrayPush(r,a(e))}function baseGetTag(e){return null==e?void 0===e?undefinedTag:nullTag:symToStringTag&&symToStringTag in Object(e)?getRawTag(e):objectToString(e)}function baseIsArguments(e){return isObjectLike(e)&&baseGetTag(e)==argsTag}function baseIsEqual(e,t,a,r,n){return e===t||(null==e||null==t||!isObjectLike(e)&&!isObjectLike(t)?e!==e&&t!==t:baseIsEqualDeep(e,t,a,r,baseIsEqual,n))}function baseIsEqualDeep(e,t,a,r,n,o){var s=isArray(e),i=isArray(t),c=s?arrayTag:getTag(e),u=i?arrayTag:getTag(t),g=(c=c==argsTag?objectTag:c)==objectTag,l=(u=u==argsTag?objectTag:u)==objectTag,y=c==u;if(y&&isBuffer(e)){if(!isBuffer(t))return!1;s=!0,g=!1}if(y&&!g)return o||(o=new Stack),s||isTypedArray(e)?equalArrays(e,t,a,r,n,o):equalByTag(e,t,c,a,r,n,o);if(!(a&COMPARE_PARTIAL_FLAG)){var f=g&&hasOwnProperty.call(e,"__wrapped__"),p=l&&hasOwnProperty.call(t,"__wrapped__");if(f||p){var h=f?e.value():e,T=p?t.value():t;return o||(o=new Stack),n(h,T,a,r,o)}}return!!y&&(o||(o=new Stack),equalObjects(e,t,a,r,n,o))}function baseIsNative(e){return!(!isObject(e)||isMasked(e))&&(isFunction(e)?reIsNative:reIsHostCtor).test(toSource(e))}function baseIsTypedArray(e){return isObjectLike(e)&&isLength(e.length)&&!!typedArrayTags[baseGetTag(e)]}function baseKeys(e){if(!isPrototype(e))return nativeKeys(e);var t=[];for(var a in Object(e))hasOwnProperty.call(e,a)&&"constructor"!=a&&t.push(a);return t}function equalArrays(e,t,a,r,n,o){var s=a&COMPARE_PARTIAL_FLAG,i=e.length,c=t.length;if(i!=c&&!(s&&c>i))return!1;var u=o.get(e);if(u&&o.get(t))return u==t;var g=-1,l=!0,y=a&COMPARE_UNORDERED_FLAG?new SetCache:void 0;for(o.set(e,t),o.set(t,e);++g<i;){var f=e[g],p=t[g];if(r)var h=s?r(p,f,g,t,e,o):r(f,p,g,e,t,o);if(void 0!==h){if(h)continue;l=!1;break}if(y){if(!arraySome(t,function(e,t){if(!cacheHas(y,t)&&(f===e||n(f,e,a,r,o)))return y.push(t)})){l=!1;break}}else if(f!==p&&!n(f,p,a,r,o)){l=!1;break}}return o.delete(e),o.delete(t),l}function equalByTag(e,t,a,r,n,o,s){switch(a){case dataViewTag:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case arrayBufferTag:return!(e.byteLength!=t.byteLength||!o(new Uint8Array(e),new Uint8Array(t)));case boolTag:case dateTag:case numberTag:return eq(+e,+t);case errorTag:return e.name==t.name&&e.message==t.message;case regexpTag:case stringTag:return e==t+"";case mapTag:var i=mapToArray;case setTag:var c=r&COMPARE_PARTIAL_FLAG;if(i||(i=setToArray),e.size!=t.size&&!c)return!1;var u=s.get(e);if(u)return u==t;r|=COMPARE_UNORDERED_FLAG,s.set(e,t);var g=equalArrays(i(e),i(t),r,n,o,s);return s.delete(e),g;case symbolTag:if(symbolValueOf)return symbolValueOf.call(e)==symbolValueOf.call(t)}return!1}function equalObjects(e,t,a,r,n,o){var s=a&COMPARE_PARTIAL_FLAG,i=getAllKeys(e),c=i.length;if(c!=getAllKeys(t).length&&!s)return!1;for(var u=c;u--;){var g=i[u];if(!(s?g in t:hasOwnProperty.call(t,g)))return!1}var l=o.get(e);if(l&&o.get(t))return l==t;var y=!0;o.set(e,t),o.set(t,e);for(var f=s;++u<c;){var p=e[g=i[u]],h=t[g];if(r)var T=s?r(h,p,g,t,e,o):r(p,h,g,e,t,o);if(!(void 0===T?p===h||n(p,h,a,r,o):T)){y=!1;break}f||(f="constructor"==g)}if(y&&!f){var b=e.constructor,d=t.constructor;b!=d&&"constructor"in e&&"constructor"in t&&!("function"==typeof b&&b instanceof b&&"function"==typeof d&&d instanceof d)&&(y=!1)}return o.delete(e),o.delete(t),y}function getAllKeys(e){return baseGetAllKeys(e,keys,getSymbols)}function getMapData(e,t){var a=e.__data__;return isKeyable(t)?a["string"==typeof t?"string":"hash"]:a.map}function getNative(e,t){var a=getValue(e,t);return baseIsNative(a)?a:void 0}function getRawTag(e){var t=hasOwnProperty.call(e,symToStringTag),a=e[symToStringTag];try{e[symToStringTag]=void 0;var r=!0}catch(e){}var n=nativeObjectToString.call(e);return r&&(t?e[symToStringTag]=a:delete e[symToStringTag]),n}function isIndex(e,t){return!!(t=null==t?MAX_SAFE_INTEGER:t)&&("number"==typeof e||reIsUint.test(e))&&e>-1&&e%1==0&&e<t}function isKeyable(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}function isMasked(e){return!!maskSrcKey&&maskSrcKey in e}function isPrototype(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||objectProto)}function objectToString(e){return nativeObjectToString.call(e)}function toSource(e){if(null!=e){try{return funcToString.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function eq(e,t){return e===t||e!==e&&t!==t}function isArrayLike(e){return null!=e&&isLength(e.length)&&!isFunction(e)}function isEqual(e,t){return baseIsEqual(e,t)}function isFunction(e){if(!isObject(e))return!1;var t=baseGetTag(e);return t==funcTag||t==genTag||t==asyncTag||t==proxyTag}function isLength(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=MAX_SAFE_INTEGER}function isObject(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function isObjectLike(e){return null!=e&&"object"==typeof e}function keys(e){return isArrayLike(e)?arrayLikeKeys(e):baseKeys(e)}function stubArray(){return[]}function stubFalse(){return!1}var LARGE_ARRAY_SIZE=200,HASH_UNDEFINED="__lodash_hash_undefined__",COMPARE_PARTIAL_FLAG=1,COMPARE_UNORDERED_FLAG=2,MAX_SAFE_INTEGER=9007199254740991,argsTag="[object Arguments]",arrayTag="[object Array]",asyncTag="[object AsyncFunction]",boolTag="[object Boolean]",dateTag="[object Date]",errorTag="[object Error]",funcTag="[object Function]",genTag="[object GeneratorFunction]",mapTag="[object Map]",numberTag="[object Number]",nullTag="[object Null]",objectTag="[object Object]",promiseTag="[object Promise]",proxyTag="[object Proxy]",regexpTag="[object RegExp]",setTag="[object Set]",stringTag="[object String]",symbolTag="[object Symbol]",undefinedTag="[object Undefined]",weakMapTag="[object WeakMap]",arrayBufferTag="[object ArrayBuffer]",dataViewTag="[object DataView]",float32Tag="[object Float32Array]",float64Tag="[object Float64Array]",int8Tag="[object Int8Array]",int16Tag="[object Int16Array]",int32Tag="[object Int32Array]",uint8Tag="[object Uint8Array]",uint8ClampedTag="[object Uint8ClampedArray]",uint16Tag="[object Uint16Array]",uint32Tag="[object Uint32Array]",reRegExpChar=/[\\^$.*+?()[\]{}|]/g,reIsHostCtor=/^\[object .+?Constructor\]$/,reIsUint=/^(?:0|[1-9]\d*)$/,typedArrayTags={};typedArrayTags[float32Tag]=typedArrayTags[float64Tag]=typedArrayTags[int8Tag]=typedArrayTags[int16Tag]=typedArrayTags[int32Tag]=typedArrayTags[uint8Tag]=typedArrayTags[uint8ClampedTag]=typedArrayTags[uint16Tag]=typedArrayTags[uint32Tag]=!0,typedArrayTags[argsTag]=typedArrayTags[arrayTag]=typedArrayTags[arrayBufferTag]=typedArrayTags[boolTag]=typedArrayTags[dataViewTag]=typedArrayTags[dateTag]=typedArrayTags[errorTag]=typedArrayTags[funcTag]=typedArrayTags[mapTag]=typedArrayTags[numberTag]=typedArrayTags[objectTag]=typedArrayTags[regexpTag]=typedArrayTags[setTag]=typedArrayTags[stringTag]=typedArrayTags[weakMapTag]=!1;var freeGlobal="object"==typeof global&&global&&global.Object===Object&&global,freeSelf="object"==typeof self&&self&&self.Object===Object&&self,root=freeGlobal||freeSelf||Function("return this")(),freeExports="object"==typeof exports&&exports&&!exports.nodeType&&exports,freeModule=freeExports&&"object"==typeof module&&module&&!module.nodeType&&module,moduleExports=freeModule&&freeModule.exports===freeExports,freeProcess=moduleExports&&freeGlobal.process,nodeUtil=function(){try{return freeProcess&&freeProcess.binding&&freeProcess.binding("util")}catch(e){}}(),nodeIsTypedArray=nodeUtil&&nodeUtil.isTypedArray,arrayProto=Array.prototype,funcProto=Function.prototype,objectProto=Object.prototype,coreJsData=root["__core-js_shared__"],funcToString=funcProto.toString,hasOwnProperty=objectProto.hasOwnProperty,maskSrcKey=function(){var e=/[^.]+$/.exec(coreJsData&&coreJsData.keys&&coreJsData.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),nativeObjectToString=objectProto.toString,reIsNative=RegExp("^"+funcToString.call(hasOwnProperty).replace(reRegExpChar,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Buffer=moduleExports?root.Buffer:void 0,Symbol=root.Symbol,Uint8Array=root.Uint8Array,propertyIsEnumerable=objectProto.propertyIsEnumerable,splice=arrayProto.splice,symToStringTag=Symbol?Symbol.toStringTag:void 0,nativeGetSymbols=Object.getOwnPropertySymbols,nativeIsBuffer=Buffer?Buffer.isBuffer:void 0,nativeKeys=overArg(Object.keys,Object),DataView=getNative(root,"DataView"),Map=getNative(root,"Map"),Promise=getNative(root,"Promise"),Set=getNative(root,"Set"),WeakMap=getNative(root,"WeakMap"),nativeCreate=getNative(Object,"create"),dataViewCtorString=toSource(DataView),mapCtorString=toSource(Map),promiseCtorString=toSource(Promise),setCtorString=toSource(Set),weakMapCtorString=toSource(WeakMap),symbolProto=Symbol?Symbol.prototype:void 0,symbolValueOf=symbolProto?symbolProto.valueOf:void 0;Hash.prototype.clear=hashClear,Hash.prototype.delete=hashDelete,Hash.prototype.get=hashGet,Hash.prototype.has=hashHas,Hash.prototype.set=hashSet,ListCache.prototype.clear=listCacheClear,ListCache.prototype.delete=listCacheDelete,ListCache.prototype.get=listCacheGet,ListCache.prototype.has=listCacheHas,ListCache.prototype.set=listCacheSet,MapCache.prototype.clear=mapCacheClear,MapCache.prototype.delete=mapCacheDelete,MapCache.prototype.get=mapCacheGet,MapCache.prototype.has=mapCacheHas,MapCache.prototype.set=mapCacheSet,SetCache.prototype.add=SetCache.prototype.push=setCacheAdd,SetCache.prototype.has=setCacheHas,Stack.prototype.clear=stackClear,Stack.prototype.delete=stackDelete,Stack.prototype.get=stackGet,Stack.prototype.has=stackHas,Stack.prototype.set=stackSet;var getSymbols=nativeGetSymbols?function(e){return null==e?[]:(e=Object(e),arrayFilter(nativeGetSymbols(e),function(t){return propertyIsEnumerable.call(e,t)}))}:stubArray,getTag=baseGetTag;(DataView&&getTag(new DataView(new ArrayBuffer(1)))!=dataViewTag||Map&&getTag(new Map)!=mapTag||Promise&&getTag(Promise.resolve())!=promiseTag||Set&&getTag(new Set)!=setTag||WeakMap&&getTag(new WeakMap)!=weakMapTag)&&(getTag=function(e){var t=baseGetTag(e),a=t==objectTag?e.constructor:void 0,r=a?toSource(a):"";if(r)switch(r){case dataViewCtorString:return dataViewTag;case mapCtorString:return mapTag;case promiseCtorString:return promiseTag;case setCtorString:return setTag;case weakMapCtorString:return weakMapTag}return t});var isArguments=baseIsArguments(function(){return arguments}())?baseIsArguments:function(e){return isObjectLike(e)&&hasOwnProperty.call(e,"callee")&&!propertyIsEnumerable.call(e,"callee")},isArray=Array.isArray,isBuffer=nativeIsBuffer||stubFalse,isTypedArray=nodeIsTypedArray?baseUnary(nodeIsTypedArray):baseIsTypedArray;module.exports=isEqual;

}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})

},{}],21:[function(require,module,exports){
var root=require("./_root"),Symbol=root.Symbol;module.exports=Symbol;

},{"./_root":28}],22:[function(require,module,exports){
function baseGetTag(e){return null==e?void 0===e?undefinedTag:nullTag:symToStringTag&&symToStringTag in Object(e)?getRawTag(e):objectToString(e)}var Symbol=require("./_Symbol"),getRawTag=require("./_getRawTag"),objectToString=require("./_objectToString"),nullTag="[object Null]",undefinedTag="[object Undefined]",symToStringTag=Symbol?Symbol.toStringTag:void 0;module.exports=baseGetTag;

},{"./_Symbol":21,"./_getRawTag":25,"./_objectToString":26}],23:[function(require,module,exports){
(function (global){
var freeGlobal="object"==typeof global&&global&&global.Object===Object&&global;module.exports=freeGlobal;

}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})

},{}],24:[function(require,module,exports){
var overArg=require("./_overArg"),getPrototype=overArg(Object.getPrototypeOf,Object);module.exports=getPrototype;

},{"./_overArg":27}],25:[function(require,module,exports){
function getRawTag(t){var o=hasOwnProperty.call(t,symToStringTag),r=t[symToStringTag];try{t[symToStringTag]=void 0;var a=!0}catch(t){}var e=nativeObjectToString.call(t);return a&&(o?t[symToStringTag]=r:delete t[symToStringTag]),e}var Symbol=require("./_Symbol"),objectProto=Object.prototype,hasOwnProperty=objectProto.hasOwnProperty,nativeObjectToString=objectProto.toString,symToStringTag=Symbol?Symbol.toStringTag:void 0;module.exports=getRawTag;

},{"./_Symbol":21}],26:[function(require,module,exports){
function objectToString(t){return nativeObjectToString.call(t)}var objectProto=Object.prototype,nativeObjectToString=objectProto.toString;module.exports=objectToString;

},{}],27:[function(require,module,exports){
function overArg(r,e){return function(n){return r(e(n))}}module.exports=overArg;

},{}],28:[function(require,module,exports){
var freeGlobal=require("./_freeGlobal"),freeSelf="object"==typeof self&&self&&self.Object===Object&&self,root=freeGlobal||freeSelf||Function("return this")();module.exports=root;

},{"./_freeGlobal":23}],29:[function(require,module,exports){
function isObjectLike(e){return null!=e&&"object"==typeof e}module.exports=isObjectLike;

},{}],30:[function(require,module,exports){
function isPlainObject(t){if(!isObjectLike(t)||baseGetTag(t)!=objectTag)return!1;var e=getPrototype(t);if(null===e)return!0;var o=hasOwnProperty.call(e,"constructor")&&e.constructor;return"function"==typeof o&&o instanceof o&&funcToString.call(o)==objectCtorString}var baseGetTag=require("./_baseGetTag"),getPrototype=require("./_getPrototype"),isObjectLike=require("./isObjectLike"),objectTag="[object Object]",funcProto=Function.prototype,objectProto=Object.prototype,funcToString=funcProto.toString,hasOwnProperty=objectProto.hasOwnProperty,objectCtorString=funcToString.call(Object);module.exports=isPlainObject;

},{"./_baseGetTag":22,"./_getPrototype":24,"./isObjectLike":29}],31:[function(require,module,exports){
"use strict";var Event=function(t,e,i){this.type=t,this.loaded=void 0!==e?e:0,i>0?(this.total=i,this.lengthComputable=!0):(this.total=0,this.lengthComputable=!1)};module.exports=Event;

},{}],32:[function(require,module,exports){
"use strict";var EventTarget=function(e){this._eventContext=e||this,this._eventListeners={}};EventTarget.events=["loadstart","progress","abort","error","load","timeout","loadend"],EventTarget.prototype.hasListeners=function(){for(var e=0;e<EventTarget.events.length;e++){var t=EventTarget.events[e];if(this._eventListeners[t]||this["on"+t])return!0}return!1},EventTarget.prototype.addEventListener=function(e,t){t&&(this._eventListeners[e]=this._eventListeners[e]||[],this._eventListeners[e].push(t))},EventTarget.prototype.dispatchEvent=function(e){var t=[];if(this._eventListeners[e.type])for(var n=0;n<this._eventListeners[e.type].length;n++)t.push(this._eventListeners[e.type][n]);if(-1!==EventTarget.events.indexOf(e.type)){var s=this["on"+e.type];s&&t.push(s)}for(n=0;n<t.length;n++)"function"==typeof t[n]?t[n].call(this._eventContext,e):t[n].handleEvent()},module.exports=EventTarget;

},{}],33:[function(require,module,exports){
"use strict";var HeadersContainer=function(e){if(this._headers=[],e&&e instanceof Object)for(var r in e)e.hasOwnProperty(r)&&this._headers.push({name:r,value:e[r]})};HeadersContainer.prototype.reset=function(){this._headers=[]},HeadersContainer.prototype.getHeader=function(e){var r=this._getHeader(e);return null!==r?r.value:null},HeadersContainer.prototype.getAll=function(){for(var e="",r=0;r<this._headers.length;r++)e.length>0&&(e+="\r\n"),e+=this._headers[r].name+": "+this._headers[r].value;return e},HeadersContainer.prototype.addHeader=function(e,r){var t=this._getHeader(e);t?t.value+=", "+r:this._headers.push({name:e,value:r})},HeadersContainer.prototype._getHeader=function(e){e=e.toLowerCase();for(var r=0;r<this._headers.length;r++)if(this._headers[r].name.toLowerCase()===e)return this._headers[r];return null},module.exports=HeadersContainer;

},{}],34:[function(require,module,exports){
"use strict";function throwError(e,t){var r=new Error(t||"");throw r.name=e,r}var Event=require("./Event"),EventTarget=require("./EventTarget"),HeadersContainer=require("./HeadersContainer"),MockXhr=function(){EventTarget.call(this),this._readyState=MockXhr.UNSENT,this.requestHeaders=new HeadersContainer,this._upload=new EventTarget(this),this._response=this._networkErrorResponse(),"function"==typeof MockXhr.onCreate&&MockXhr.onCreate(this)};MockXhr.prototype=Object.create(EventTarget.prototype,{readyState:{get:function(){return this._readyState}},upload:{get:function(){return this._upload}},status:{get:function(){return this._response.status}},statusText:{get:function(){return this._response.statusMessage}},responseType:{get:function(){return""},set:function(){throw new Error("Operation not supported.")}},response:{get:function(){return this._getResponseText()}},responseText:{get:function(){return this._getResponseText()}}}),MockXhr.prototype.constructor=MockXhr,MockXhr.UNSENT=0,MockXhr.OPENED=1,MockXhr.HEADERS_RECEIVED=2,MockXhr.LOADING=3,MockXhr.DONE=4,MockXhr.prototype.open=function(e,t){this._methodForbidden(e)&&throwError("SecurityError",'Method "'+e+'" forbidden.'),e=this._normalizeMethodName(e),this._terminateRequest(),this.sendFlag=!1,this.method=e,this.url=t,this.requestHeaders.reset(),this._response=this._networkErrorResponse(),this._readyState!==MockXhr.OPENED&&(this._readyState=MockXhr.OPENED,this._fireReadyStateChange())},MockXhr.prototype.setRequestHeader=function(e,t){if((this._readyState!==MockXhr.OPENED||this.sendFlag)&&throwError("InvalidStateError"),"string"!=typeof e||"string"!=typeof t)throw new SyntaxError;this._requestHeaderForbidden(e)||(t=t.trim(),this.requestHeaders.addHeader(e,t))},MockXhr.prototype.send=function(e){if(void 0===e&&(e=null),(this._readyState!==MockXhr.OPENED||this.sendFlag)&&throwError("InvalidStateError"),"GET"!==this.method&&"HEAD"!==this.method||(e=null),null!==e&&null===this.requestHeaders.getHeader("Content-Type")){var t=null;"string"==typeof e?t="text/plain;charset=UTF-8":e.type&&(t=e.type),null!==t&&this.requestHeaders.addHeader("Content-Type",t)}this.body=e,this.uploadCompleteFlag=null===this.body,this.sendFlag=!0,this._fireEvent("loadstart",0,0),this.uploadCompleteFlag||this._fireUploadEvent("loadstart",0,this._getRequestBodySize());var r=this;if("function"==typeof this.onSend){var o=this.onSend;setTimeout(function(){o.call(r,r)},0)}if("function"==typeof MockXhr.onSend){var s=MockXhr.onSend;setTimeout(function(){s.call(r,r)},0)}},MockXhr.prototype.abort=function(){this._terminateRequest(),(this._readyState===MockXhr.OPENED&&this.sendFlag||this._readyState===MockXhr.HEADERS_RECEIVED||this._readyState===MockXhr.LOADING)&&this._requestErrorSteps("abort"),this._readyState===MockXhr.DONE&&(this._readyState=MockXhr.UNSENT)},MockXhr.prototype._networkErrorResponse=function(){return{type:"error",status:0,statusMessage:"",headers:new HeadersContainer,body:null}},MockXhr.prototype._isNetworkErrorResponse=function(){return"error"===this._response.type},MockXhr.prototype._terminateRequest=function(){delete this.method,delete this.url},MockXhr.prototype._getRequestBodySize=function(){return this.body?this.body.size?this.body.size:this.body.length:0},MockXhr.prototype.getResponseHeader=function(e){return this._response.headers.getHeader(e)},MockXhr.prototype.getAllResponseHeaders=function(){return this._response.headers.getAll()},MockXhr.prototype._getResponseText=function(){return this._readyState!==MockXhr.LOADING&&this._readyState!==MockXhr.DONE?"":this._response.body?this._response.body:""},MockXhr.prototype._newEvent=function(e,t,r){return new Event(e,t,r)},MockXhr.prototype._fireEvent=function(e,t,r){this.dispatchEvent(this._newEvent(e,t,r))},MockXhr.prototype._fireUploadEvent=function(e,t,r){this._upload.dispatchEvent(this._newEvent(e,t,r))},MockXhr.prototype._fireReadyStateChange=function(){var e=new Event("readystatechange");this.onreadystatechange&&this.onreadystatechange(e),this.dispatchEvent(e)},MockXhr.prototype._requestEndOfBody=function(){this.uploadCompleteFlag=!0;var e=this._getRequestBodySize(),t=e;this._fireUploadEvent("progress",t,e),this._fireUploadEvent("load",t,e),this._fireUploadEvent("loadend",t,e)},MockXhr.prototype._processResponse=function(e){this._response=e,this._handleResponseErrors(),this._isNetworkErrorResponse()||(this._readyState=MockXhr.HEADERS_RECEIVED,this._fireReadyStateChange(),this._readyState===MockXhr.HEADERS_RECEIVED&&null===this._response.body&&this._handleResponseEndOfBody())},MockXhr.prototype._handleResponseEndOfBody=function(){if(this._handleResponseErrors(),!this._isNetworkErrorResponse()){var e=this._response.body?this._response.body.length:0;this._fireEvent("progress",e,e),this._readyState=MockXhr.DONE,this.sendFlag=!1,this._fireReadyStateChange(),this._fireEvent("load",e,e),this._fireEvent("loadend",e,e)}},MockXhr.prototype._handleResponseErrors=function(){if(this.sendFlag)if(this._isNetworkErrorResponse())this._requestErrorSteps("error");else switch(this._response.terminationReason){case"end-user abort":this._requestErrorSteps("abort");break;case"fatal":this._readyState=MockXhr.DONE,this.sendFlag=!1,this._response=this._networkErrorResponse()}},MockXhr.prototype._requestErrorSteps=function(e){this._readyState=MockXhr.DONE,this.sendFlag=!1,this._response=this._networkErrorResponse(),this._fireReadyStateChange(),this.uploadCompleteFlag||(this.uploadCompleteFlag=!0,this._fireUploadEvent(e,0,0),this._fireUploadEvent("loadend",0,0)),this._fireEvent(e,0,0),this._fireEvent("loadend",0,0)},MockXhr.prototype.uploadProgress=function(e){if(!this.sendFlag||this.uploadCompleteFlag)throw new Error("Mock usage error detected.");this._fireUploadEvent("progress",e,this._getRequestBodySize())},MockXhr.prototype.respond=function(e,t,r,o){this.setResponseHeaders(e,t,o),this.setResponseBody(r)},MockXhr.prototype.setResponseHeaders=function(e,t,r){if(this._readyState!==MockXhr.OPENED||!this.sendFlag)throw new Error("Mock usage error detected.");this.body&&this._requestEndOfBody(),e="number"==typeof e?e:200,this._processResponse({status:e,statusMessage:void 0!==r?r:MockXhr.statusCodes[e],headers:new HeadersContainer(t)})},MockXhr.prototype.downloadProgress=function(e,t){if(this._readyState!==MockXhr.HEADERS_RECEIVED&&this._readyState!==MockXhr.LOADING)throw new Error("Mock usage error detected.");this._readyState===MockXhr.HEADERS_RECEIVED&&(this._readyState=MockXhr.LOADING),this._fireReadyStateChange(),this._fireEvent("progress",e,t)},MockXhr.prototype.setResponseBody=function(e){if(!this.sendFlag||this._readyState!==MockXhr.OPENED&&this._readyState!==MockXhr.HEADERS_RECEIVED&&this._readyState!==MockXhr.LOADING)throw new Error("Mock usage error detected.");this._readyState===MockXhr.OPENED&&this.setResponseHeaders(),this._readyState=MockXhr.LOADING,this._fireReadyStateChange(),this._response.body=void 0!==e?e:null,this._handleResponseEndOfBody()},MockXhr.prototype.setNetworkError=function(){if(!this.sendFlag)throw new Error("Mock usage error detected.");this._processResponse(this._networkErrorResponse())};var forbiddenHeaders=["Accept-Charset","Accept-Encoding","Access-Control-Request-Headers","Access-Control-Request-Method","Connection","Content-Length","Cookie","Cookie2","Date","DNT","Expect","Host","Keep-Alive","Origin","Referer","TE","Trailer","Transfer-Encoding","Upgrade","Via"],forbiddenHeaderRegEx=new RegExp("^("+forbiddenHeaders.join("|")+"|Proxy-.*|Sec-.*)$","i");MockXhr.prototype._requestHeaderForbidden=function(e){return forbiddenHeaderRegEx.test(e)},MockXhr.prototype._methodForbidden=function(e){return/^(CONNECT|TRACE|TRACK)$/i.test(e)};var upperCaseMethods=["DELETE","GET","HEAD","OPTIONS","POST","PUT"],upperCaseMethodsRegEx=new RegExp("^("+upperCaseMethods.join("|")+")$","i");MockXhr.prototype._normalizeMethodName=function(e){return upperCaseMethodsRegEx.test(e)&&(e=e.toUpperCase()),e},MockXhr.statusCodes={100:"Continue",101:"Switching Protocols",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",300:"Multiple Choice",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",307:"Temporary Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Request Entity Too Large",414:"Request-URI Too Long",415:"Unsupported Media Type",416:"Requested Range Not Satisfiable",417:"Expectation Failed",422:"Unprocessable Entity",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported"},module.exports=MockXhr;

},{"./Event":31,"./EventTarget":32,"./HeadersContainer":33}],35:[function(require,module,exports){
"use strict";function toObject(e){if(null===e||void 0===e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}function shouldUseNative(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var r={},t=0;t<10;t++)r["_"+String.fromCharCode(t)]=t;if("0123456789"!==Object.getOwnPropertyNames(r).map(function(e){return r[e]}).join(""))return!1;var n={};return"abcdefghijklmnopqrst".split("").forEach(function(e){n[e]=e}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},n)).join("")}catch(e){return!1}}var getOwnPropertySymbols=Object.getOwnPropertySymbols,hasOwnProperty=Object.prototype.hasOwnProperty,propIsEnumerable=Object.prototype.propertyIsEnumerable;module.exports=shouldUseNative()?Object.assign:function(e,r){for(var t,n,o=toObject(e),a=1;a<arguments.length;a++){t=Object(arguments[a]);for(var s in t)hasOwnProperty.call(t,s)&&(o[s]=t[s]);if(getOwnPropertySymbols){n=getOwnPropertySymbols(t);for(var c=0;c<n.length;c++)propIsEnumerable.call(t,n[c])&&(o[n[c]]=t[n[c]])}}return o};

},{}],36:[function(require,module,exports){
"use strict";var emptyFunction=require("fbjs/lib/emptyFunction"),invariant=require("fbjs/lib/invariant"),ReactPropTypesSecret=require("./lib/ReactPropTypesSecret");module.exports=function(){function e(e,r,t,n,p,o){o!==ReactPropTypesSecret&&invariant(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")}function r(){return e}e.isRequired=e;var t={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:r,element:e,instanceOf:r,node:e,objectOf:r,oneOf:r,oneOfType:r,shape:r,exact:r};return t.checkPropTypes=emptyFunction,t.PropTypes=t,t};

},{"./lib/ReactPropTypesSecret":37,"fbjs/lib/emptyFunction":9,"fbjs/lib/invariant":13}],37:[function(require,module,exports){
"use strict";var ReactPropTypesSecret="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";module.exports=ReactPropTypesSecret;

},{}],38:[function(require,module,exports){
"use strict";function w(e){for(var t=arguments.length-1,n="Minified React error #"+e+"; visit http://facebook.github.io/react/docs/error-decoder.html?invariant="+e,r=0;r<t;r++)n+="&args[]="+encodeURIComponent(arguments[r+1]);throw t=Error(n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."),t.name="Invariant Violation",t.framesToPop=1,t}function ja(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function pa(){if(la)for(var e in oa){var t=oa[e],n=la.indexOf(e);if(-1<n||w("96",e),!qa.plugins[n]){t.extractEvents||w("97",e),qa.plugins[n]=t,n=t.eventTypes;for(var r in n){var o=void 0,a=n[r],i=t,l=r;qa.eventNameDispatchConfigs.hasOwnProperty(l)&&w("99",l),qa.eventNameDispatchConfigs[l]=a;var u=a.phasedRegistrationNames;if(u){for(o in u)u.hasOwnProperty(o)&&ra(u[o],i,l);o=!0}else a.registrationName?(ra(a.registrationName,i,l),o=!0):o=!1;o||w("98",r,e)}}}}function ra(e,t,n){qa.registrationNameModules[e]&&w("100",e),qa.registrationNameModules[e]=t,qa.registrationNameDependencies[e]=t.eventTypes[n].dependencies}function ua(e,t){return(e&t)===t}function La(e){for(var t;t=e._renderedComponent;)e=t;return e}function Ma(e,t){(e=La(e))._hostNode=t,t[Ha]=e}function Na(e,t){if(!(e._flags&Fa.hasCachedChildNodes)){var n=e._renderedChildren;t=t.firstChild;var r;e:for(r in n)if(n.hasOwnProperty(r)){var o=n[r],a=La(o)._domID;if(0!==a){for(;null!==t;t=t.nextSibling){var i=t,l=a;if(i.nodeType===Aa&&i.getAttribute(Ea)===""+l||i.nodeType===Ba&&i.nodeValue===" react-text: "+l+" "||i.nodeType===Ba&&i.nodeValue===" react-empty: "+l+" "){Ma(o,t);continue e}}w("32",a)}}e._flags|=Fa.hasCachedChildNodes}}function Oa(e){if(e[Ha])return e[Ha];for(var t=[];!e[Ha];){if(t.push(e),!e.parentNode)return null;e=e.parentNode}var n=e[Ha];if(n.tag===ya||n.tag===za)return n;for(;e&&(n=e[Ha]);e=t.pop()){var r=n;t.length&&Na(n,e)}return r}function Ra(e){if("function"==typeof e.getName)return e.getName();if("number"==typeof e.tag){if("string"==typeof(e=e.type))return e;if("function"==typeof e)return e.displayName||e.name}return null}function Za(e){var t=e;if(e.alternate)for(;t.return;)t=t.return;else{if((t.effectTag&Xa)!==Wa)return 1;for(;t.return;)if(((t=t.return).effectTag&Xa)!==Wa)return 1}return t.tag===Ta?2:3}function $a(e){2!==Za(e)&&w("188")}function ab(e){var t=e.alternate;if(!t)return 3===(t=Za(e))&&w("188"),1===t?null:e;for(var n=e,r=t;;){var o=n.return,a=o?o.alternate:null;if(!o||!a)break;if(o.child===a.child){for(var i=o.child;i;){if(i===n)return $a(o),e;if(i===r)return $a(o),t;i=i.sibling}w("188")}if(n.return!==r.return)n=o,r=a;else{i=!1;for(var l=o.child;l;){if(l===n){i=!0,n=o,r=a;break}if(l===r){i=!0,r=o,n=a;break}l=l.sibling}if(!i){for(l=a.child;l;){if(l===n){i=!0,n=a,r=o;break}if(l===r){i=!0,r=a,n=o;break}l=l.sibling}i||w("189")}}n.alternate!==r&&w("190")}return n.tag!==Ta&&w("188"),n.stateNode.current===n?e:t}function cb(e,t,n,r,o,a,i,l,u){K._hasCaughtError=!1,K._caughtError=null;var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(e){K._caughtError=e,K._hasCaughtError=!0}}function db(){if(K._hasRethrowError){var e=K._rethrowError;throw K._rethrowError=null,K._hasRethrowError=!1,e}}function gb(e,t,n,r){t=e.type||"unknown-event",e.currentTarget=hb.getNodeFromInstance(r),eb.invokeGuardedCallbackAndCatchFirstError(t,n,void 0,e),e.currentTarget=null}function mb(e){if(e=ib.getInstanceFromNode(e))if("number"==typeof e.tag){jb&&"function"==typeof jb.restoreControlledState||w("194");var t=ib.getFiberCurrentPropsFromNode(e.stateNode);jb.restoreControlledState(e.stateNode,e.type,t)}else"function"!=typeof e.restoreControlledState&&w("195"),e.restoreControlledState()}function ob(e,t,n,r,o,a){return e(t,n,r,o,a)}function pb(e,t){return e(t)}function qb(e,t){return pb(e,t)}function ub(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===tb?e.parentNode:e}function xb(e){var t=e.targetInst;do{if(!t){e.ancestors.push(t);break}var n=t;if("number"==typeof n.tag){for(;n.return;)n=n.return;n=n.tag!==vb?null:n.stateNode.containerInfo}else{for(;n._hostParent;)n=n._hostParent;n=G.getNodeFromInstance(n).parentNode}if(!n)break;e.ancestors.push(t),t=G.getClosestInstanceFromNode(n)}while(t);for(n=0;n<e.ancestors.length;n++)t=e.ancestors[n],yb._handleTopLevel(e.topLevelType,t,e.nativeEvent,ub(e.nativeEvent))}function Cb(e,t){return null==t&&w("30"),null==e?t:Array.isArray(e)?Array.isArray(t)?(e.push.apply(e,t),e):(e.push(t),e):Array.isArray(t)?[e].concat(t):[e,t]}function Db(e,t,n){Array.isArray(e)?e.forEach(t,n):e&&t.call(n,e)}function Fb(e,t){e&&(ib.executeDispatchesInOrder(e,t),e.isPersistent()||e.constructor.release(e))}function Gb(e){return Fb(e,!0)}function Hb(e){return Fb(e,!1)}function Ib(e,t,n){switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":return!(!n.disabled||"button"!==t&&"input"!==t&&"select"!==t&&"textarea"!==t);default:return!1}}function Lb(e,t){if(!l.canUseDOM||t&&!("addEventListener"in document))return!1;var n=(t="on"+e)in document;return n||((n=document.createElement("div")).setAttribute(t,"return;"),n="function"==typeof n[t]),!n&&Kb&&"wheel"===e&&(n=document.implementation.hasFeature("Events.wheel","3.0")),n}function Mb(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n["ms"+e]="MS"+t,n["O"+e]="o"+t.toLowerCase(),n}function Qb(e){if(Ob[e])return Ob[e];if(!Nb[e])return e;var t,n=Nb[e];for(t in n)if(n.hasOwnProperty(t)&&t in Pb)return Ob[e]=n[t];return""}function Vb(e){return Object.prototype.hasOwnProperty.call(e,Ub)||(e[Ub]=Tb++,Sb[e[Ub]]={}),Sb[e[Ub]]}function fc(e){return!!ec.hasOwnProperty(e)||!dc.hasOwnProperty(e)&&(cc.test(e)?ec[e]=!0:(dc[e]=!0,!1))}function jc(){return null}function rc(e){var t="";return aa.Children.forEach(e,function(e){null==e||"string"!=typeof e&&"number"!=typeof e||(t+=e)}),t}function tc(e,t,n){if(e=e.options,t){t={};for(var r=0;r<n.length;r++)t["$"+n[r]]=!0;for(n=0;n<e.length;n++)r=t.hasOwnProperty("$"+e[n].value),e[n].selected!==r&&(e[n].selected=r)}else{for(n=""+n,t=null,r=0;r<e.length;r++){if(e[r].value===n)return void(e[r].selected=!0);null!==t||e[r].disabled||(t=e[r])}null!==t&&(t.selected=!0)}}function yc(e,t){t&&(xc[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML)&&w("137",e,""),null!=t.dangerouslySetInnerHTML&&(null!=t.children&&w("60"),"object"==typeof t.dangerouslySetInnerHTML&&"__html"in t.dangerouslySetInnerHTML||w("61")),null!=t.style&&"object"!=typeof t.style&&w("62",""))}function zc(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Ac(e){var t=zc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"function"==typeof n.get&&"function"==typeof n.set)return Object.defineProperty(e,t,{enumerable:n.enumerable,configurable:!0,get:function(){return n.get.call(this)},set:function(e){r=""+e,n.set.call(this,e)}}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}function Cc(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}function Ic(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===Hc)return void(n.nodeValue=t)}e.textContent=t}function Rc(e,t){Nc(t,e.nodeType===Lc||e.nodeType===Mc?e:e.ownerDocument)}function od(e,t){return e!==id&&e!==hd||t!==id&&t!==hd?e===gd&&t!==gd?-255:e!==gd&&t===gd?255:e-t:0}function pd(){return{first:null,last:null,hasForceUpdate:!1,callbackList:null}}function qd(e,t,n,r){null!==n?n.next=t:(t.next=e.first,e.first=t),null!==r?t.next=r:e.last=t}function rd(e,t){t=t.priorityLevel;var n=null;if(null!==e.last&&0>=od(e.last.priorityLevel,t))n=e.last;else for(e=e.first;null!==e&&0>=od(e.priorityLevel,t);)n=e,e=e.next;return n}function sd(e,t){var n=e.alternate,r=e.updateQueue;null===r&&(r=e.updateQueue=pd()),null!==n?null===(e=n.updateQueue)&&(e=n.updateQueue=pd()):e=null;var o=md=r;n=nd=e!==r?e:null;var a=rd(o,t),i=null!==a?a.next:o.first;return null===n?(qd(o,t,a,i),null):(r=rd(n,t),e=null!==r?r.next:n.first,qd(o,t,a,i),i===e&&null!==i||a===r&&null!==a?(null===r&&(n.first=t),null===e&&(n.last=null),null):(t={priorityLevel:t.priorityLevel,partialState:t.partialState,callback:t.callback,isReplace:t.isReplace,isForced:t.isForced,isTopLevelUnmount:t.isTopLevelUnmount,next:null},qd(n,t,r,e),t))}function td(e,t,n,r){return"function"==typeof(e=e.partialState)?e.call(t,n,r):e}function Md(e,t,n){(e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=n}function Nd(e){return e.tag===zd&&null!=e.type.childContextTypes}function Od(e,t){var r=e.stateNode,o=e.type.childContextTypes;if("function"!=typeof r.getChildContext)return t;r=r.getChildContext();for(var a in r)a in o||w("108",Ra(e)||"Unknown",a);return n({},t,r)}function be(e,t,n){this.tag=e,this.key=t,this.stateNode=this.type=null,this.sibling=this.child=this.return=null,this.index=0,this.memoizedState=this.updateQueue=this.memoizedProps=this.pendingProps=this.ref=null,this.internalContextTag=n,this.effectTag=ae,this.lastEffect=this.firstEffect=this.nextEffect=null,this.pendingWorkPriority=Zd,this.alternate=null}function ce(e,t,n){var r=void 0;return"function"==typeof e?(r=e.prototype&&e.prototype.isReactComponent?new be(Rd,t,n):new be(Qd,t,n),r.type=e):"string"==typeof e?(r=new be(Td,t,n),r.type=e):"object"==typeof e&&null!==e&&"number"==typeof e.tag?r=e:w("130",null==e?e:typeof e,""),r}function Le(e){return null===e||void 0===e?null:"function"==typeof(e=Je&&e[Je]||e["@@iterator"])?e:null}function Me(e,t){var n=t.ref;if(null!==n&&"function"!=typeof n){if(t._owner){var r=void 0;(t=t._owner)&&("number"==typeof t.tag?(t.tag!==Ae&&w("110"),r=t.stateNode):r=t.getPublicInstance()),r||w("147",n);var o=""+n;return null!==e&&null!==e.ref&&e.ref._stringRef===o?e.ref:(e=function(e){var t=r.refs===da?r.refs={}:r.refs;null===e?delete t[o]:t[o]=e},e._stringRef=o,e)}"string"!=typeof n&&w("148"),t._owner||w("149",n)}return n}function Ne(e,t){"textarea"!==e.type&&w("31","[object Object]"===Object.prototype.toString.call(t)?"object with keys {"+Object.keys(t).join(", ")+"}":t,"")}function Oe(e,t){function n(n,r){if(t){if(!e){if(null===r.alternate)return;r=r.alternate}var o=n.lastEffect;null!==o?(o.nextEffect=r,n.lastEffect=r):n.firstEffect=n.lastEffect=r,r.nextEffect=null,r.effectTag=Ie}}function r(e,r){if(!t)return null;for(;null!==r;)n(e,r),r=r.sibling;return null}function o(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(t,n){return e?(t=re(t,n),t.index=0,t.sibling=null,t):(t.pendingWorkPriority=n,t.effectTag=Ge,t.index=0,t.sibling=null,t)}function i(e,n,r){return e.index=r,t?null!==(r=e.alternate)?(r=r.index)<n?(e.effectTag=He,n):r:(e.effectTag=He,n):n}function l(e){return t&&null===e.alternate&&(e.effectTag=He),e}function u(e,t,n,r){return null===t||t.tag!==Be?(n=ue(n,e.internalContextTag,r),n.return=e,n):(t=a(t,r),t.pendingProps=n,t.return=e,t)}function c(e,t,n,r){return null===t||t.type!==n.type?(r=se(n,e.internalContextTag,r),r.ref=Me(t,n),r.return=e,r):(r=a(t,r),r.ref=Me(t,n),r.pendingProps=n.props,r.return=e,r)}function s(e,t,n,r){return null===t||t.tag!==De?(n=ve(n,e.internalContextTag,r),n.return=e,n):(t=a(t,r),t.pendingProps=n,t.return=e,t)}function d(e,t,n,r){return null===t||t.tag!==Ee?(t=we(n,e.internalContextTag,r),t.type=n.value,t.return=e,t):(t=a(t,r),t.type=n.value,t.return=e,t)}function p(e,t,n,r){return null===t||t.tag!==Ce||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?(n=xe(n,e.internalContextTag,r),n.return=e,n):(t=a(t,r),t.pendingProps=n.children||[],t.return=e,t)}function f(e,t,n,r){return null===t||t.tag!==Fe?(n=te(n,e.internalContextTag,r),n.return=e,n):(t=a(t,r),t.pendingProps=n,t.return=e,t)}function g(e,t,n){if("string"==typeof t||"number"==typeof t)return t=ue(""+t,e.internalContextTag,n),t.return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case Ke:return n=se(t,e.internalContextTag,n),n.ref=Me(null,t),n.return=e,n;case oe:return t=ve(t,e.internalContextTag,n),t.return=e,t;case pe:return n=we(t,e.internalContextTag,n),n.type=t.value,n.return=e,n;case qe:return t=xe(t,e.internalContextTag,n),t.return=e,t}if(ye(t)||Le(t))return t=te(t,e.internalContextTag,n),t.return=e,t;Ne(e,t)}return null}function h(e,t,n,r){var o=null!==t?t.key:null;if("string"==typeof n||"number"==typeof n)return null!==o?null:u(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case Ke:return n.key===o?c(e,t,n,r):null;case oe:return n.key===o?s(e,t,n,r):null;case pe:return null===o?d(e,t,n,r):null;case qe:return n.key===o?p(e,t,n,r):null}if(ye(n)||Le(n))return null!==o?null:f(e,t,n,r);Ne(e,n)}return null}function b(e,t,n,r,o){if("string"==typeof r||"number"==typeof r)return e=e.get(n)||null,u(t,e,""+r,o);if("object"==typeof r&&null!==r){switch(r.$$typeof){case Ke:return e=e.get(null===r.key?n:r.key)||null,c(t,e,r,o);case oe:return e=e.get(null===r.key?n:r.key)||null,s(t,e,r,o);case pe:return e=e.get(n)||null,d(t,e,r,o);case qe:return e=e.get(null===r.key?n:r.key)||null,p(t,e,r,o)}if(ye(r)||Le(r))return e=e.get(n)||null,f(t,e,r,o);Ne(t,r)}return null}function m(e,a,l,u){for(var c=null,s=null,d=a,p=a=0,f=null;null!==d&&p<l.length;p++){d.index>p?(f=d,d=null):f=d.sibling;var m=h(e,d,l[p],u);if(null===m){null===d&&(d=f);break}t&&d&&null===m.alternate&&n(e,d),a=i(m,a,p),null===s?c=m:s.sibling=m,s=m,d=f}if(p===l.length)return r(e,d),c;if(null===d){for(;p<l.length;p++)(d=g(e,l[p],u))&&(a=i(d,a,p),null===s?c=d:s.sibling=d,s=d);return c}for(d=o(e,d);p<l.length;p++)(f=b(d,e,p,l[p],u))&&(t&&null!==f.alternate&&d.delete(null===f.key?p:f.key),a=i(f,a,p),null===s?c=f:s.sibling=f,s=f);return t&&d.forEach(function(t){return n(e,t)}),c}function y(e,a,l,u){var c=Le(l);"function"!=typeof c&&w("150"),null==(l=c.call(l))&&w("151");for(var s=c=null,d=a,p=a=0,f=null,m=l.next();null!==d&&!m.done;p++,m=l.next()){d.index>p?(f=d,d=null):f=d.sibling;var y=h(e,d,m.value,u);if(null===y){d||(d=f);break}t&&d&&null===y.alternate&&n(e,d),a=i(y,a,p),null===s?c=y:s.sibling=y,s=y,d=f}if(m.done)return r(e,d),c;if(null===d){for(;!m.done;p++,m=l.next())null!==(m=g(e,m.value,u))&&(a=i(m,a,p),null===s?c=m:s.sibling=m,s=m);return c}for(d=o(e,d);!m.done;p++,m=l.next())null!==(m=b(d,e,p,m.value,u))&&(t&&null!==m.alternate&&d.delete(null===m.key?p:m.key),a=i(m,a,p),null===s?c=m:s.sibling=m,s=m);return t&&d.forEach(function(t){return n(e,t)}),c}return function(e,t,o,i){var u="object"==typeof o&&null!==o;if(u)switch(o.$$typeof){case Ke:e:{var c=o.key;for(u=t;null!==u;){if(u.key===c){if(u.type===o.type){r(e,u.sibling),(t=a(u,i)).ref=Me(u,o),t.pendingProps=o.props,t.return=e,e=t;break e}r(e,u);break}n(e,u),u=u.sibling}(i=se(o,e.internalContextTag,i)).ref=Me(t,o),i.return=e,e=i}return l(e);case oe:e:{for(u=o.key;null!==t;){if(t.key===u){if(t.tag===De){r(e,t.sibling),(t=a(t,i)).pendingProps=o,t.return=e,e=t;break e}r(e,t);break}n(e,t),t=t.sibling}(o=ve(o,e.internalContextTag,i)).return=e,e=o}return l(e);case pe:e:{if(null!==t){if(t.tag===Ee){r(e,t.sibling),(t=a(t,i)).type=o.value,t.return=e,e=t;break e}r(e,t)}(t=we(o,e.internalContextTag,i)).type=o.value,t.return=e,e=t}return l(e);case qe:e:{for(u=o.key;null!==t;){if(t.key===u){if(t.tag===Ce&&t.stateNode.containerInfo===o.containerInfo&&t.stateNode.implementation===o.implementation){r(e,t.sibling),(t=a(t,i)).pendingProps=o.children||[],t.return=e,e=t;break e}r(e,t);break}n(e,t),t=t.sibling}(o=xe(o,e.internalContextTag,i)).return=e,e=o}return l(e)}if("string"==typeof o||"number"==typeof o)return o=""+o,null!==t&&t.tag===Be?(r(e,t.sibling),t=a(t,i),t.pendingProps=o,t.return=e,e=t):(r(e,t),o=ue(o,e.internalContextTag,i),o.return=e,e=o),l(e);if(ye(o))return m(e,t,o,i);if(Le(o))return y(e,t,o,i);if(u&&Ne(e,o),void 0===o)switch(e.tag){case Ae:case ze:w("152",(o=e.type).displayName||o.name||"Component")}return r(e,t)}}function ef(e,t,n,r){function o(e,t){t.updater=a,e.stateNode=t,Pa.set(t,e)}var a={isMounted:df,enqueueSetState:function(n,r,o){n=Pa.get(n);var a=t(n,!1);Ze(n,r,void 0===o?null:o,a),e(n,a)},enqueueReplaceState:function(n,r,o){n=Pa.get(n);var a=t(n,!1);$e(n,r,void 0===o?null:o,a),e(n,a)},enqueueForceUpdate:function(n,r){n=Pa.get(n);var o=t(n,!1);af(n,void 0===r?null:r,o),e(n,o)}};return{adoptClassInstance:o,constructClassInstance:function(e,t){var n=e.type,r=Xe(e),a=Ye(e),i=a?We(e,r):da;return t=new n(t,i),o(e,t),a&&Ve(e,r,i),t},mountClassInstance:function(e,t){var n=e.alternate,r=e.stateNode,o=r.state||null,i=e.pendingProps;i||w("158");var l=Xe(e);r.props=i,r.state=o,r.refs=da,r.context=We(e,l),ed.enableAsyncSubtreeAPI&&null!=e.type&&null!=e.type.prototype&&!0===e.type.prototype.unstable_isAsyncReactComponent&&(e.internalContextTag|=Ue),"function"==typeof r.componentWillMount&&(l=r.state,r.componentWillMount(),l!==r.state&&a.enqueueReplaceState(r,r.state,null),null!==(l=e.updateQueue)&&(r.state=bf(n,e,l,r,o,i,t))),"function"==typeof r.componentDidMount&&(e.effectTag|=Te)},updateClassInstance:function(e,t,o){var i=t.stateNode;i.props=t.memoizedProps,i.state=t.memoizedState;var l=t.memoizedProps,u=t.pendingProps;u||null==(u=l)&&w("159");var c=i.context,s=Xe(t);if(s=We(t,s),"function"!=typeof i.componentWillReceiveProps||l===u&&c===s||(c=i.state,i.componentWillReceiveProps(u,s),i.state!==c&&a.enqueueReplaceState(i,i.state,null)),c=t.memoizedState,o=null!==t.updateQueue?bf(e,t,t.updateQueue,i,c,u,o):c,!(l!==u||c!==o||cf()||null!==t.updateQueue&&t.updateQueue.hasForceUpdate))return"function"!=typeof i.componentDidUpdate||l===e.memoizedProps&&c===e.memoizedState||(t.effectTag|=Te),!1;var d=u;if(null===l||null!==t.updateQueue&&t.updateQueue.hasForceUpdate)d=!0;else{var p=t.stateNode,f=t.type;d="function"==typeof p.shouldComponentUpdate?p.shouldComponentUpdate(d,o,s):!f.prototype||!f.prototype.isPureReactComponent||(!ea(l,d)||!ea(c,o))}return d?("function"==typeof i.componentWillUpdate&&i.componentWillUpdate(u,o,s),"function"==typeof i.componentDidUpdate&&(t.effectTag|=Te)):("function"!=typeof i.componentDidUpdate||l===e.memoizedProps&&c===e.memoizedState||(t.effectTag|=Te),n(t,u),r(t,o)),i.props=u,i.state=o,i.context=s,d}}}function Lf(e,t,n,r,o){function a(e,t,n){i(e,t,n,t.pendingWorkPriority)}function i(e,t,n,r){t.child=null===e?ff(t,t.child,n,r):e.child===t.child?gf(t,t.child,n,r):hf(t,t.child,n,r)}function l(e,t){var n=t.ref;null===n||e&&e.ref===n||(t.effectTag|=Jf)}function u(e,t,n,r){if(l(e,t),!n)return r&&qf(t,!1),s(e,t);n=t.stateNode,Kf.current=t;var o=n.render();return t.effectTag|=Ff,a(e,t,o),t.memoizedState=n.state,t.memoizedProps=n.props,r&&qf(t,!0),t.child}function c(e){var t=e.stateNode;t.pendingContext?pf(e,t.pendingContext,t.pendingContext!==t.context):t.context&&pf(e,t.context,!1),b(e,t.containerInfo)}function s(e,t){return jf(e,t),t.child}function d(e,t){switch(t.tag){case uf:c(t);break;case tf:of(t);break;case yf:b(t,t.stateNode.containerInfo)}return null}var p=e.shouldSetTextContent,f=e.useSyncScheduling,g=e.shouldDeprioritizeSubtree,h=t.pushHostContext,b=t.pushHostContainer,m=n.enterHydrationState,y=n.resetHydrationState,v=n.tryToClaimNextHydratableInstance,C=(e=ef(r,o,function(e,t){e.memoizedProps=t},function(e,t){e.memoizedState=t})).adoptClassInstance,E=e.constructClassInstance,k=e.mountClassInstance,T=e.updateClassInstance;return{beginWork:function(e,t,n){if(t.pendingWorkPriority===Df||t.pendingWorkPriority>n)return d(0,t);switch(t.tag){case rf:null!==e&&w("155");var r=t.type,o=t.pendingProps,i=mf(t);return i=lf(t,i),r=r(o,i),t.effectTag|=Ff,"object"==typeof r&&null!==r&&"function"==typeof r.render?(t.tag=tf,o=of(t),C(t,r),k(t,n),t=u(e,t,!0,o)):(t.tag=sf,a(e,t,r),t.memoizedProps=o,t=t.child),t;case sf:e:{if(o=t.type,n=t.pendingProps,r=t.memoizedProps,nf())null===n&&(n=r);else if(null===n||r===n){t=s(e,t);break e}r=mf(t),o=o(n,r=lf(t,r)),t.effectTag|=Ff,a(e,t,o),t.memoizedProps=n,t=t.child}return t;case tf:return o=of(t),r=void 0,null===e?t.stateNode?w("153"):(E(t,t.pendingProps),k(t,n),r=!0):r=T(e,t,n),u(e,t,r,o);case uf:return c(t),null!==(r=t.updateQueue)?(o=t.memoizedState,r=kf(e,t,r,null,o,null,n),o===r?(y(),t=s(e,t)):(o=r.element,null!==e&&null!==e.child||!m(t)?(y(),a(e,t,o)):(t.effectTag|=Gf,t.child=ff(t,t.child,o,n)),t.memoizedState=r,t=t.child)):(y(),t=s(e,t)),t;case wf:h(t),null===e&&v(t),o=t.type;var P=t.memoizedProps;return null===(r=t.pendingProps)&&null===(r=P)&&w("154"),i=null!==e?e.memoizedProps:null,nf()||null!==r&&P!==r?(P=r.children,p(o,r)?P=null:i&&p(o,i)&&(t.effectTag|=Hf),l(e,t),n!==Ef&&!f&&g(o,r)?(t.pendingWorkPriority=Ef,t=null):(a(e,t,P),t.memoizedProps=r,t=t.child)):t=s(e,t),t;case xf:return null===e&&v(t),null===(e=t.pendingProps)&&(e=t.memoizedProps),t.memoizedProps=e,null;case Af:t.tag=zf;case zf:return n=t.pendingProps,nf()?null===n&&null===(n=e&&e.memoizedProps)&&w("154"):null!==n&&t.memoizedProps!==n||(n=t.memoizedProps),o=n.children,r=t.pendingWorkPriority,t.stateNode=null===e?ff(t,t.stateNode,o,r):e.child===t.child?gf(t,t.stateNode,o,r):hf(t,t.stateNode,o,r),t.memoizedProps=n,t.stateNode;case Bf:return null;case yf:e:{if(b(t,t.stateNode.containerInfo),n=t.pendingWorkPriority,o=t.pendingProps,nf())null===o&&null==(o=e&&e.memoizedProps)&&w("154");else if(null===o||t.memoizedProps===o){t=s(e,t);break e}null===e?t.child=hf(t,t.child,o,n):a(e,t,o),t.memoizedProps=o,t=t.child}return t;case Cf:e:{if(n=t.pendingProps,nf())null===n&&(n=t.memoizedProps);else if(null===n||t.memoizedProps===n){t=s(e,t);break e}a(e,t,n),t.memoizedProps=n,t=t.child}return t;default:w("156")}},beginFailedWork:function(e,t,n){switch(t.tag){case tf:of(t);break;case uf:c(t);break;default:w("157")}return t.effectTag|=If,null===e?t.child=null:t.child!==e.child&&(t.child=e.child),t.pendingWorkPriority===Df||t.pendingWorkPriority>n?d(0,t):(t.firstEffect=null,t.lastEffect=null,i(e,t,null,n),t.tag===tf&&(e=t.stateNode,t.memoizedProps=e.props,t.memoizedState=e.state),t.child)}}}function eg(e,t,n){var r=e.createInstance,o=e.createTextInstance,a=e.appendInitialChild,i=e.finalizeInitialChildren,l=e.prepareUpdate,u=t.getRootHostContainer,c=t.popHostContext,s=t.getHostContext,d=t.popHostContainer,p=n.prepareToHydrateHostInstance,f=n.prepareToHydrateHostTextInstance,g=n.popHydrationState;return{completeWork:function(e,t,n){var h=t.pendingProps;switch(null===h?h=t.memoizedProps:t.pendingWorkPriority===dg&&n!==dg||(t.pendingProps=null),t.tag){case Qf:return null;case Rf:return Nf(t),null;case Sf:return d(t),Of(t),(h=t.stateNode).pendingContext&&(h.context=h.pendingContext,h.pendingContext=null),null!==e&&null!==e.child||(g(t),t.effectTag&=~ag),null;case Tf:c(t),n=u();var b=t.type;if(null!==e&&null!=t.stateNode){var m=e.memoizedProps,y=t.stateNode,v=s();h=l(y,b,m,h,n,v),(t.updateQueue=h)&&(t.effectTag|=cg),e.ref!==t.ref&&(t.effectTag|=bg)}else{if(!h)return null===t.stateNode&&w("166"),null;if(e=s(),g(t))p(t,n,e)&&(t.effectTag|=cg);else{e=r(b,h,n,e,t);e:for(m=t.child;null!==m;){if(m.tag===Tf||m.tag===Uf)a(e,m.stateNode);else if(m.tag!==Vf&&null!==m.child){m=m.child;continue}if(m===t)break e;for(;null===m.sibling;){if(null===m.return||m.return===t)break e;m=m.return}m=m.sibling}i(e,b,h,n)&&(t.effectTag|=cg),t.stateNode=e}null!==t.ref&&(t.effectTag|=bg)}return null;case Uf:if(e&&null!=t.stateNode)e.memoizedProps!==h&&(t.effectTag|=cg);else{if("string"!=typeof h)return null===t.stateNode&&w("166"),null;e=u(),n=s(),g(t)?f(t)&&(t.effectTag|=cg):t.stateNode=o(h,e,n,t)}return null;case Wf:(h=t.memoizedProps)||w("165"),t.tag=Xf,n=[];e:for((b=t.stateNode)&&(b.return=t);null!==b;){if(b.tag===Tf||b.tag===Uf||b.tag===Vf)w("164");else if(b.tag===Yf)n.push(b.type);else if(null!==b.child){b.child.return=b,b=b.child;continue}for(;null===b.sibling;){if(null===b.return||b.return===t)break e;b=b.return}b.sibling.return=b.return,b=b.sibling}return b=h.handler,h=b(h.props,n),t.child=Mf(t,null!==e?e.child:null,h,t.pendingWorkPriority),t.child;case Xf:return t.tag=Wf,null;case Yf:case Zf:return null;case Vf:return t.effectTag|=cg,d(t),null;case Pf:w("167");default:w("156")}}}}function hg(e){return function(t){try{return e(t)}catch(e){}}}function vg(e,t){function n(e){var n=e.ref;if(null!==n)try{n(null)}catch(n){t(e,n)}}function r(e){return e.tag===lg||e.tag===kg||e.tag===ng}function o(e){for(var t=e;;)if(i(t),null!==t.child&&t.tag!==ng)t.child.return=t,t=t.child;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return;t=t.return}t.sibling.return=t.return,t=t.sibling}}function a(e){for(var t=e,n=!1,r=void 0,a=void 0;;){if(!n){n=t.return;e:for(;;){switch(null===n&&w("160"),n.tag){case lg:r=n.stateNode,a=!1;break e;case kg:case ng:r=n.stateNode.containerInfo,a=!0;break e}n=n.return}n=!0}if(t.tag===lg||t.tag===mg)o(t),a?b(r,t.stateNode):h(r,t.stateNode);else if(t.tag===ng?r=t.stateNode.containerInfo:i(t),null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return;(t=t.return).tag===ng&&(n=!1)}t.sibling.return=t.return,t=t.sibling}}function i(e){switch("function"==typeof qg&&qg(e),e.tag){case jg:n(e);var r=e.stateNode;if("function"==typeof r.componentWillUnmount)try{r.props=e.memoizedProps,r.state=e.memoizedState,r.componentWillUnmount()}catch(n){t(e,n)}break;case lg:n(e);break;case og:o(e.stateNode);break;case ng:a(e)}}var l=e.commitMount,u=e.commitUpdate,c=e.resetTextContent,s=e.commitTextUpdate,d=e.appendChild,p=e.appendChildToContainer,f=e.insertBefore,g=e.insertInContainerBefore,h=e.removeChild,b=e.removeChildFromContainer,m=e.getPublicInstance;return{commitPlacement:function(e){e:{for(var t=e.return;null!==t;){if(r(t)){var n=t;break e}t=t.return}w("160"),n=void 0}var o=t=void 0;switch(n.tag){case lg:t=n.stateNode,o=!1;break;case kg:case ng:t=n.stateNode.containerInfo,o=!0;break;default:w("161")}n.effectTag&ug&&(c(t),n.effectTag&=~ug);e:t:for(n=e;;){for(;null===n.sibling;){if(null===n.return||r(n.return)){n=null;break e}n=n.return}for(n.sibling.return=n.return,n=n.sibling;n.tag!==lg&&n.tag!==mg;){if(n.effectTag&rg)continue t;if(null===n.child||n.tag===ng)continue t;n.child.return=n,n=n.child}if(!(n.effectTag&rg)){n=n.stateNode;break e}}for(var a=e;;){if(a.tag===lg||a.tag===mg)n?o?g(t,a.stateNode,n):f(t,a.stateNode,n):o?p(t,a.stateNode):d(t,a.stateNode);else if(a.tag!==ng&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===e)break;for(;null===a.sibling;){if(null===a.return||a.return===e)return;a=a.return}a.sibling.return=a.return,a=a.sibling}},commitDeletion:function(e){a(e),e.return=null,e.child=null,e.alternate&&(e.alternate.child=null,e.alternate.return=null)},commitWork:function(e,t){switch(t.tag){case jg:break;case lg:var n=t.stateNode;if(null!=n){var r=t.memoizedProps;e=null!==e?e.memoizedProps:r;var o=t.type,a=t.updateQueue;t.updateQueue=null,null!==a&&u(n,a,o,e,r,t)}break;case mg:null===t.stateNode&&w("162"),n=t.memoizedProps,s(t.stateNode,null!==e?e.memoizedProps:n,n);break;case kg:case ng:break;default:w("163")}},commitLifeCycles:function(e,t){switch(t.tag){case jg:var n=t.stateNode;if(t.effectTag&sg)if(null===e)n.props=t.memoizedProps,n.state=t.memoizedState,n.componentDidMount();else{var r=e.memoizedProps;e=e.memoizedState,n.props=t.memoizedProps,n.state=t.memoizedState,n.componentDidUpdate(r,e)}t.effectTag&tg&&null!==t.updateQueue&&pg(t,t.updateQueue,n);break;case kg:null!==(e=t.updateQueue)&&pg(t,e,t.child&&t.child.stateNode);break;case lg:n=t.stateNode,null===e&&t.effectTag&sg&&l(n,t.type,t.memoizedProps,t);break;case mg:case ng:break;default:w("163")}},commitAttachRef:function(e){var t=e.ref;if(null!==t){var n=e.stateNode;switch(e.tag){case lg:t(m(n));break;default:t(n)}}},commitDetachRef:function(e){null!==(e=e.ref)&&e(null)}}}function Ag(e){function t(e){return e===zg&&w("174"),e}var n=e.getChildHostContext,r=e.getRootHostContext,o=wg(zg),a=wg(zg),i=wg(zg);return{getHostContext:function(){return t(o.current)},getRootHostContainer:function(){return t(i.current)},popHostContainer:function(e){xg(o,e),xg(a,e),xg(i,e)},popHostContext:function(e){a.current===e&&(xg(o,e),xg(a,e))},pushHostContainer:function(e,t){yg(i,t,e),t=r(t),yg(a,e,e),yg(o,t,e)},pushHostContext:function(e){var r=t(i.current),l=t(o.current);l!==(r=n(l,e.type,r))&&(yg(a,e,e),yg(o,r,e))},resetHostContainer:function(){o.current=zg,i.current=zg}}}function Hg(e){function t(e,t){var n=Gg();n.stateNode=t,n.return=e,n.effectTag=Eg,null!==e.lastEffect?(e.lastEffect.nextEffect=n,e.lastEffect=n):e.firstEffect=e.lastEffect=n}function n(e,t){switch(e.tag){case Bg:return a(t,e.type,e.pendingProps);case Cg:return i(t,e.pendingProps);default:return!1}}function r(e){for(e=e.return;null!==e&&e.tag!==Bg&&e.tag!==Dg;)e=e.return;f=e}var o=e.shouldSetTextContent,a=e.canHydrateInstance,i=e.canHydrateTextInstance,l=e.getNextHydratableSibling,u=e.getFirstHydratableChild,c=e.hydrateInstance,s=e.hydrateTextInstance,d=e.didNotHydrateInstance,p=e.didNotFindHydratableInstance;if(e=e.didNotFindHydratableTextInstance,!(a&&i&&l&&u&&c&&s&&d&&p&&e))return{enterHydrationState:function(){return!1},resetHydrationState:function(){},tryToClaimNextHydratableInstance:function(){},prepareToHydrateHostInstance:function(){w("175")},prepareToHydrateHostTextInstance:function(){w("176")},popHydrationState:function(){return!1}};var f=null,g=null,h=!1;return{enterHydrationState:function(e){return g=u(e.stateNode.containerInfo),f=e,h=!0},resetHydrationState:function(){g=f=null,h=!1},tryToClaimNextHydratableInstance:function(e){if(h){var r=g;if(r){if(!n(e,r)){if(!(r=l(r))||!n(e,r))return e.effectTag|=Fg,h=!1,void(f=e);t(f,g)}e.stateNode=r,f=e,g=u(r)}else e.effectTag|=Fg,h=!1,f=e}},prepareToHydrateHostInstance:function(e,t,n){return t=c(e.stateNode,e.type,e.memoizedProps,t,n,e),e.updateQueue=t,null!==t},prepareToHydrateHostTextInstance:function(e){return s(e.stateNode,e.memoizedProps,e)},popHydrationState:function(e){if(e!==f)return!1;if(!h)return r(e),h=!0,!1;var n=e.type;if(e.tag!==Bg||"head"!==n&&"body"!==n&&!o(n,e.memoizedProps))for(n=g;n;)t(e,n),n=l(n);return r(e),g=f?l(e.stateNode):null,!0}}}function hh(e){function t(){for(;null!==q&&q.current.pendingWorkPriority===T;){q.isScheduled=!1;var e=q.nextScheduledRoot;if(q.nextScheduledRoot=null,q===Q)return Q=q=null,z=T,null;q=e}e=q;for(var t=null,n=T;null!==e;)e.current.pendingWorkPriority!==T&&(n===T||n>e.current.pendingWorkPriority)&&(n=e.current.pendingWorkPriority,t=e),e=e.nextScheduledRoot;null!==t?(z=n,Jg(),gh(),C(),B=Lg(t.current,n),t!==ae&&(oe=0,ae=t)):(z=T,ae=B=null)}function n(n){te=!0,Y=null;var r=n.stateNode;if(r.current===n&&w("177"),z!==Og&&z!==U||oe++,Kg.current=null,n.effectTag>Tg)if(null!==n.lastEffect){n.lastEffect.nextEffect=n;var o=n.firstEffect}else o=n;else o=n.firstEffect;for(D(),K=o;null!==K;){var a=!1,i=void 0;try{for(;null!==K;){var l=K.effectTag;if(l&Yg&&e.resetTextContent(K.stateNode),l&ah){var u=K.alternate;null!==u&&M(u)}switch(l&~(Zg|$g|Yg|ah|Tg)){case Ug:x(K),K.effectTag&=~Ug;break;case Wg:x(K),K.effectTag&=~Ug,F(K.alternate,K);break;case Vg:F(K.alternate,K);break;case Xg:ne=!0,S(K),ne=!1}K=K.nextEffect}}catch(e){a=!0,i=e}a&&(null===K&&w("178"),s(K,i),null!==K&&(K=K.nextEffect))}for(R(),r.current=n,K=o;null!==K;){r=!1,o=void 0;try{for(;null!==K;){var c=K.effectTag;if(c&(Vg|Zg)&&_(K.alternate,K),c&ah&&j(K),c&$g)switch(a=K,i=void 0,null!==X&&(i=X.get(a),X.delete(a),null==i&&null!==a.alternate&&(a=a.alternate,i=X.get(a),X.delete(a))),null==i&&w("184"),a.tag){case eh:a.stateNode.componentDidCatch(i.error,{componentStack:i.componentStack});break;case bh:null===$&&($=i.error);break;default:w("157")}var d=K.nextEffect;K.nextEffect=null,K=d}}catch(e){r=!0,o=e}r&&(null===K&&w("178"),s(K,o),null!==K&&(K=K.nextEffect))}te=!1,"function"==typeof Ng&&Ng(n.stateNode),G&&(G.forEach(h),G=null),t()}function r(e){for(;;){var t=N(e.alternate,e,z),n=e.return,r=e.sibling,o=e;if(!(o.pendingWorkPriority!==T&&o.pendingWorkPriority>z)){for(var a=fh(o),i=o.child;null!==i;)a=Mg(a,i.pendingWorkPriority),i=i.sibling;o.pendingWorkPriority=a}if(null!==t)return t;if(null!==n&&(null===n.firstEffect&&(n.firstEffect=e.firstEffect),null!==e.lastEffect&&(null!==n.lastEffect&&(n.lastEffect.nextEffect=e.firstEffect),n.lastEffect=e.lastEffect),e.effectTag>Tg&&(null!==n.lastEffect?n.lastEffect.nextEffect=e:n.firstEffect=e,n.lastEffect=e)),null!==r)return r;if(null===n){Y=e;break}e=n}return null}function o(e){var t=k(e.alternate,e,z);return null===t&&(t=r(e)),Kg.current=null,t}function a(e){var t=P(e.alternate,e,z);return null===t&&(t=r(e)),Kg.current=null,t}function i(e){c(Rg,e)}function l(){if(null!==X&&0<X.size&&z===U)for(;null!==B;){var e=B;if(null===(B=null!==X&&(X.has(e)||null!==e.alternate&&X.has(e.alternate))?a(B):o(B))&&(null===Y&&w("179"),A=U,n(Y),A=z,null===X||0===X.size||z!==U))break}}function u(e,r){if(null!==Y?(A=U,n(Y),l()):null===B&&t(),!(z===T||z>e)){A=z;e:for(;;){if(z<=U)for(;null!==B&&!(null===(B=o(B))&&(null===Y&&w("179"),A=U,n(Y),A=z,l(),z===T||z>e||z>U)););else if(null!==r)for(;null!==B&&!H;)if(1<r.timeRemaining()){if(null===(B=o(B)))if(null===Y&&w("179"),1<r.timeRemaining()){if(A=U,n(Y),A=z,l(),z===T||z>e||z<Pg)break}else H=!0}else H=!0;switch(z){case Og:case U:if(z<=e)continue e;break e;case Pg:case Qg:case Rg:if(null===r)break e;if(!H&&z<=e)continue e;break e;case T:break e;default:w("181")}}}}function c(e,t){L&&w("182"),L=!0;var n=A,r=!1,o=null;try{u(e,t)}catch(e){r=!0,o=e}for(;r;){if(ee){$=o;break}var l=B;if(null===l)ee=!0;else{var c=s(l,o);if(null===c&&w("183"),!ee){try{r=c,o=e,c=t;for(var d=r;null!==l;){switch(l.tag){case eh:Ig(l);break;case ch:v(l);break;case bh:y(l);break;case dh:y(l)}if(l===d||l.alternate===d)break;l=l.return}B=a(r),u(o,c)}catch(e){r=!0,o=e;continue}break}}}if(A=n,null!==t&&(J=!1),z>U&&!J&&(I(i),J=!0),e=$,ee=H=L=!1,ae=Z=X=$=null,oe=0,null!==e)throw e}function s(e,t){var n=Kg.current=null,r=!1,o=!1,a=null;if(e.tag===bh)n=e,d(e)&&(ee=!0);else for(var i=e.return;null!==i&&null===n;){if(i.tag===eh?"function"==typeof i.stateNode.componentDidCatch&&(r=!0,a=Ra(i),n=i,o=!0):i.tag===bh&&(n=i),d(i)){if(ne||null!==G&&(G.has(i)||null!==i.alternate&&G.has(i.alternate)))return null;n=null,o=!1}i=i.return}if(null!==n){null===Z&&(Z=new Set),Z.add(n);var l="";i=e;do{e:switch(i.tag){case fe:case ge:case he:case ie:var u=i._debugOwner,c=i._debugSource,s=Ra(i),p=null;u&&(p=Ra(u)),u=c,s="\n    in "+(s||"Unknown")+(u?" (at "+u.fileName.replace(/^.*[\\\/]/,"")+":"+u.lineNumber+")":p?" (created by "+p+")":"");break e;default:s=""}l+=s,i=i.return}while(i);i=l,e=Ra(e),null===X&&(X=new Map),t={componentName:e,componentStack:i,error:t,errorBoundary:r?n.stateNode:null,errorBoundaryFound:r,errorBoundaryName:a,willRetry:o},X.set(n,t);try{console.error(t.error)}catch(e){console.error(e)}return te?(null===G&&(G=new Set),G.add(n)):h(n),n}return null===$&&($=t),null}function d(e){return null!==Z&&(Z.has(e)||null!==e.alternate&&Z.has(e.alternate))}function p(e,t){return f(e,t,!1)}function f(e,t){oe>re&&(ee=!0,w("185")),!L&&t<=z&&(B=null);for(var n=!0;null!==e&&n;){if(n=!1,(e.pendingWorkPriority===T||e.pendingWorkPriority>t)&&(n=!0,e.pendingWorkPriority=t),null!==e.alternate&&(e.alternate.pendingWorkPriority===T||e.alternate.pendingWorkPriority>t)&&(n=!0,e.alternate.pendingWorkPriority=t),null===e.return){if(e.tag!==bh)break;var r=e.stateNode;if(t===T||r.isScheduled||(r.isScheduled=!0,Q?Q.nextScheduledRoot=r:q=r,Q=r),!L)switch(t){case Og:V?c(Og,null):c(U,null);break;case U:W||w("186");break;default:J||(I(i),J=!0)}}e=e.return}}function g(e,t){var n=A;return n===T&&(n=!O||e.internalContextTag&Sg||t?Qg:Og),n===Og&&(L||W)?U:n}function h(e){f(e,U,!0)}var b=Ag(e),m=Hg(e),y=b.popHostContainer,v=b.popHostContext,C=b.resetHostContainer,E=Lf(e,b,m,p,g),k=E.beginWork,P=E.beginFailedWork,N=eg(e,b,m).completeWork,x=(b=vg(e,s)).commitPlacement,S=b.commitDeletion,F=b.commitWork,_=b.commitLifeCycles,j=b.commitAttachRef,M=b.commitDetachRef,I=e.scheduleDeferredCallback,O=e.useSyncScheduling,D=e.prepareForCommit,R=e.resetAfterCommit,A=T,L=!1,H=!1,W=!1,V=!1,B=null,z=T,K=null,Y=null,q=null,Q=null,J=!1,X=null,Z=null,G=null,$=null,ee=!1,te=!1,ne=!1,re=1e3,oe=0,ae=null;return{scheduleUpdate:p,getPriorityContext:g,batchedUpdates:function(e,t){var n=W;W=!0;try{return e(t)}finally{W=n,L||W||c(U,null)}},unbatchedUpdates:function(e){var t=V,n=W;V=W,W=!1;try{return e()}finally{W=n,V=t}},flushSync:function(e){var t=W,n=A;W=!0,A=Og;try{return e()}finally{W=t,A=n,L&&w("187"),c(U,null)}},deferredUpdates:function(e){var t=A;A=Qg;try{return e()}finally{A=t}}}}function ih(){w("196")}function jh(e){return e?"number"==typeof(e=Pa.get(e)).tag?ih(e):e._processChildContext(e._context):da}function sh(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function th(e,t){var n=sh(e);e=0;for(var r;n;){if(n.nodeType===rh){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=sh(n)}}function vh(){return!uh&&l.canUseDOM&&(uh="textContent"in document.documentElement?"textContent":"innerText"),uh}function Bh(){w("211")}function Ch(){w("212")}function Dh(e){if(null==e)return null;if(e.nodeType===Ah)return e;var t=Pa.get(e);if(t)return"number"==typeof t.tag?Bh(t):Ch(t);"function"==typeof e.render?w("188"):w("213",Object.keys(e))}function Fh(e){if(void 0!==e._hostParent)return e._hostParent;if("number"==typeof e.tag){do{e=e.return}while(e&&e.tag!==Eh);if(e)return e}return null}function Gh(e,t){for(var n=0,r=e;r;r=Fh(r))n++;r=0;for(var o=t;o;o=Fh(o))r++;for(;0<n-r;)e=Fh(e),n--;for(;0<r-n;)t=Fh(t),r--;for(;n--;){if(e===t||e===t.alternate)return e;e=Fh(e),t=Fh(t)}return null}function Jh(e,t,n){(t=Ih(e,n.dispatchConfig.phasedRegistrationNames[t]))&&(n._dispatchListeners=Cb(n._dispatchListeners,t),n._dispatchInstances=Cb(n._dispatchInstances,e))}function Kh(e){e&&e.dispatchConfig.phasedRegistrationNames&&Hh.traverseTwoPhase(e._targetInst,Jh,e)}function Qh(e){if(e&&e.dispatchConfig.phasedRegistrationNames){var t=e._targetInst;t=t?Hh.getParentInstance(t):null,Hh.traverseTwoPhase(t,Jh,e)}}function Rh(e,t,n){e&&n&&n.dispatchConfig.registrationName&&(t=Ih(e,n.dispatchConfig.registrationName))&&(n._dispatchListeners=Cb(n._dispatchListeners,t),n._dispatchInstances=Cb(n._dispatchInstances,e))}function Sh(e){e&&e.dispatchConfig.registrationName&&Rh(e._targetInst,null,e)}function Y(e,t,n,r){this.dispatchConfig=e,this._targetInst=t,this.nativeEvent=n,e=this.constructor.Interface;for(var o in e)e.hasOwnProperty(o)&&((t=e[o])?this[o]=t(n):"target"===o?this.target=r:this[o]=n[o]);return this.isDefaultPrevented=(null!=n.defaultPrevented?n.defaultPrevented:!1===n.returnValue)?ca.thatReturnsTrue:ca.thatReturnsFalse,this.isPropagationStopped=ca.thatReturnsFalse,this}function Zh(e,t,n,r){if(this.eventPool.length){var o=this.eventPool.pop();return this.call(o,e,t,n,r),o}return new this(e,t,n,r)}function $h(e){e instanceof this||w("223"),e.destructor(),10>this.eventPool.length&&this.eventPool.push(e)}function Yh(e){e.eventPool=[],e.getPooled=Zh,e.release=$h}function ai(e,t,n,r){return Y.call(this,e,t,n,r)}function bi(e,t,n,r){return Y.call(this,e,t,n,r)}function mi(e,t){switch(e){case"topKeyUp":return-1!==ci.indexOf(t.keyCode);case"topKeyDown":return 229!==t.keyCode;case"topKeyPress":case"topMouseDown":case"topBlur":return!0;default:return!1}}function ni(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}function pi(e,t){switch(e){case"topCompositionEnd":return ni(t);case"topKeyPress":return 32!==t.which?null:(li=!0,ji);case"topTextInput":return(e=t.data)===ji&&li?null:e;default:return null}}function qi(e,t){if(oi)return"topCompositionEnd"===e||!di&&mi(e,t)?(e=Vh.getData(),Vh.reset(),oi=!1,e):null;switch(e){case"topPaste":return null;case"topKeyPress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"topCompositionEnd":return ii?null:t.data;default:return null}}function ti(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!si[e.type]:"textarea"===t}function vi(e,t,n){return e=Y.getPooled(ui.change,e,t,n),e.type="change",nb.enqueueStateRestore(n),Th.accumulateTwoPhaseDispatches(e),e}function yi(e){Jb.enqueueEvents(e),Jb.processEventQueue(!1)}function zi(e){var t=G.getNodeFromInstance(e);if(Bc.updateValueIfChanged(t))return e}function Ai(e,t){if("topChange"===e)return t}function Ci(){wi&&(wi.detachEvent("onpropertychange",Di),xi=wi=null)}function Di(e){"value"===e.propertyName&&zi(xi)&&(e=vi(xi,e,ub(e)),sb.batchedUpdates(yi,e))}function Ei(e,t,n){"topFocus"===e?(Ci(),wi=t,xi=n,wi.attachEvent("onpropertychange",Di)):"topBlur"===e&&Ci()}function Fi(e){if("topSelectionChange"===e||"topKeyUp"===e||"topKeyDown"===e)return zi(xi)}function Gi(e,t){if("topClick"===e)return zi(t)}function Hi(e,t){if("topInput"===e||"topChange"===e)return zi(t)}function Ji(e,t,n,r){return Y.call(this,e,t,n,r)}function Li(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Ki[e])&&!!t[e]}function Mi(){return Li}function Ni(e,t,n,r){return Y.call(this,e,t,n,r)}function cj(e,t){if(aj||null==Ti||Ti!==ia())return null;var n=Ti;return"selectionStart"in n&&zh.hasSelectionCapabilities(n)?n={start:n.selectionStart,end:n.selectionEnd}:window.getSelection?(n=window.getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}):n=void 0,$i&&ea($i,n)?null:($i=n,e=Y.getPooled(Si.select,Zi,e,t),e.type="select",e.target=Ti,Th.accumulateTwoPhaseDispatches(e),e)}function ej(e,t,n,r){return Y.call(this,e,t,n,r)}function fj(e,t,n,r){return Y.call(this,e,t,n,r)}function gj(e,t,n,r){return Y.call(this,e,t,n,r)}function hj(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,32<=e||13===e?e:0}function kj(e,t,n,r){return Y.call(this,e,t,n,r)}function lj(e,t,n,r){return Y.call(this,e,t,n,r)}function mj(e,t,n,r){return Y.call(this,e,t,n,r)}function nj(e,t,n,r){return Y.call(this,e,t,n,r)}function oj(e,t,n,r){return Y.call(this,e,t,n,r)}function Bj(e){return e[1].toUpperCase()}function ak(e){return!(!e||e.nodeType!==Dj&&e.nodeType!==Gj&&e.nodeType!==Hj&&(e.nodeType!==Fj||" react-mount-point-unstable "!==e.nodeValue))}function bk(e){return!(!(e=e?e.nodeType===Gj?e.documentElement:e.firstChild:null)||e.nodeType!==Dj||!e.hasAttribute(Ij))}function ck(e,t,n,r,o){ak(n)||w("200");var a=n._reactRootContainer;if(a)Xj.updateContainer(t,a,e,o);else{if(!r&&!bk(n))for(r=void 0;r=n.lastChild;)n.removeChild(r);var i=Xj.createContainer(n);a=n._reactRootContainer=i,Xj.unbatchedUpdates(function(){Xj.updateContainer(t,i,e,o)})}return Xj.getPublicRootInstance(a)}function dk(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;return ak(t)||w("200"),ne.createPortal(e,t,null,n)}var aa=require("react");require("fbjs/lib/invariant");var l=require("fbjs/lib/ExecutionEnvironment"),n=require("object-assign"),ba=require("fbjs/lib/EventListener"),ca=require("fbjs/lib/emptyFunction"),da=require("fbjs/lib/emptyObject"),ea=require("fbjs/lib/shallowEqual"),fa=require("fbjs/lib/containsNode"),ha=require("fbjs/lib/focusNode"),ia=require("fbjs/lib/getActiveElement");aa||w("227");var ka={Namespaces:{html:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg"},getIntrinsicNamespace:ja,getChildNamespace:function(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ja(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}},la=null,oa={},qa={plugins:[],eventNameDispatchConfigs:{},registrationNameModules:{},registrationNameDependencies:{},possibleRegistrationNames:null,injectEventPluginOrder:function(e){la&&w("101"),la=Array.prototype.slice.call(e),pa()},injectEventPluginsByName:function(e){var t,n=!1;for(t in e)if(e.hasOwnProperty(t)){var r=e[t];oa.hasOwnProperty(t)&&oa[t]===r||(oa[t]&&w("102",t),oa[t]=r,n=!0)}n&&pa()}},sa=qa,ta={children:!0,dangerouslySetInnerHTML:!0,autoFocus:!0,defaultValue:!0,defaultChecked:!0,innerHTML:!0,suppressContentEditableWarning:!0,style:!0},wa={MUST_USE_PROPERTY:1,HAS_BOOLEAN_VALUE:4,HAS_NUMERIC_VALUE:8,HAS_POSITIVE_NUMERIC_VALUE:24,HAS_OVERLOADED_BOOLEAN_VALUE:32,HAS_STRING_BOOLEAN_VALUE:64,injectDOMPropertyConfig:function(e){var t=wa,n=e.Properties||{},r=e.DOMAttributeNamespaces||{},o=e.DOMAttributeNames||{};e=e.DOMMutationMethods||{};for(var a in n){xa.properties.hasOwnProperty(a)&&w("48",a);var i=a.toLowerCase(),l=n[a];1>=(i={attributeName:i,attributeNamespace:null,propertyName:a,mutationMethod:null,mustUseProperty:ua(l,t.MUST_USE_PROPERTY),hasBooleanValue:ua(l,t.HAS_BOOLEAN_VALUE),hasNumericValue:ua(l,t.HAS_NUMERIC_VALUE),hasPositiveNumericValue:ua(l,t.HAS_POSITIVE_NUMERIC_VALUE),hasOverloadedBooleanValue:ua(l,t.HAS_OVERLOADED_BOOLEAN_VALUE),hasStringBooleanValue:ua(l,t.HAS_STRING_BOOLEAN_VALUE)}).hasBooleanValue+i.hasNumericValue+i.hasOverloadedBooleanValue||w("50",a),o.hasOwnProperty(a)&&(i.attributeName=o[a]),r.hasOwnProperty(a)&&(i.attributeNamespace=r[a]),e.hasOwnProperty(a)&&(i.mutationMethod=e[a]),xa.properties[a]=i}}},xa={ID_ATTRIBUTE_NAME:"data-reactid",ROOT_ATTRIBUTE_NAME:"data-reactroot",ATTRIBUTE_NAME_START_CHAR:":A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",ATTRIBUTE_NAME_CHAR:":A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040",properties:{},shouldSetAttribute:function(e,t){if(xa.isReservedProp(e)||!("o"!==e[0]&&"O"!==e[0]||"n"!==e[1]&&"N"!==e[1]))return!1;if(null===t)return!0;switch(typeof t){case"boolean":return xa.shouldAttributeAcceptBooleanValue(e);case"undefined":case"number":case"string":case"object":return!0;default:return!1}},getPropertyInfo:function(e){return xa.properties.hasOwnProperty(e)?xa.properties[e]:null},shouldAttributeAcceptBooleanValue:function(e){if(xa.isReservedProp(e))return!0;var t=xa.getPropertyInfo(e);return t?t.hasBooleanValue||t.hasStringBooleanValue||t.hasOverloadedBooleanValue:"data-"===(e=e.toLowerCase().slice(0,5))||"aria-"===e},isReservedProp:function(e){return ta.hasOwnProperty(e)},injection:wa},A=xa,E={IndeterminateComponent:0,FunctionalComponent:1,ClassComponent:2,HostRoot:3,HostPortal:4,HostComponent:5,HostText:6,CoroutineComponent:7,CoroutineHandlerPhase:8,YieldComponent:9,Fragment:10},F={ELEMENT_NODE:1,TEXT_NODE:3,COMMENT_NODE:8,DOCUMENT_NODE:9,DOCUMENT_FRAGMENT_NODE:11},ya=E.HostComponent,za=E.HostText,Aa=F.ELEMENT_NODE,Ba=F.COMMENT_NODE,Ea=A.ID_ATTRIBUTE_NAME,Fa={hasCachedChildNodes:1},Ga=Math.random().toString(36).slice(2),Ha="__reactInternalInstance$"+Ga,Ia="__reactEventHandlers$"+Ga,G={getClosestInstanceFromNode:Oa,getInstanceFromNode:function(e){var t=e[Ha];return t?t.tag===ya||t.tag===za?t:t._hostNode===e?t:null:null!=(t=Oa(e))&&t._hostNode===e?t:null},getNodeFromInstance:function(e){if(e.tag===ya||e.tag===za)return e.stateNode;if(void 0===e._hostNode&&w("33"),e._hostNode)return e._hostNode;for(var t=[];!e._hostNode;)t.push(e),e._hostParent||w("34"),e=e._hostParent;for(;t.length;e=t.pop())Na(e,e._hostNode);return e._hostNode},precacheChildNodes:Na,precacheNode:Ma,uncacheNode:function(e){var t=e._hostNode;t&&(delete t[Ha],e._hostNode=null)},precacheFiberNode:function(e,t){t[Ha]=e},getFiberCurrentPropsFromNode:function(e){return e[Ia]||null},updateFiberProps:function(e,t){e[Ia]=t}},Pa={remove:function(e){e._reactInternalFiber=void 0},get:function(e){return e._reactInternalFiber},has:function(e){return void 0!==e._reactInternalFiber},set:function(e,t){e._reactInternalFiber=t}},Qa={ReactCurrentOwner:aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner},J={NoEffect:0,PerformedWork:1,Placement:2,Update:4,PlacementAndUpdate:6,Deletion:8,ContentReset:16,Callback:32,Err:64,Ref:128},Sa=E.HostComponent,Ta=E.HostRoot,Ua=E.HostPortal,Va=E.HostText,Wa=J.NoEffect,Xa=J.Placement,bb={isFiberMounted:function(e){return 2===Za(e)},isMounted:function(e){return!!(e=Pa.get(e))&&2===Za(e)},findCurrentFiberUsingSlowPath:ab,findCurrentHostFiber:function(e){if(!(e=ab(e)))return null;for(var t=e;;){if(t.tag===Sa||t.tag===Va)return t;if(t.child)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null},findCurrentHostFiberWithNoPortals:function(e){if(!(e=ab(e)))return null;for(var t=e;;){if(t.tag===Sa||t.tag===Va)return t;if(t.child&&t.tag!==Ua)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null}},K={_caughtError:null,_hasCaughtError:!1,_rethrowError:null,_hasRethrowError:!1,injection:{injectErrorUtils:function(e){"function"!=typeof e.invokeGuardedCallback&&w("197"),cb=e.invokeGuardedCallback}},invokeGuardedCallback:function(e,t,n,r,o,a,i,l,u){cb.apply(K,arguments)},invokeGuardedCallbackAndCatchFirstError:function(e,t,n,r,o,a,i,l,u){if(K.invokeGuardedCallback.apply(this,arguments),K.hasCaughtError()){var c=K.clearCaughtError();K._hasRethrowError||(K._hasRethrowError=!0,K._rethrowError=c)}},rethrowCaughtError:function(){return db.apply(K,arguments)},hasCaughtError:function(){return K._hasCaughtError},clearCaughtError:function(){if(K._hasCaughtError){var e=K._caughtError;return K._caughtError=null,K._hasCaughtError=!1,e}w("198")}},eb=K,fb,hb={isEndish:function(e){return"topMouseUp"===e||"topTouchEnd"===e||"topTouchCancel"===e},isMoveish:function(e){return"topMouseMove"===e||"topTouchMove"===e},isStartish:function(e){return"topMouseDown"===e||"topTouchStart"===e},executeDirectDispatch:function(e){var t=e._dispatchListeners,n=e._dispatchInstances;return Array.isArray(t)&&w("103"),e.currentTarget=t?hb.getNodeFromInstance(n):null,t=t?t(e):null,e.currentTarget=null,e._dispatchListeners=null,e._dispatchInstances=null,t},executeDispatchesInOrder:function(e,t){var n=e._dispatchListeners,r=e._dispatchInstances;if(Array.isArray(n))for(var o=0;o<n.length&&!e.isPropagationStopped();o++)gb(e,t,n[o],r[o]);else n&&gb(e,t,n,r);e._dispatchListeners=null,e._dispatchInstances=null},executeDispatchesInOrderStopAtTrue:function(e){e:{var t=e._dispatchListeners,n=e._dispatchInstances;if(Array.isArray(t)){for(var r=0;r<t.length&&!e.isPropagationStopped();r++)if(t[r](e,n[r])){t=n[r];break e}}else if(t&&t(e,n)){t=n;break e}t=null}return e._dispatchInstances=null,e._dispatchListeners=null,t},hasDispatches:function(e){return!!e._dispatchListeners},getFiberCurrentPropsFromNode:function(e){return fb.getFiberCurrentPropsFromNode(e)},getInstanceFromNode:function(e){return fb.getInstanceFromNode(e)},getNodeFromInstance:function(e){return fb.getNodeFromInstance(e)},injection:{injectComponentTree:function(e){fb=e}}},ib=hb,jb=null,kb=null,lb=null,nb={injection:{injectFiberControlledHostComponent:function(e){jb=e}},enqueueStateRestore:function(e){kb?lb?lb.push(e):lb=[e]:kb=e},restoreStateIfNeeded:function(){if(kb){var e=kb,t=lb;if(lb=kb=null,mb(e),t)for(e=0;e<t.length;e++)mb(t[e])}}},rb=!1,sb={batchedUpdates:function(e,t){if(rb)return ob(qb,e,t);rb=!0;try{return ob(qb,e,t)}finally{rb=!1,nb.restoreStateIfNeeded()}},injection:{injectStackBatchedUpdates:function(e){ob=e},injectFiberBatchedUpdates:function(e){pb=e}}},tb=F.TEXT_NODE,vb=E.HostRoot,wb=[],yb={_enabled:!0,_handleTopLevel:null,setHandleTopLevel:function(e){yb._handleTopLevel=e},setEnabled:function(e){yb._enabled=!!e},isEnabled:function(){return yb._enabled},trapBubbledEvent:function(e,t,n){return n?ba.listen(n,t,yb.dispatchEvent.bind(null,e)):null},trapCapturedEvent:function(e,t,n){return n?ba.capture(n,t,yb.dispatchEvent.bind(null,e)):null},dispatchEvent:function(e,t){if(yb._enabled){var n=ub(t);if(null===(n=G.getClosestInstanceFromNode(n))||"number"!=typeof n.tag||bb.isFiberMounted(n)||(n=null),wb.length){var r=wb.pop();r.topLevelType=e,r.nativeEvent=t,r.targetInst=n,e=r}else e={topLevelType:e,nativeEvent:t,targetInst:n,ancestors:[]};try{sb.batchedUpdates(xb,e)}finally{e.topLevelType=null,e.nativeEvent=null,e.targetInst=null,e.ancestors.length=0,10>wb.length&&wb.push(e)}}}},L=yb,Eb=null,Jb={injection:{injectEventPluginOrder:sa.injectEventPluginOrder,injectEventPluginsByName:sa.injectEventPluginsByName},getListener:function(e,t){if("number"==typeof e.tag){var n=e.stateNode;if(!n)return null;var r=ib.getFiberCurrentPropsFromNode(n);if(!r)return null;if(n=r[t],Ib(t,e.type,r))return null}else{if("string"==typeof(r=e._currentElement)||"number"==typeof r||!e._rootNodeID)return null;if(e=r.props,n=e[t],Ib(t,r.type,e))return null}return n&&"function"!=typeof n&&w("231",t,typeof n),n},extractEvents:function(e,t,n,r){for(var o,a=sa.plugins,i=0;i<a.length;i++){var l=a[i];l&&(l=l.extractEvents(e,t,n,r))&&(o=Cb(o,l))}return o},enqueueEvents:function(e){e&&(Eb=Cb(Eb,e))},processEventQueue:function(e){var t=Eb;Eb=null,e?Db(t,Gb):Db(t,Hb),Eb&&w("95"),eb.rethrowCaughtError()}},Kb;l.canUseDOM&&(Kb=document.implementation&&document.implementation.hasFeature&&!0!==document.implementation.hasFeature("",""));var Nb={animationend:Mb("Animation","AnimationEnd"),animationiteration:Mb("Animation","AnimationIteration"),animationstart:Mb("Animation","AnimationStart"),transitionend:Mb("Transition","TransitionEnd")},Ob={},Pb={};l.canUseDOM&&(Pb=document.createElement("div").style,"AnimationEvent"in window||(delete Nb.animationend.animation,delete Nb.animationiteration.animation,delete Nb.animationstart.animation),"TransitionEvent"in window||delete Nb.transitionend.transition);var Rb={topAbort:"abort",topAnimationEnd:Qb("animationend")||"animationend",topAnimationIteration:Qb("animationiteration")||"animationiteration",topAnimationStart:Qb("animationstart")||"animationstart",topBlur:"blur",topCancel:"cancel",topCanPlay:"canplay",topCanPlayThrough:"canplaythrough",topChange:"change",topClick:"click",topClose:"close",topCompositionEnd:"compositionend",topCompositionStart:"compositionstart",topCompositionUpdate:"compositionupdate",topContextMenu:"contextmenu",topCopy:"copy",topCut:"cut",topDoubleClick:"dblclick",topDrag:"drag",topDragEnd:"dragend",topDragEnter:"dragenter",topDragExit:"dragexit",topDragLeave:"dragleave",topDragOver:"dragover",topDragStart:"dragstart",topDrop:"drop",topDurationChange:"durationchange",topEmptied:"emptied",topEncrypted:"encrypted",topEnded:"ended",topError:"error",topFocus:"focus",topInput:"input",topKeyDown:"keydown",topKeyPress:"keypress",topKeyUp:"keyup",topLoadedData:"loadeddata",topLoad:"load",topLoadedMetadata:"loadedmetadata",topLoadStart:"loadstart",topMouseDown:"mousedown",topMouseMove:"mousemove",topMouseOut:"mouseout",topMouseOver:"mouseover",topMouseUp:"mouseup",topPaste:"paste",topPause:"pause",topPlay:"play",topPlaying:"playing",topProgress:"progress",topRateChange:"ratechange",topScroll:"scroll",topSeeked:"seeked",topSeeking:"seeking",topSelectionChange:"selectionchange",topStalled:"stalled",topSuspend:"suspend",topTextInput:"textInput",topTimeUpdate:"timeupdate",topToggle:"toggle",topTouchCancel:"touchcancel",topTouchEnd:"touchend",topTouchMove:"touchmove",topTouchStart:"touchstart",topTransitionEnd:Qb("transitionend")||"transitionend",topVolumeChange:"volumechange",topWaiting:"waiting",topWheel:"wheel"},Sb={},Tb=0,Ub="_reactListenersID"+(""+Math.random()).slice(2),M=n({},{handleTopLevel:function(e,t,n,r){e=Jb.extractEvents(e,t,n,r),Jb.enqueueEvents(e),Jb.processEventQueue(!1)}},{setEnabled:function(e){L&&L.setEnabled(e)},isEnabled:function(){return!(!L||!L.isEnabled())},listenTo:function(e,t){var n=Vb(t);e=sa.registrationNameDependencies[e];for(var r=0;r<e.length;r++){var o=e[r];n.hasOwnProperty(o)&&n[o]||("topWheel"===o?Lb("wheel")?L.trapBubbledEvent("topWheel","wheel",t):Lb("mousewheel")?L.trapBubbledEvent("topWheel","mousewheel",t):L.trapBubbledEvent("topWheel","DOMMouseScroll",t):"topScroll"===o?L.trapCapturedEvent("topScroll","scroll",t):"topFocus"===o||"topBlur"===o?(L.trapCapturedEvent("topFocus","focus",t),L.trapCapturedEvent("topBlur","blur",t),n.topBlur=!0,n.topFocus=!0):"topCancel"===o?(Lb("cancel",!0)&&L.trapCapturedEvent("topCancel","cancel",t),n.topCancel=!0):"topClose"===o?(Lb("close",!0)&&L.trapCapturedEvent("topClose","close",t),n.topClose=!0):Rb.hasOwnProperty(o)&&L.trapBubbledEvent(o,Rb[o],t),n[o]=!0)}},isListeningToAllDependencies:function(e,t){t=Vb(t),e=sa.registrationNameDependencies[e];for(var n=0;n<e.length;n++){var r=e[n];if(!t.hasOwnProperty(r)||!t[r])return!1}return!0},trapBubbledEvent:function(e,t,n){return L.trapBubbledEvent(e,t,n)},trapCapturedEvent:function(e,t,n){return L.trapCapturedEvent(e,t,n)}}),Wb={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Xb=["Webkit","ms","Moz","O"];Object.keys(Wb).forEach(function(e){Xb.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Wb[t]=Wb[e]})});var Yb={isUnitlessNumber:Wb,shorthandPropertyExpansions:{background:{backgroundAttachment:!0,backgroundColor:!0,backgroundImage:!0,backgroundPositionX:!0,backgroundPositionY:!0,backgroundRepeat:!0},backgroundPosition:{backgroundPositionX:!0,backgroundPositionY:!0},border:{borderWidth:!0,borderStyle:!0,borderColor:!0},borderBottom:{borderBottomWidth:!0,borderBottomStyle:!0,borderBottomColor:!0},borderLeft:{borderLeftWidth:!0,borderLeftStyle:!0,borderLeftColor:!0},borderRight:{borderRightWidth:!0,borderRightStyle:!0,borderRightColor:!0},borderTop:{borderTopWidth:!0,borderTopStyle:!0,borderTopColor:!0},font:{fontStyle:!0,fontVariant:!0,fontWeight:!0,fontSize:!0,lineHeight:!0,fontFamily:!0},outline:{outlineWidth:!0,outlineStyle:!0,outlineColor:!0}}},Zb=Yb.isUnitlessNumber,$b=!1;if(l.canUseDOM){var ac=document.createElement("div").style;try{ac.font=""}catch(e){$b=!0}}var bc={createDangerousStringForStyles:function(){},setValueForStyles:function(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=n,a=t[n];if(o=null==a||"boolean"==typeof a||""===a?"":r||"number"!=typeof a||0===a||Zb.hasOwnProperty(o)&&Zb[o]?(""+a).trim():a+"px","float"===n&&(n="cssFloat"),r)e.setProperty(n,o);else if(o)e[n]=o;else if(r=$b&&Yb.shorthandPropertyExpansions[n])for(var i in r)e[i]="";else e[n]=""}}},cc=new RegExp("^["+A.ATTRIBUTE_NAME_START_CHAR+"]["+A.ATTRIBUTE_NAME_CHAR+"]*$"),dc={},ec={},gc={setAttributeForID:function(e,t){e.setAttribute(A.ID_ATTRIBUTE_NAME,t)},setAttributeForRoot:function(e){e.setAttribute(A.ROOT_ATTRIBUTE_NAME,"")},getValueForProperty:function(){},getValueForAttribute:function(){},setValueForProperty:function(e,t,n){var r=A.getPropertyInfo(t);if(r&&A.shouldSetAttribute(t,n)){var o=r.mutationMethod;o?o(e,n):null==n||r.hasBooleanValue&&!n||r.hasNumericValue&&isNaN(n)||r.hasPositiveNumericValue&&1>n||r.hasOverloadedBooleanValue&&!1===n?gc.deleteValueForProperty(e,t):r.mustUseProperty?e[r.propertyName]=n:(t=r.attributeName,(o=r.attributeNamespace)?e.setAttributeNS(o,t,""+n):r.hasBooleanValue||r.hasOverloadedBooleanValue&&!0===n?e.setAttribute(t,""):e.setAttribute(t,""+n))}else gc.setValueForAttribute(e,t,A.shouldSetAttribute(t,n)?n:null)},setValueForAttribute:function(e,t,n){fc(t)&&(null==n?e.removeAttribute(t):e.setAttribute(t,""+n))},deleteValueForAttribute:function(e,t){e.removeAttribute(t)},deleteValueForProperty:function(e,t){var n=A.getPropertyInfo(t);n?(t=n.mutationMethod)?t(e,void 0):n.mustUseProperty?e[n.propertyName]=!n.hasBooleanValue&&"":e.removeAttribute(n.attributeName):e.removeAttribute(t)}},hc=gc,ic=Qa.ReactDebugCurrentFrame,kc={current:null,phase:null,resetCurrentFiber:function(){ic.getCurrentStack=null,kc.current=null,kc.phase=null},setCurrentFiber:function(e,t){ic.getCurrentStack=jc,kc.current=e,kc.phase=t},getCurrentFiberOwnerName:function(){return null},getCurrentFiberStackAddendum:jc},lc=kc,mc={getHostProps:function(e,t){var r=t.value,o=t.checked;return n({type:void 0,step:void 0,min:void 0,max:void 0},t,{defaultChecked:void 0,defaultValue:void 0,value:null!=r?r:e._wrapperState.initialValue,checked:null!=o?o:e._wrapperState.initialChecked})},initWrapperState:function(e,t){var n=t.defaultValue;e._wrapperState={initialChecked:null!=t.checked?t.checked:t.defaultChecked,initialValue:null!=t.value?t.value:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}},updateWrapper:function(e,t){var n=t.checked;null!=n&&hc.setValueForProperty(e,"checked",n||!1),null!=(n=t.value)?0===n&&""===e.value?e.value="0":"number"===t.type?(n!=(t=parseFloat(e.value)||0)||n==t&&e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n):(null==t.value&&null!=t.defaultValue&&e.defaultValue!==""+t.defaultValue&&(e.defaultValue=""+t.defaultValue),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked))},postMountWrapper:function(e,t){switch(t.type){case"submit":case"reset":break;case"color":case"date":case"datetime":case"datetime-local":case"month":case"time":case"week":e.value="",e.value=e.defaultValue;break;default:e.value=e.value}""!==(t=e.name)&&(e.name=""),e.defaultChecked=!e.defaultChecked,e.defaultChecked=!e.defaultChecked,""!==t&&(e.name=t)},restoreControlledState:function(e,t){mc.updateWrapper(e,t);var n=t.name;if("radio"===t.type&&null!=n){for(t=e;t.parentNode;)t=t.parentNode;for(n=t.querySelectorAll("input[name="+JSON.stringify(""+n)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=G.getFiberCurrentPropsFromNode(r);o||w("90"),mc.updateWrapper(r,o)}}}}},qc=mc,sc={validateProps:function(){},postMountWrapper:function(e,t){null!=t.value&&e.setAttribute("value",t.value)},getHostProps:function(e,t){return e=n({children:void 0},t),(t=rc(t.children))&&(e.children=t),e}},uc={getHostProps:function(e,t){return n({},t,{value:void 0})},initWrapperState:function(e,t){var n=t.value;e._wrapperState={initialValue:null!=n?n:t.defaultValue,wasMultiple:!!t.multiple}},postMountWrapper:function(e,t){e.multiple=!!t.multiple;var n=t.value;null!=n?tc(e,!!t.multiple,n):null!=t.defaultValue&&tc(e,!!t.multiple,t.defaultValue)},postUpdateWrapper:function(e,t){e._wrapperState.initialValue=void 0;var n=e._wrapperState.wasMultiple;e._wrapperState.wasMultiple=!!t.multiple;var r=t.value;null!=r?tc(e,!!t.multiple,r):n!==!!t.multiple&&(null!=t.defaultValue?tc(e,!!t.multiple,t.defaultValue):tc(e,!!t.multiple,t.multiple?[]:""))},restoreControlledState:function(e,t){var n=t.value;null!=n&&tc(e,!!t.multiple,n)}},vc={getHostProps:function(e,t){return null!=t.dangerouslySetInnerHTML&&w("91"),n({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})},initWrapperState:function(e,t){var n=t.value,r=n;null==n&&(n=t.defaultValue,null!=(t=t.children)&&(null!=n&&w("92"),Array.isArray(t)&&(1>=t.length||w("93"),t=t[0]),n=""+t),null==n&&(n=""),r=n),e._wrapperState={initialValue:""+r}},updateWrapper:function(e,t){var n=t.value;null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&(e.defaultValue=n)),null!=t.defaultValue&&(e.defaultValue=t.defaultValue)},postMountWrapper:function(e){var t=e.textContent;t===e._wrapperState.initialValue&&(e.value=t)},restoreControlledState:function(e,t){vc.updateWrapper(e,t)}},wc=vc,xc=n({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}),Bc={_getTrackerFromNode:function(e){return e._valueTracker},track:function(e){e._valueTracker||(e._valueTracker=Ac(e))},updateValueIfChanged:function(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=zc(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)},stopTracking:function(e){(e=e._valueTracker)&&e.stopTracking()}},Dc=ka.Namespaces,Ec,Fc=function(e){return"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n)})}:e}(function(e,t){if(e.namespaceURI!==Dc.svg||"innerHTML"in e)e.innerHTML=t;else for(Ec=Ec||document.createElement("div"),Ec.innerHTML="<svg>"+t+"</svg>",t=Ec.firstChild;t.firstChild;)e.appendChild(t.firstChild)}),Gc=/["'&<>]/,Hc=F.TEXT_NODE;l.canUseDOM&&("textContent"in document.documentElement||(Ic=function(e,t){if(e.nodeType===Hc)e.nodeValue=t;else{if("boolean"==typeof t||"number"==typeof t)t=""+t;else{t=""+t;var n=Gc.exec(t);if(n){var r,o="",a=0;for(r=n.index;r<t.length;r++){switch(t.charCodeAt(r)){case 34:n="&quot;";break;case 38:n="&amp;";break;case 39:n="&#x27;";break;case 60:n="&lt;";break;case 62:n="&gt;";break;default:continue}a!==r&&(o+=t.substring(a,r)),a=r+1,o+=n}t=a!==r?o+t.substring(a,r):o}}Fc(e,t)}}));var Jc=Ic,Kc=lc.getCurrentFiberOwnerName,Lc=F.DOCUMENT_NODE,Mc=F.DOCUMENT_FRAGMENT_NODE,Nc=M.listenTo,Oc=sa.registrationNameModules,Pc=ka.Namespaces.html,Qc=ka.getIntrinsicNamespace,Sc={topAbort:"abort",topCanPlay:"canplay",topCanPlayThrough:"canplaythrough",topDurationChange:"durationchange",topEmptied:"emptied",topEncrypted:"encrypted",topEnded:"ended",topError:"error",topLoadedData:"loadeddata",topLoadedMetadata:"loadedmetadata",topLoadStart:"loadstart",topPause:"pause",topPlay:"play",topPlaying:"playing",topProgress:"progress",topRateChange:"ratechange",topSeeked:"seeked",topSeeking:"seeking",topStalled:"stalled",topSuspend:"suspend",topTimeUpdate:"timeupdate",topVolumeChange:"volumechange",topWaiting:"waiting"},N={createElement:function(e,t,n,r){return n=n.nodeType===Lc?n:n.ownerDocument,r===Pc&&(r=Qc(e)),r===Pc?"script"===e?(e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):e="string"==typeof t.is?n.createElement(e,{is:t.is}):n.createElement(e):e=n.createElementNS(r,e),e},createTextNode:function(e,t){return(t.nodeType===Lc?t:t.ownerDocument).createTextNode(e)},setInitialProperties:function(e,t,n,r){var o=Cc(t,n);switch(t){case"iframe":case"object":M.trapBubbledEvent("topLoad","load",e);var a=n;break;case"video":case"audio":for(a in Sc)Sc.hasOwnProperty(a)&&M.trapBubbledEvent(a,Sc[a],e);a=n;break;case"source":M.trapBubbledEvent("topError","error",e),a=n;break;case"img":case"image":M.trapBubbledEvent("topError","error",e),M.trapBubbledEvent("topLoad","load",e),a=n;break;case"form":M.trapBubbledEvent("topReset","reset",e),M.trapBubbledEvent("topSubmit","submit",e),a=n;break;case"details":M.trapBubbledEvent("topToggle","toggle",e),a=n;break;case"input":qc.initWrapperState(e,n),a=qc.getHostProps(e,n),M.trapBubbledEvent("topInvalid","invalid",e),Rc(r,"onChange");break;case"option":sc.validateProps(e,n),a=sc.getHostProps(e,n);break;case"select":uc.initWrapperState(e,n),a=uc.getHostProps(e,n),M.trapBubbledEvent("topInvalid","invalid",e),Rc(r,"onChange");break;case"textarea":wc.initWrapperState(e,n),a=wc.getHostProps(e,n),M.trapBubbledEvent("topInvalid","invalid",e),Rc(r,"onChange");break;default:a=n}yc(t,a,Kc);var i,l=a;for(i in l)if(l.hasOwnProperty(i)){var u=l[i];"style"===i?bc.setValueForStyles(e,u):"dangerouslySetInnerHTML"===i?null!=(u=u?u.__html:void 0)&&Fc(e,u):"children"===i?"string"==typeof u?Jc(e,u):"number"==typeof u&&Jc(e,""+u):"suppressContentEditableWarning"!==i&&(Oc.hasOwnProperty(i)?null!=u&&Rc(r,i):o?hc.setValueForAttribute(e,i,u):null!=u&&hc.setValueForProperty(e,i,u))}switch(t){case"input":Bc.track(e),qc.postMountWrapper(e,n);break;case"textarea":Bc.track(e),wc.postMountWrapper(e,n);break;case"option":sc.postMountWrapper(e,n);break;case"select":uc.postMountWrapper(e,n);break;default:"function"==typeof a.onClick&&(e.onclick=ca)}},diffProperties:function(e,t,n,r,o){var a=null;switch(t){case"input":n=qc.getHostProps(e,n),r=qc.getHostProps(e,r),a=[];break;case"option":n=sc.getHostProps(e,n),r=sc.getHostProps(e,r),a=[];break;case"select":n=uc.getHostProps(e,n),r=uc.getHostProps(e,r),a=[];break;case"textarea":n=wc.getHostProps(e,n),r=wc.getHostProps(e,r),a=[];break;default:"function"!=typeof n.onClick&&"function"==typeof r.onClick&&(e.onclick=ca)}yc(t,r,Kc);var i,l;e=null;for(i in n)if(!r.hasOwnProperty(i)&&n.hasOwnProperty(i)&&null!=n[i])if("style"===i)for(l in t=n[i])t.hasOwnProperty(l)&&(e||(e={}),e[l]="");else"dangerouslySetInnerHTML"!==i&&"children"!==i&&"suppressContentEditableWarning"!==i&&(Oc.hasOwnProperty(i)?a||(a=[]):(a=a||[]).push(i,null));for(i in r){var u=r[i];if(t=null!=n?n[i]:void 0,r.hasOwnProperty(i)&&u!==t&&(null!=u||null!=t))if("style"===i)if(t){for(l in t)!t.hasOwnProperty(l)||u&&u.hasOwnProperty(l)||(e||(e={}),e[l]="");for(l in u)u.hasOwnProperty(l)&&t[l]!==u[l]&&(e||(e={}),e[l]=u[l])}else e||(a||(a=[]),a.push(i,e)),e=u;else"dangerouslySetInnerHTML"===i?(u=u?u.__html:void 0,t=t?t.__html:void 0,null!=u&&t!==u&&(a=a||[]).push(i,""+u)):"children"===i?t===u||"string"!=typeof u&&"number"!=typeof u||(a=a||[]).push(i,""+u):"suppressContentEditableWarning"!==i&&(Oc.hasOwnProperty(i)?(null!=u&&Rc(o,i),a||t===u||(a=[])):(a=a||[]).push(i,u))}return e&&(a=a||[]).push("style",e),a},updateProperties:function(e,t,n,r,o){Cc(n,r),r=Cc(n,o);for(var a=0;a<t.length;a+=2){var i=t[a],l=t[a+1];"style"===i?bc.setValueForStyles(e,l):"dangerouslySetInnerHTML"===i?Fc(e,l):"children"===i?Jc(e,l):r?null!=l?hc.setValueForAttribute(e,i,l):hc.deleteValueForAttribute(e,i):null!=l?hc.setValueForProperty(e,i,l):hc.deleteValueForProperty(e,i)}switch(n){case"input":qc.updateWrapper(e,o),Bc.updateValueIfChanged(e);break;case"textarea":wc.updateWrapper(e,o);break;case"select":uc.postUpdateWrapper(e,o)}},diffHydratedProperties:function(e,t,n,r,o){switch(t){case"iframe":case"object":M.trapBubbledEvent("topLoad","load",e);break;case"video":case"audio":for(var a in Sc)Sc.hasOwnProperty(a)&&M.trapBubbledEvent(a,Sc[a],e);break;case"source":M.trapBubbledEvent("topError","error",e);break;case"img":case"image":M.trapBubbledEvent("topError","error",e),M.trapBubbledEvent("topLoad","load",e);break;case"form":M.trapBubbledEvent("topReset","reset",e),M.trapBubbledEvent("topSubmit","submit",e);break;case"details":M.trapBubbledEvent("topToggle","toggle",e);break;case"input":qc.initWrapperState(e,n),M.trapBubbledEvent("topInvalid","invalid",e),Rc(o,"onChange");break;case"option":sc.validateProps(e,n);break;case"select":uc.initWrapperState(e,n),M.trapBubbledEvent("topInvalid","invalid",e),Rc(o,"onChange");break;case"textarea":wc.initWrapperState(e,n),M.trapBubbledEvent("topInvalid","invalid",e),Rc(o,"onChange")}yc(t,n,Kc),r=null;for(var i in n)n.hasOwnProperty(i)&&(a=n[i],"children"===i?"string"==typeof a?e.textContent!==a&&(r=["children",a]):"number"==typeof a&&e.textContent!==""+a&&(r=["children",""+a]):Oc.hasOwnProperty(i)&&null!=a&&Rc(o,i));switch(t){case"input":Bc.track(e),qc.postMountWrapper(e,n);break;case"textarea":Bc.track(e),wc.postMountWrapper(e,n);break;case"select":case"option":break;default:"function"==typeof n.onClick&&(e.onclick=ca)}return r},diffHydratedText:function(e,t){return e.nodeValue!==t},warnForDeletedHydratableElement:function(){},warnForDeletedHydratableText:function(){},warnForInsertedHydratedElement:function(){},warnForInsertedHydratedText:function(){},restoreControlledState:function(e,t,n){switch(t){case"input":qc.restoreControlledState(e,n);break;case"textarea":wc.restoreControlledState(e,n);break;case"select":uc.restoreControlledState(e,n)}}},Tc=void 0;if(l.canUseDOM)if("function"!=typeof requestIdleCallback){var Uc=null,Vc=null,Wc=!1,Xc=!1,Yc=0,Zc=33,$c=33,ad={timeRemaining:"object"==typeof performance&&"function"==typeof performance.now?function(){return Yc-performance.now()}:function(){return Yc-Date.now()}},bd="__reactIdleCallback$"+Math.random().toString(36).slice(2);window.addEventListener("message",function(e){e.source===window&&e.data===bd&&(Wc=!1,e=Vc,Vc=null,null!==e&&e(ad))},!1);var cd=function(e){Xc=!1;var t=e-Yc+$c;t<$c&&Zc<$c?(8>t&&(t=8),$c=t<Zc?Zc:t):Zc=t,Yc=e+$c,Wc||(Wc=!0,window.postMessage(bd,"*")),t=Uc,Uc=null,null!==t&&t(e)};Tc=function(e){return Vc=e,Xc||(Xc=!0,requestAnimationFrame(cd)),0}}else Tc=requestIdleCallback;else Tc=function(e){return setTimeout(function(){e({timeRemaining:function(){return 1/0}})}),0};var dd={rIC:Tc},ed={enableAsyncSubtreeAPI:!0},Q={NoWork:0,SynchronousPriority:1,TaskPriority:2,HighPriority:3,LowPriority:4,OffscreenPriority:5},fd=J.Callback,gd=Q.NoWork,hd=Q.SynchronousPriority,id=Q.TaskPriority,jd=E.ClassComponent,kd=E.HostRoot,md=void 0,nd=void 0,ud={addUpdate:function(e,t,n,r){sd(e,{priorityLevel:r,partialState:t,callback:n,isReplace:!1,isForced:!1,isTopLevelUnmount:!1,next:null})},addReplaceUpdate:function(e,t,n,r){sd(e,{priorityLevel:r,partialState:t,callback:n,isReplace:!0,isForced:!1,isTopLevelUnmount:!1,next:null})},addForceUpdate:function(e,t,n){sd(e,{priorityLevel:n,partialState:null,callback:t,isReplace:!1,isForced:!0,isTopLevelUnmount:!1,next:null})},getUpdatePriority:function(e){var t=e.updateQueue;return null===t||e.tag!==jd&&e.tag!==kd?gd:null!==t.first?t.first.priorityLevel:gd},addTopLevelUpdate:function(e,t,n,r){var o=null===t.element;e=sd(e,t={priorityLevel:r,partialState:t,callback:n,isReplace:!1,isForced:!1,isTopLevelUnmount:o,next:null}),o&&(o=md,n=nd,null!==o&&null!==t.next&&(t.next=null,o.last=t),null!==n&&null!==e&&null!==e.next&&(e.next=null,n.last=t))},beginUpdateQueue:function(e,t,r,o,a,i,l){null!==e&&e.updateQueue===r&&(r=t.updateQueue={first:r.first,last:r.last,callbackList:null,hasForceUpdate:!1}),e=r.callbackList;for(var u=r.hasForceUpdate,c=!0,s=r.first;null!==s&&0>=od(s.priorityLevel,l);){r.first=s.next,null===r.first&&(r.last=null);var d;s.isReplace?(a=td(s,o,a,i),c=!0):(d=td(s,o,a,i))&&(a=c?n({},a,d):n(a,d),c=!1),s.isForced&&(u=!0),null===s.callback||s.isTopLevelUnmount&&null!==s.next||((e=null!==e?e:[]).push(s.callback),t.effectTag|=fd),s=s.next}return r.callbackList=e,r.hasForceUpdate=u,null!==r.first||null!==e||u||(t.updateQueue=null),a},commitCallbacks:function(e,t,n){if(null!==(e=t.callbackList))for(t.callbackList=null,t=0;t<e.length;t++){var r=e[t];"function"!=typeof r&&w("191",r),r.call(n)}}},vd=[],wd=-1,xd={createCursor:function(e){return{current:e}},isEmpty:function(){return-1===wd},pop:function(e){0>wd||(e.current=vd[wd],vd[wd]=null,wd--)},push:function(e,t){vd[++wd]=e.current,e.current=t},reset:function(){for(;-1<wd;)vd[wd]=null,wd--}},yd=bb.isFiberMounted,zd=E.ClassComponent,Ad=E.HostRoot,Bd=xd.createCursor,Cd=xd.pop,Dd=xd.push,Ed=Bd(da),Fd=Bd(!1),Ld=da,R={getUnmaskedContext:function(e){return Nd(e)?Ld:Ed.current},cacheContext:Md,getMaskedContext:function(e,t){var n=e.type.contextTypes;if(!n)return da;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o,a={};for(o in n)a[o]=t[o];return r&&Md(e,t,a),a},hasContextChanged:function(){return Fd.current},isContextConsumer:function(e){return e.tag===zd&&null!=e.type.contextTypes},isContextProvider:Nd,popContextProvider:function(e){Nd(e)&&(Cd(Fd,e),Cd(Ed,e))},popTopLevelContextObject:function(e){Cd(Fd,e),Cd(Ed,e)},pushTopLevelContextObject:function(e,t,n){null!=Ed.cursor&&w("168"),Dd(Ed,t,e),Dd(Fd,n,e)},processChildContext:Od,pushContextProvider:function(e){if(!Nd(e))return!1;var t=e.stateNode;return t=t&&t.__reactInternalMemoizedMergedChildContext||da,Ld=Ed.current,Dd(Ed,t,e),Dd(Fd,Fd.current,e),!0},invalidateContextProvider:function(e,t){var n=e.stateNode;if(n||w("169"),t){var r=Od(e,Ld,!0);n.__reactInternalMemoizedMergedChildContext=r,Cd(Fd,e),Cd(Ed,e),Dd(Ed,r,e)}else Cd(Fd,e);Dd(Fd,t,e)},resetContext:function(){Ld=da,Ed.current=da,Fd.current=!1},findCurrentUnmaskedContext:function(e){for(yd(e)&&e.tag===zd?void 0:w("170");e.tag!==Ad;){if(Nd(e))return e.stateNode.__reactInternalMemoizedMergedChildContext;(e=e.return)||w("171")}return e.stateNode.context}},Pd={NoContext:0,AsyncUpdates:1},Qd=E.IndeterminateComponent,Rd=E.ClassComponent,Sd=E.HostRoot,Td=E.HostComponent,Ud=E.HostText,Vd=E.HostPortal,Wd=E.CoroutineComponent,Xd=E.YieldComponent,Yd=E.Fragment,Zd=Q.NoWork,$d=Pd.NoContext,ae=J.NoEffect,de={createWorkInProgress:function(e,t){var n=e.alternate;return null===n?(n=new be(e.tag,e.key,e.internalContextTag),n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.effectTag=ae,n.nextEffect=null,n.firstEffect=null,n.lastEffect=null),n.pendingWorkPriority=t,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n},createHostRootFiber:function(){return new be(Sd,null,$d)},createFiberFromElement:function(e,t,n){return t=ce(e.type,e.key,t,null),t.pendingProps=e.props,t.pendingWorkPriority=n,t},createFiberFromFragment:function(e,t,n){return t=new be(Yd,null,t),t.pendingProps=e,t.pendingWorkPriority=n,t},createFiberFromText:function(e,t,n){return t=new be(Ud,null,t),t.pendingProps=e,t.pendingWorkPriority=n,t},createFiberFromElementType:ce,createFiberFromHostInstanceForDeletion:function(){var e=new be(Td,null,$d);return e.type="DELETED",e},createFiberFromCoroutine:function(e,t,n){return t=new be(Wd,e.key,t),t.type=e.handler,t.pendingProps=e,t.pendingWorkPriority=n,t},createFiberFromYield:function(e,t){return new be(Xd,null,t)},createFiberFromPortal:function(e,t,n){return t=new be(Vd,e.key,t),t.pendingProps=e.children||[],t.pendingWorkPriority=n,t.stateNode={containerInfo:e.containerInfo,implementation:e.implementation},t},largerPriority:function(e,t){return e!==Zd&&(t===Zd||t>e)?e:t}},ee=de.createHostRootFiber,fe=E.IndeterminateComponent,ge=E.FunctionalComponent,he=E.ClassComponent,ie=E.HostComponent,je,ke;"function"==typeof Symbol&&Symbol.for?(je=Symbol.for("react.coroutine"),ke=Symbol.for("react.yield")):(je=60104,ke=60105);var le={createCoroutine:function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:je,key:null==r?null:""+r,children:e,handler:t,props:n}},createYield:function(e){return{$$typeof:ke,value:e}},isCoroutine:function(e){return"object"==typeof e&&null!==e&&e.$$typeof===je},isYield:function(e){return"object"==typeof e&&null!==e&&e.$$typeof===ke},REACT_YIELD_TYPE:ke,REACT_COROUTINE_TYPE:je},me="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.portal")||60106,ne={createPortal:function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:me,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}},isPortal:function(e){return"object"==typeof e&&null!==e&&e.$$typeof===me},REACT_PORTAL_TYPE:me},oe=le.REACT_COROUTINE_TYPE,pe=le.REACT_YIELD_TYPE,qe=ne.REACT_PORTAL_TYPE,re=de.createWorkInProgress,se=de.createFiberFromElement,te=de.createFiberFromFragment,ue=de.createFiberFromText,ve=de.createFiberFromCoroutine,we=de.createFiberFromYield,xe=de.createFiberFromPortal,ye=Array.isArray,ze=E.FunctionalComponent,Ae=E.ClassComponent,Be=E.HostText,Ce=E.HostPortal,De=E.CoroutineComponent,Ee=E.YieldComponent,Fe=E.Fragment,Ge=J.NoEffect,He=J.Placement,Ie=J.Deletion,Je="function"==typeof Symbol&&Symbol.iterator,Ke="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,Pe=Oe(!0,!0),Qe=Oe(!1,!0),Re=Oe(!1,!1),Se={reconcileChildFibers:Pe,reconcileChildFibersInPlace:Qe,mountChildFibersInPlace:Re,cloneChildFibers:function(e,t){if(null!==e&&t.child!==e.child&&w("153"),null!==t.child){e=t.child;var n=re(e,e.pendingWorkPriority);for(n.pendingProps=e.pendingProps,t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,n=n.sibling=re(e,e.pendingWorkPriority),n.pendingProps=e.pendingProps,n.return=t;n.sibling=null}}},Te=J.Update,Ue=Pd.AsyncUpdates,Ve=R.cacheContext,We=R.getMaskedContext,Xe=R.getUnmaskedContext,Ye=R.isContextConsumer,Ze=ud.addUpdate,$e=ud.addReplaceUpdate,af=ud.addForceUpdate,bf=ud.beginUpdateQueue,cf=R.hasContextChanged,df=bb.isMounted,ff=Se.mountChildFibersInPlace,gf=Se.reconcileChildFibers,hf=Se.reconcileChildFibersInPlace,jf=Se.cloneChildFibers,kf=ud.beginUpdateQueue,lf=R.getMaskedContext,mf=R.getUnmaskedContext,nf=R.hasContextChanged,of=R.pushContextProvider,pf=R.pushTopLevelContextObject,qf=R.invalidateContextProvider,rf=E.IndeterminateComponent,sf=E.FunctionalComponent,tf=E.ClassComponent,uf=E.HostRoot,wf=E.HostComponent,xf=E.HostText,yf=E.HostPortal,zf=E.CoroutineComponent,Af=E.CoroutineHandlerPhase,Bf=E.YieldComponent,Cf=E.Fragment,Df=Q.NoWork,Ef=Q.OffscreenPriority,Ff=J.PerformedWork,Gf=J.Placement,Hf=J.ContentReset,If=J.Err,Jf=J.Ref,Kf=Qa.ReactCurrentOwner,Mf=Se.reconcileChildFibers,Nf=R.popContextProvider,Of=R.popTopLevelContextObject,Pf=E.IndeterminateComponent,Qf=E.FunctionalComponent,Rf=E.ClassComponent,Sf=E.HostRoot,Tf=E.HostComponent,Uf=E.HostText,Vf=E.HostPortal,Wf=E.CoroutineComponent,Xf=E.CoroutineHandlerPhase,Yf=E.YieldComponent,Zf=E.Fragment,ag=J.Placement,bg=J.Ref,cg=J.Update,dg=Q.OffscreenPriority,fg=null,gg=null,ig={injectInternals:function(e){if("undefined"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)return!1;var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!t.supportsFiber)return!0;try{var n=t.inject(e);fg=hg(function(e){return t.onCommitFiberRoot(n,e)}),gg=hg(function(e){return t.onCommitFiberUnmount(n,e)})}catch(e){}return!0},onCommitRoot:function(e){"function"==typeof fg&&fg(e)},onCommitUnmount:function(e){"function"==typeof gg&&gg(e)}},jg=E.ClassComponent,kg=E.HostRoot,lg=E.HostComponent,mg=E.HostText,ng=E.HostPortal,og=E.CoroutineComponent,pg=ud.commitCallbacks,qg=ig.onCommitUnmount,rg=J.Placement,sg=J.Update,tg=J.Callback,ug=J.ContentReset,wg=xd.createCursor,xg=xd.pop,yg=xd.push,zg={},Bg=E.HostComponent,Cg=E.HostText,Dg=E.HostRoot,Eg=J.Deletion,Fg=J.Placement,Gg=de.createFiberFromHostInstanceForDeletion,Ig=R.popContextProvider,Jg=xd.reset,Kg=Qa.ReactCurrentOwner,Lg=de.createWorkInProgress,Mg=de.largerPriority,Ng=ig.onCommitRoot,T=Q.NoWork,Og=Q.SynchronousPriority,U=Q.TaskPriority,Pg=Q.HighPriority,Qg=Q.LowPriority,Rg=Q.OffscreenPriority,Sg=Pd.AsyncUpdates,Tg=J.PerformedWork,Ug=J.Placement,Vg=J.Update,Wg=J.PlacementAndUpdate,Xg=J.Deletion,Yg=J.ContentReset,Zg=J.Callback,$g=J.Err,ah=J.Ref,bh=E.HostRoot,ch=E.HostComponent,dh=E.HostPortal,eh=E.ClassComponent,fh=ud.getUpdatePriority,gh=R.resetContext;jh._injectFiber=function(e){ih=e};var kh=ud.addTopLevelUpdate,lh=R.findCurrentUnmaskedContext,mh=R.isContextProvider,nh=R.processChildContext,oh=E.HostComponent,ph=bb.findCurrentHostFiber,qh=bb.findCurrentHostFiberWithNoPortals;jh._injectFiber(function(e){var t=lh(e);return mh(e)?nh(e,t,!1):t});var rh=F.TEXT_NODE,uh=null,wh={getOffsets:function(e){var t=window.getSelection&&window.getSelection();if(!t||0===t.rangeCount)return null;var n=t.anchorNode,r=t.anchorOffset,o=t.focusNode,a=t.focusOffset,i=t.getRangeAt(0);try{i.startContainer.nodeType,i.endContainer.nodeType}catch(e){return null}t=t.anchorNode===t.focusNode&&t.anchorOffset===t.focusOffset?0:i.toString().length;var l=i.cloneRange();return l.selectNodeContents(e),l.setEnd(i.startContainer,i.startOffset),e=l.startContainer===l.endContainer&&l.startOffset===l.endOffset?0:l.toString().length,i=e+t,(t=document.createRange()).setStart(n,r),t.setEnd(o,a),n=t.collapsed,{start:n?i:e,end:n?e:i}},setOffsets:function(e,t){if(window.getSelection){var n=window.getSelection(),r=e[vh()].length,o=Math.min(t.start,r);if(t=void 0===t.end?o:Math.min(t.end,r),!n.extend&&o>t&&(r=t,t=o,o=r),r=th(e,o),e=th(e,t),r&&e){var a=document.createRange();a.setStart(r.node,r.offset),n.removeAllRanges(),o>t?(n.addRange(a),n.extend(e.node,e.offset)):(a.setEnd(e.node,e.offset),n.addRange(a))}}}},xh=F.ELEMENT_NODE,yh={hasSelectionCapabilities:function(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&"text"===e.type||"textarea"===t||"true"===e.contentEditable)},getSelectionInformation:function(){var e=ia();return{focusedElem:e,selectionRange:yh.hasSelectionCapabilities(e)?yh.getSelection(e):null}},restoreSelection:function(e){var t=ia(),n=e.focusedElem;if(e=e.selectionRange,t!==n&&fa(document.documentElement,n)){for(yh.hasSelectionCapabilities(n)&&yh.setSelection(n,e),t=[],e=n;e=e.parentNode;)e.nodeType===xh&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(ha(n),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}},getSelection:function(e){return("selectionStart"in e?{start:e.selectionStart,end:e.selectionEnd}:wh.getOffsets(e))||{start:0,end:0}},setSelection:function(e,t){var n=t.start,r=t.end;void 0===r&&(r=n),"selectionStart"in e?(e.selectionStart=n,e.selectionEnd=Math.min(r,e.value.length)):wh.setOffsets(e,t)}},zh=yh,Ah=F.ELEMENT_NODE;Dh._injectFiber=function(e){Bh=e},Dh._injectStack=function(e){Ch=e};var Eh=E.HostComponent,Hh={isAncestor:function(e,t){for(;t;){if(e===t||e===t.alternate)return!0;t=Fh(t)}return!1},getLowestCommonAncestor:Gh,getParentInstance:function(e){return Fh(e)},traverseTwoPhase:function(e,t,n){for(var r=[];e;)r.push(e),e=Fh(e);for(e=r.length;0<e--;)t(r[e],"captured",n);for(e=0;e<r.length;e++)t(r[e],"bubbled",n)},traverseEnterLeave:function(e,t,n,r,o){for(var a=e&&t?Gh(e,t):null,i=[];e&&e!==a;)i.push(e),e=Fh(e);for(e=[];t&&t!==a;)e.push(t),t=Fh(t);for(t=0;t<i.length;t++)n(i[t],"bubbled",r);for(t=e.length;0<t--;)n(e[t],"captured",o)}},Ih=Jb.getListener,Th={accumulateTwoPhaseDispatches:function(e){Db(e,Kh)},accumulateTwoPhaseDispatchesSkipTarget:function(e){Db(e,Qh)},accumulateDirectDispatches:function(e){Db(e,Sh)},accumulateEnterLeaveDispatches:function(e,t,n,r){Hh.traverseEnterLeave(n,r,Rh,e,t)}},X={_root:null,_startText:null,_fallbackText:null},Uh={initialize:function(e){return X._root=e,X._startText=Uh.getText(),!0},reset:function(){X._root=null,X._startText=null,X._fallbackText=null},getData:function(){if(X._fallbackText)return X._fallbackText;var e,t,n=X._startText,r=n.length,o=Uh.getText(),a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return X._fallbackText=o.slice(e,1<t?1-t:void 0),X._fallbackText},getText:function(){return"value"in X._root?X._root.value:X._root[vh()]}},Vh=Uh,Wh="dispatchConfig _targetInst nativeEvent isDefaultPrevented isPropagationStopped _dispatchListeners _dispatchInstances".split(" "),Xh={type:null,target:null,currentTarget:ca.thatReturnsNull,eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null};n(Y.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=ca.thatReturnsTrue)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=ca.thatReturnsTrue)},persist:function(){this.isPersistent=ca.thatReturnsTrue},isPersistent:ca.thatReturnsFalse,destructor:function(){var e,t=this.constructor.Interface;for(e in t)this[e]=null;for(t=0;t<Wh.length;t++)this[Wh[t]]=null}}),Y.Interface=Xh,Y.augmentClass=function(e,t){function r(){}r.prototype=this.prototype;var o=new r;n(o,e.prototype),e.prototype=o,e.prototype.constructor=e,e.Interface=n({},this.Interface,t),e.augmentClass=this.augmentClass,Yh(e)},Yh(Y),Y.augmentClass(ai,{data:null}),Y.augmentClass(bi,{data:null});var ci=[9,13,27,32],di=l.canUseDOM&&"CompositionEvent"in window,ei=null;l.canUseDOM&&"documentMode"in document&&(ei=document.documentMode);var fi;if(fi=l.canUseDOM&&"TextEvent"in window&&!ei){var gi=window.opera;fi=!("object"==typeof gi&&"function"==typeof gi.version&&12>=parseInt(gi.version(),10))}var hi=fi,ii=l.canUseDOM&&(!di||ei&&8<ei&&11>=ei),ji=String.fromCharCode(32),ki={beforeInput:{phasedRegistrationNames:{bubbled:"onBeforeInput",captured:"onBeforeInputCapture"},dependencies:["topCompositionEnd","topKeyPress","topTextInput","topPaste"]},compositionEnd:{phasedRegistrationNames:{bubbled:"onCompositionEnd",captured:"onCompositionEndCapture"},dependencies:"topBlur topCompositionEnd topKeyDown topKeyPress topKeyUp topMouseDown".split(" ")},compositionStart:{phasedRegistrationNames:{bubbled:"onCompositionStart",captured:"onCompositionStartCapture"},dependencies:"topBlur topCompositionStart topKeyDown topKeyPress topKeyUp topMouseDown".split(" ")},compositionUpdate:{phasedRegistrationNames:{bubbled:"onCompositionUpdate",captured:"onCompositionUpdateCapture"},dependencies:"topBlur topCompositionUpdate topKeyDown topKeyPress topKeyUp topMouseDown".split(" ")}},li=!1,oi=!1,ri={eventTypes:ki,extractEvents:function(e,t,n,r){var o;if(di)e:{switch(e){case"topCompositionStart":var a=ki.compositionStart;break e;case"topCompositionEnd":a=ki.compositionEnd;break e;case"topCompositionUpdate":a=ki.compositionUpdate;break e}a=void 0}else oi?mi(e,n)&&(a=ki.compositionEnd):"topKeyDown"===e&&229===n.keyCode&&(a=ki.compositionStart);return a?(ii&&(oi||a!==ki.compositionStart?a===ki.compositionEnd&&oi&&(o=Vh.getData()):oi=Vh.initialize(r)),a=ai.getPooled(a,t,n,r),o?a.data=o:null!==(o=ni(n))&&(a.data=o),Th.accumulateTwoPhaseDispatches(a),o=a):o=null,(e=hi?pi(e,n):qi(e,n))?(t=bi.getPooled(ki.beforeInput,t,n,r),t.data=e,Th.accumulateTwoPhaseDispatches(t)):t=null,[o,t]}},si={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0},ui={change:{phasedRegistrationNames:{bubbled:"onChange",captured:"onChangeCapture"},dependencies:"topBlur topChange topClick topFocus topInput topKeyDown topKeyUp topSelectionChange".split(" ")}},wi=null,xi=null,Bi=!1;l.canUseDOM&&(Bi=Lb("input")&&(!document.documentMode||9<document.documentMode));var Ii={eventTypes:ui,_isInputEventSupported:Bi,extractEvents:function(e,t,n,r){var o=t?G.getNodeFromInstance(t):window,a=o.nodeName&&o.nodeName.toLowerCase();if("select"===a||"input"===a&&"file"===o.type)var i=Ai;else if(ti(o))if(Bi)i=Hi;else{i=Fi;var l=Ei}else!(a=o.nodeName)||"input"!==a.toLowerCase()||"checkbox"!==o.type&&"radio"!==o.type||(i=Gi);if(i&&(i=i(e,t)))return vi(i,n,r);l&&l(e,o,t),"topBlur"===e&&null!=t&&(e=t._wrapperState||o._wrapperState)&&e.controlled&&"number"===o.type&&(e=""+o.value,o.getAttribute("value")!==e&&o.setAttribute("value",e))}};Y.augmentClass(Ji,{view:function(e){return e.view?e.view:(e=ub(e)).window===e?e:(e=e.ownerDocument)?e.defaultView||e.parentWindow:window},detail:function(e){return e.detail||0}});var Ki={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};Ji.augmentClass(Ni,{screenX:null,screenY:null,clientX:null,clientY:null,pageX:null,pageY:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,getModifierState:Mi,button:null,buttons:null,relatedTarget:function(e){return e.relatedTarget||(e.fromElement===e.srcElement?e.toElement:e.fromElement)}});var Oi={mouseEnter:{registrationName:"onMouseEnter",dependencies:["topMouseOut","topMouseOver"]},mouseLeave:{registrationName:"onMouseLeave",dependencies:["topMouseOut","topMouseOver"]}},Pi={eventTypes:Oi,extractEvents:function(e,t,n,r){if("topMouseOver"===e&&(n.relatedTarget||n.fromElement)||"topMouseOut"!==e&&"topMouseOver"!==e)return null;var o=r.window===r?r:(o=r.ownerDocument)?o.defaultView||o.parentWindow:window;if("topMouseOut"===e?(e=t,t=(t=n.relatedTarget||n.toElement)?G.getClosestInstanceFromNode(t):null):e=null,e===t)return null;var a=null==e?o:G.getNodeFromInstance(e);o=null==t?o:G.getNodeFromInstance(t);var i=Ni.getPooled(Oi.mouseLeave,e,n,r);return i.type="mouseleave",i.target=a,i.relatedTarget=o,n=Ni.getPooled(Oi.mouseEnter,t,n,r),n.type="mouseenter",n.target=o,n.relatedTarget=a,Th.accumulateEnterLeaveDispatches(i,n,e,t),[i,n]}},Qi=F.DOCUMENT_NODE,Ri=l.canUseDOM&&"documentMode"in document&&11>=document.documentMode,Si={select:{phasedRegistrationNames:{bubbled:"onSelect",captured:"onSelectCapture"},dependencies:"topBlur topContextMenu topFocus topKeyDown topKeyUp topMouseDown topMouseUp topSelectionChange".split(" ")}},Ti=null,Zi=null,$i=null,aj=!1,bj=M.isListeningToAllDependencies,dj={eventTypes:Si,extractEvents:function(e,t,n,r){var o=r.window===r?r.document:r.nodeType===Qi?r:r.ownerDocument;if(!o||!bj("onSelect",o))return null;switch(o=t?G.getNodeFromInstance(t):window,e){case"topFocus":(ti(o)||"true"===o.contentEditable)&&(Ti=o,Zi=t,$i=null);break;case"topBlur":$i=Zi=Ti=null;break;case"topMouseDown":aj=!0;break;case"topContextMenu":case"topMouseUp":return aj=!1,cj(n,r);case"topSelectionChange":if(Ri)break;case"topKeyDown":case"topKeyUp":return cj(n,r)}return null}};Y.augmentClass(ej,{animationName:null,elapsedTime:null,pseudoElement:null}),Y.augmentClass(fj,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ji.augmentClass(gj,{relatedTarget:null});var ij={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},jj={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"};Ji.augmentClass(kj,{key:function(e){if(e.key){var t=ij[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=hj(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?jj[e.keyCode]||"Unidentified":""},location:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,repeat:null,locale:null,getModifierState:Mi,charCode:function(e){return"keypress"===e.type?hj(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?hj(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Ni.augmentClass(lj,{dataTransfer:null}),Ji.augmentClass(mj,{touches:null,targetTouches:null,changedTouches:null,altKey:null,metaKey:null,ctrlKey:null,shiftKey:null,getModifierState:Mi}),Y.augmentClass(nj,{propertyName:null,elapsedTime:null,pseudoElement:null}),Ni.augmentClass(oj,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:null,deltaMode:null});var pj={},qj={};"abort animationEnd animationIteration animationStart blur cancel canPlay canPlayThrough click close contextMenu copy cut doubleClick drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error focus input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing progress rateChange reset scroll seeked seeking stalled submit suspend timeUpdate toggle touchCancel touchEnd touchMove touchStart transitionEnd volumeChange waiting wheel".split(" ").forEach(function(e){var t=e[0].toUpperCase()+e.slice(1),n="on"+t;n={phasedRegistrationNames:{bubbled:n,captured:n+"Capture"},dependencies:[t="top"+t]},pj[e]=n,qj[t]=n});var rj={eventTypes:pj,extractEvents:function(e,t,n,r){var o=qj[e];if(!o)return null;switch(e){case"topAbort":case"topCancel":case"topCanPlay":case"topCanPlayThrough":case"topClose":case"topDurationChange":case"topEmptied":case"topEncrypted":case"topEnded":case"topError":case"topInput":case"topInvalid":case"topLoad":case"topLoadedData":case"topLoadedMetadata":case"topLoadStart":case"topPause":case"topPlay":case"topPlaying":case"topProgress":case"topRateChange":case"topReset":case"topSeeked":case"topSeeking":case"topStalled":case"topSubmit":case"topSuspend":case"topTimeUpdate":case"topToggle":case"topVolumeChange":case"topWaiting":var a=Y;break;case"topKeyPress":if(0===hj(n))return null;case"topKeyDown":case"topKeyUp":a=kj;break;case"topBlur":case"topFocus":a=gj;break;case"topClick":if(2===n.button)return null;case"topDoubleClick":case"topMouseDown":case"topMouseMove":case"topMouseUp":case"topMouseOut":case"topMouseOver":case"topContextMenu":a=Ni;break;case"topDrag":case"topDragEnd":case"topDragEnter":case"topDragExit":case"topDragLeave":case"topDragOver":case"topDragStart":case"topDrop":a=lj;break;case"topTouchCancel":case"topTouchEnd":case"topTouchMove":case"topTouchStart":a=mj;break;case"topAnimationEnd":case"topAnimationIteration":case"topAnimationStart":a=ej;break;case"topTransitionEnd":a=nj;break;case"topScroll":a=Ji;break;case"topWheel":a=oj;break;case"topCopy":case"topCut":case"topPaste":a=fj}return a||w("86",e),e=a.getPooled(o,t,n,r),Th.accumulateTwoPhaseDispatches(e),e}};L.setHandleTopLevel(M.handleTopLevel),Jb.injection.injectEventPluginOrder("ResponderEventPlugin SimpleEventPlugin TapEventPlugin EnterLeaveEventPlugin ChangeEventPlugin SelectEventPlugin BeforeInputEventPlugin".split(" ")),ib.injection.injectComponentTree(G),Jb.injection.injectEventPluginsByName({SimpleEventPlugin:rj,EnterLeaveEventPlugin:Pi,ChangeEventPlugin:Ii,SelectEventPlugin:dj,BeforeInputEventPlugin:ri});var sj=A.injection.MUST_USE_PROPERTY,Z=A.injection.HAS_BOOLEAN_VALUE,tj=A.injection.HAS_NUMERIC_VALUE,uj=A.injection.HAS_POSITIVE_NUMERIC_VALUE,vj=A.injection.HAS_STRING_BOOLEAN_VALUE,wj={Properties:{allowFullScreen:Z,allowTransparency:vj,async:Z,autoPlay:Z,capture:Z,checked:sj|Z,cols:uj,contentEditable:vj,controls:Z,default:Z,defer:Z,disabled:Z,download:A.injection.HAS_OVERLOADED_BOOLEAN_VALUE,draggable:vj,formNoValidate:Z,hidden:Z,loop:Z,multiple:sj|Z,muted:sj|Z,noValidate:Z,open:Z,playsInline:Z,readOnly:Z,required:Z,reversed:Z,rows:uj,rowSpan:tj,scoped:Z,seamless:Z,selected:sj|Z,size:uj,start:tj,span:uj,spellCheck:vj,style:0,itemScope:Z,acceptCharset:0,className:0,htmlFor:0,httpEquiv:0,value:vj},DOMAttributeNames:{acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv"},DOMMutationMethods:{value:function(e,t){if(null==t)return e.removeAttribute("value");"number"!==e.type||!1===e.hasAttribute("value")?e.setAttribute("value",""+t):e.validity&&!e.validity.badInput&&e.ownerDocument.activeElement!==e&&e.setAttribute("value",""+t)}}},xj=A.injection.HAS_STRING_BOOLEAN_VALUE,yj={xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace"},zj={Properties:{autoReverse:xj,externalResourcesRequired:xj,preserveAlpha:xj},DOMAttributeNames:{autoReverse:"autoReverse",externalResourcesRequired:"externalResourcesRequired",preserveAlpha:"preserveAlpha"},DOMAttributeNamespaces:{xlinkActuate:yj.xlink,xlinkArcrole:yj.xlink,xlinkHref:yj.xlink,xlinkRole:yj.xlink,xlinkShow:yj.xlink,xlinkTitle:yj.xlink,xlinkType:yj.xlink,xmlBase:yj.xml,xmlLang:yj.xml,xmlSpace:yj.xml}},Aj=/[\-\:]([a-z])/g;"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode x-height xlink:actuate xlink:arcrole xlink:href xlink:role xlink:show xlink:title xlink:type xml:base xmlns:xlink xml:lang xml:space".split(" ").forEach(function(e){var t=e.replace(Aj,Bj);zj.Properties[t]=0,zj.DOMAttributeNames[t]=e}),A.injection.injectDOMPropertyConfig(wj),A.injection.injectDOMPropertyConfig(zj);var Cj=ig.injectInternals,Dj=F.ELEMENT_NODE,Ej=F.TEXT_NODE,Fj=F.COMMENT_NODE,Gj=F.DOCUMENT_NODE,Hj=F.DOCUMENT_FRAGMENT_NODE,Ij=A.ROOT_ATTRIBUTE_NAME,Jj=ka.getChildNamespace,Kj=N.createElement,Lj=N.createTextNode,Mj=N.setInitialProperties,Nj=N.diffProperties,Oj=N.updateProperties,Pj=N.diffHydratedProperties,Qj=N.diffHydratedText,Rj=N.warnForDeletedHydratableElement,Sj=N.warnForDeletedHydratableText,Tj=N.warnForInsertedHydratedElement,Uj=N.warnForInsertedHydratedText,Vj=G.precacheFiberNode,Wj=G.updateFiberProps;nb.injection.injectFiberControlledHostComponent(N),Dh._injectFiber(function(e){return Xj.findHostInstance(e)});var Yj=null,Zj=null,Xj=function(e){var t=e.getPublicInstance,n=(e=hh(e)).scheduleUpdate,r=e.getPriorityContext;return{createContainer:function(e){var t=ee();return e={current:t,containerInfo:e,isScheduled:!1,nextScheduledRoot:null,context:null,pendingContext:null},t.stateNode=e},updateContainer:function(e,t,o,a){var i=t.current;o=jh(o),null===t.context?t.context=o:t.pendingContext=o,t=a,a=r(i,ed.enableAsyncSubtreeAPI&&null!=e&&null!=e.type&&null!=e.type.prototype&&!0===e.type.prototype.unstable_isAsyncReactComponent),kh(i,e={element:e},void 0===t?null:t,a),n(i,a)},batchedUpdates:e.batchedUpdates,unbatchedUpdates:e.unbatchedUpdates,deferredUpdates:e.deferredUpdates,flushSync:e.flushSync,getPublicRootInstance:function(e){if(!(e=e.current).child)return null;switch(e.child.tag){case oh:return t(e.child.stateNode);default:return e.child.stateNode}},findHostInstance:function(e){return null===(e=ph(e))?null:e.stateNode},findHostInstanceWithNoPortals:function(e){return null===(e=qh(e))?null:e.stateNode}}}({getRootHostContext:function(e){if(e.nodeType===Gj)e=(e=e.documentElement)?e.namespaceURI:Jj(null,"");else{var t=e.nodeType===Fj?e.parentNode:e;e=t.namespaceURI||null,t=t.tagName,e=Jj(e,t)}return e},getChildHostContext:function(e,t){return Jj(e,t)},getPublicInstance:function(e){return e},prepareForCommit:function(){Yj=M.isEnabled(),Zj=zh.getSelectionInformation(),M.setEnabled(!1)},resetAfterCommit:function(){zh.restoreSelection(Zj),Zj=null,M.setEnabled(Yj),Yj=null},createInstance:function(e,t,n,r,o){return e=Kj(e,t,n,r),Vj(o,e),Wj(e,t),e},appendInitialChild:function(e,t){e.appendChild(t)},finalizeInitialChildren:function(e,t,n,r){Mj(e,t,n,r);e:{switch(t){case"button":case"input":case"select":case"textarea":e=!!n.autoFocus;break e}e=!1}return e},prepareUpdate:function(e,t,n,r,o){return Nj(e,t,n,r,o)},commitMount:function(e){e.focus()},commitUpdate:function(e,t,n,r,o){Wj(e,o),Oj(e,t,n,r,o)},shouldSetTextContent:function(e,t){return"textarea"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&"string"==typeof t.dangerouslySetInnerHTML.__html},resetTextContent:function(e){e.textContent=""},shouldDeprioritizeSubtree:function(e,t){return!!t.hidden},createTextInstance:function(e,t,n,r){return e=Lj(e,t),Vj(r,e),e},commitTextUpdate:function(e,t,n){e.nodeValue=n},appendChild:function(e,t){e.appendChild(t)},appendChildToContainer:function(e,t){e.nodeType===Fj?e.parentNode.insertBefore(t,e):e.appendChild(t)},insertBefore:function(e,t,n){e.insertBefore(t,n)},insertInContainerBefore:function(e,t,n){e.nodeType===Fj?e.parentNode.insertBefore(t,n):e.insertBefore(t,n)},removeChild:function(e,t){e.removeChild(t)},removeChildFromContainer:function(e,t){e.nodeType===Fj?e.parentNode.removeChild(t):e.removeChild(t)},canHydrateInstance:function(e,t){return e.nodeType===Dj&&t===e.nodeName.toLowerCase()},canHydrateTextInstance:function(e,t){return""!==t&&e.nodeType===Ej},getNextHydratableSibling:function(e){for(e=e.nextSibling;e&&e.nodeType!==Dj&&e.nodeType!==Ej;)e=e.nextSibling;return e},getFirstHydratableChild:function(e){for(e=e.firstChild;e&&e.nodeType!==Dj&&e.nodeType!==Ej;)e=e.nextSibling;return e},hydrateInstance:function(e,t,n,r,o,a){return Vj(a,e),Wj(e,n),Pj(e,t,n,o,r)},hydrateTextInstance:function(e,t,n){return Vj(n,e),Qj(e,t)},didNotHydrateInstance:function(e,t){1===t.nodeType?Rj(e,t):Sj(e,t)},didNotFindHydratableInstance:function(e,t,n){Tj(e,t,n)},didNotFindHydratableTextInstance:function(e,t){Uj(e,t)},scheduleDeferredCallback:dd.rIC,useSyncScheduling:!0});sb.injection.injectFiberBatchedUpdates(Xj.batchedUpdates);var ek={createPortal:dk,hydrate:function(e,t,n){return ck(null,e,t,!0,n)},render:function(e,t,n){return ck(null,e,t,!1,n)},unstable_renderSubtreeIntoContainer:function(e,t,n,r){return null!=e&&Pa.has(e)||w("38"),ck(e,t,n,!1,r)},unmountComponentAtNode:function(e){return ak(e)||w("40"),!!e._reactRootContainer&&(Xj.unbatchedUpdates(function(){ck(null,null,e,!1,function(){e._reactRootContainer=null})}),!0)},findDOMNode:Dh,unstable_createPortal:dk,unstable_batchedUpdates:sb.batchedUpdates,unstable_deferredUpdates:Xj.deferredUpdates,flushSync:Xj.flushSync,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:{EventPluginHub:Jb,EventPluginRegistry:sa,EventPropagators:Th,ReactControlledComponent:nb,ReactDOMComponentTree:G,ReactDOMEventListener:L}};Cj({findFiberByHostInstance:G.getClosestInstanceFromNode,findHostInstanceByFiber:Xj.findHostInstance,bundleType:0,version:"16.0.0",rendererPackageName:"react-dom"}),module.exports=ek;

},{"fbjs/lib/EventListener":6,"fbjs/lib/ExecutionEnvironment":7,"fbjs/lib/containsNode":8,"fbjs/lib/emptyFunction":9,"fbjs/lib/emptyObject":10,"fbjs/lib/focusNode":11,"fbjs/lib/getActiveElement":12,"fbjs/lib/invariant":13,"fbjs/lib/shallowEqual":16,"object-assign":35,"react":"react"}],39:[function(require,module,exports){
"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _classCallCheck(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(e,r){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!r||"object"!=typeof r&&"function"!=typeof r?e:r}function _inherits(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function, not "+typeof r);e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(e,r):e.__proto__=r)}function warnAboutReceivingStore(){didWarnAboutReceivingStore||(didWarnAboutReceivingStore=!0,(0,_warning2.default)("<Provider> does not support changing `store` on the fly. It is most likely that you see this error because you updated to Redux 2.x and React Redux 2.x which no longer hot reload reducers automatically. See https://github.com/reactjs/react-redux/releases/tag/v2.0.0 for the migration instructions."))}function createProvider(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"store",t=arguments[1]||r+"Subscription",o=function(e){function o(t,n){_classCallCheck(this,o);var i=_possibleConstructorReturn(this,e.call(this,t,n));return i[r]=t.store,i}return _inherits(o,e),o.prototype.getChildContext=function(){var e;return e={},e[r]=this[r],e[t]=null,e},o.prototype.render=function(){return _react.Children.only(this.props.children)},o}(_react.Component);return o.propTypes={store:_PropTypes.storeShape.isRequired,children:_propTypes2.default.element.isRequired},o.childContextTypes=(e={},e[r]=_PropTypes.storeShape.isRequired,e[t]=_PropTypes.subscriptionShape,e),o}exports.__esModule=!0,exports.createProvider=createProvider;var _react=require("react"),_propTypes=require("prop-types"),_propTypes2=_interopRequireDefault(_propTypes),_PropTypes=require("../utils/PropTypes"),_warning=require("../utils/warning"),_warning2=_interopRequireDefault(_warning),didWarnAboutReceivingStore=!1;exports.default=createProvider();

},{"../utils/PropTypes":48,"../utils/warning":52,"prop-types":"prop-types","react":"react"}],40:[function(require,module,exports){
"use strict";function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}function _objectWithoutProperties(t,e){var o={};for(var n in t)e.indexOf(n)>=0||Object.prototype.hasOwnProperty.call(t,n)&&(o[n]=t[n]);return o}function noop(){}function makeSelectorStateful(t,e){var o={run:function(n){try{var r=t(e.getState(),n);(r!==o.props||o.error)&&(o.shouldComponentUpdate=!0,o.props=r,o.error=null)}catch(t){o.shouldComponentUpdate=!0,o.error=t}}};return o}function connectAdvanced(t){var e,o,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.getDisplayName,i=void 0===r?function(t){return"ConnectAdvanced("+t+")"}:r,s=n.methodName,p=void 0===s?"connectAdvanced":s,a=n.renderCountProp,u=void 0===a?void 0:a,c=n.shouldHandleStateChanges,d=void 0===c||c,h=n.storeKey,l=void 0===h?"store":h,f=n.withRef,y=void 0!==f&&f,b=_objectWithoutProperties(n,["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef"]),m=l+"Subscription",_=hotReloadingVersion++,v=(e={},e[l]=_PropTypes.storeShape,e[m]=_PropTypes.subscriptionShape,e),S=(o={},o[m]=_PropTypes.subscriptionShape,o);return function(e){(0,_invariant2.default)("function"==typeof e,"You must pass a component to the function returned by connect. Instead received "+JSON.stringify(e));var o=e.displayName||e.name||"Component",n=i(o),r=_extends({},b,{getDisplayName:i,methodName:p,renderCountProp:u,shouldHandleStateChanges:d,storeKey:l,withRef:y,displayName:n,wrappedComponentName:o,WrappedComponent:e}),s=function(o){function i(t,e){_classCallCheck(this,i);var r=_possibleConstructorReturn(this,o.call(this,t,e));return r.version=_,r.state={},r.renderCount=0,r.store=t[l]||e[l],r.propsMode=Boolean(t[l]),r.setWrappedInstance=r.setWrappedInstance.bind(r),(0,_invariant2.default)(r.store,'Could not find "'+l+'" in either the context or props of "'+n+'". Either wrap the root component in a <Provider>, or explicitly pass "'+l+'" as a prop to "'+n+'".'),r.initSelector(),r.initSubscription(),r}return _inherits(i,o),i.prototype.getChildContext=function(){var t,e=this.propsMode?null:this.subscription;return t={},t[m]=e||this.context[m],t},i.prototype.componentDidMount=function(){d&&(this.subscription.trySubscribe(),this.selector.run(this.props),this.selector.shouldComponentUpdate&&this.forceUpdate())},i.prototype.componentWillReceiveProps=function(t){this.selector.run(t)},i.prototype.shouldComponentUpdate=function(){return this.selector.shouldComponentUpdate},i.prototype.componentWillUnmount=function(){this.subscription&&this.subscription.tryUnsubscribe(),this.subscription=null,this.notifyNestedSubs=noop,this.store=null,this.selector.run=noop,this.selector.shouldComponentUpdate=!1},i.prototype.getWrappedInstance=function(){return(0,_invariant2.default)(y,"To access the wrapped instance, you need to specify { withRef: true } in the options argument of the "+p+"() call."),this.wrappedInstance},i.prototype.setWrappedInstance=function(t){this.wrappedInstance=t},i.prototype.initSelector=function(){var e=t(this.store.dispatch,r);this.selector=makeSelectorStateful(e,this.store),this.selector.run(this.props)},i.prototype.initSubscription=function(){if(d){var t=(this.propsMode?this.props:this.context)[m];this.subscription=new _Subscription2.default(this.store,t,this.onStateChange.bind(this)),this.notifyNestedSubs=this.subscription.notifyNestedSubs.bind(this.subscription)}},i.prototype.onStateChange=function(){this.selector.run(this.props),this.selector.shouldComponentUpdate?(this.componentDidUpdate=this.notifyNestedSubsOnComponentDidUpdate,this.setState(dummyState)):this.notifyNestedSubs()},i.prototype.notifyNestedSubsOnComponentDidUpdate=function(){this.componentDidUpdate=void 0,this.notifyNestedSubs()},i.prototype.isSubscribed=function(){return Boolean(this.subscription)&&this.subscription.isSubscribed()},i.prototype.addExtraProps=function(t){if(!(y||u||this.propsMode&&this.subscription))return t;var e=_extends({},t);return y&&(e.ref=this.setWrappedInstance),u&&(e[u]=this.renderCount++),this.propsMode&&this.subscription&&(e[m]=this.subscription),e},i.prototype.render=function(){var t=this.selector;if(t.shouldComponentUpdate=!1,t.error)throw t.error;return(0,_react.createElement)(e,this.addExtraProps(t.props))},i}(_react.Component);return s.WrappedComponent=e,s.displayName=n,s.childContextTypes=S,s.contextTypes=v,s.propTypes=v,(0,_hoistNonReactStatics2.default)(s,e)}}exports.__esModule=!0;var _extends=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var o=arguments[e];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(t[n]=o[n])}return t};exports.default=connectAdvanced;var _hoistNonReactStatics=require("hoist-non-react-statics"),_hoistNonReactStatics2=_interopRequireDefault(_hoistNonReactStatics),_invariant=require("invariant"),_invariant2=_interopRequireDefault(_invariant),_react=require("react"),_Subscription=require("../utils/Subscription"),_Subscription2=_interopRequireDefault(_Subscription),_PropTypes=require("../utils/PropTypes"),hotReloadingVersion=0,dummyState={};

},{"../utils/PropTypes":48,"../utils/Subscription":49,"hoist-non-react-statics":17,"invariant":18,"react":"react"}],41:[function(require,module,exports){
"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _objectWithoutProperties(e,t){var r={};for(var o in e)t.indexOf(o)>=0||Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=e[o]);return r}function match(e,t,r){for(var o=t.length-1;o>=0;o--){var a=t[o](e);if(a)return a}return function(t,o){throw new Error("Invalid value of type "+typeof e+" for "+r+" argument when connecting component "+o.wrappedComponentName+".")}}function strictEqual(e,t){return e===t}function createConnect(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.connectHOC,r=void 0===t?_connectAdvanced2.default:t,o=e.mapStateToPropsFactories,a=void 0===o?_mapStateToProps2.default:o,n=e.mapDispatchToPropsFactories,p=void 0===n?_mapDispatchToProps2.default:n,s=e.mergePropsFactories,u=void 0===s?_mergeProps2.default:s,c=e.selectorFactory,l=void 0===c?_selectorFactory2.default:c;return function(e,t,o){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=n.pure,c=void 0===s||s,i=n.areStatesEqual,d=void 0===i?strictEqual:i,_=n.areOwnPropsEqual,P=void 0===_?_shallowEqual2.default:_,f=n.areStatePropsEqual,q=void 0===f?_shallowEqual2.default:f,m=n.areMergedPropsEqual,h=void 0===m?_shallowEqual2.default:m,v=_objectWithoutProperties(n,["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"]),E=match(e,a,"mapStateToProps"),g=match(t,p,"mapDispatchToProps"),w=match(o,u,"mergeProps");return r(l,_extends({methodName:"connect",getDisplayName:function(e){return"Connect("+e+")"},shouldHandleStateChanges:Boolean(e),initMapStateToProps:E,initMapDispatchToProps:g,initMergeProps:w,pure:c,areStatesEqual:d,areOwnPropsEqual:P,areStatePropsEqual:q,areMergedPropsEqual:h},v))}}exports.__esModule=!0;var _extends=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e};exports.createConnect=createConnect;var _connectAdvanced=require("../components/connectAdvanced"),_connectAdvanced2=_interopRequireDefault(_connectAdvanced),_shallowEqual=require("../utils/shallowEqual"),_shallowEqual2=_interopRequireDefault(_shallowEqual),_mapDispatchToProps=require("./mapDispatchToProps"),_mapDispatchToProps2=_interopRequireDefault(_mapDispatchToProps),_mapStateToProps=require("./mapStateToProps"),_mapStateToProps2=_interopRequireDefault(_mapStateToProps),_mergeProps=require("./mergeProps"),_mergeProps2=_interopRequireDefault(_mergeProps),_selectorFactory=require("./selectorFactory"),_selectorFactory2=_interopRequireDefault(_selectorFactory);exports.default=createConnect();

},{"../components/connectAdvanced":40,"../utils/shallowEqual":50,"./mapDispatchToProps":42,"./mapStateToProps":43,"./mergeProps":44,"./selectorFactory":45}],42:[function(require,module,exports){
"use strict";function whenMapDispatchToPropsIsFunction(p){return"function"==typeof p?(0,_wrapMapToProps.wrapMapToPropsFunc)(p,"mapDispatchToProps"):void 0}function whenMapDispatchToPropsIsMissing(p){return p?void 0:(0,_wrapMapToProps.wrapMapToPropsConstant)(function(p){return{dispatch:p}})}function whenMapDispatchToPropsIsObject(p){return p&&"object"==typeof p?(0,_wrapMapToProps.wrapMapToPropsConstant)(function(o){return(0,_redux.bindActionCreators)(p,o)}):void 0}exports.__esModule=!0,exports.whenMapDispatchToPropsIsFunction=whenMapDispatchToPropsIsFunction,exports.whenMapDispatchToPropsIsMissing=whenMapDispatchToPropsIsMissing,exports.whenMapDispatchToPropsIsObject=whenMapDispatchToPropsIsObject;var _redux=require("redux"),_wrapMapToProps=require("./wrapMapToProps");exports.default=[whenMapDispatchToPropsIsFunction,whenMapDispatchToPropsIsMissing,whenMapDispatchToPropsIsObject];

},{"./wrapMapToProps":47,"redux":"redux"}],43:[function(require,module,exports){
"use strict";function whenMapStateToPropsIsFunction(o){return"function"==typeof o?(0,_wrapMapToProps.wrapMapToPropsFunc)(o,"mapStateToProps"):void 0}function whenMapStateToPropsIsMissing(o){return o?void 0:(0,_wrapMapToProps.wrapMapToPropsConstant)(function(){return{}})}exports.__esModule=!0,exports.whenMapStateToPropsIsFunction=whenMapStateToPropsIsFunction,exports.whenMapStateToPropsIsMissing=whenMapStateToPropsIsMissing;var _wrapMapToProps=require("./wrapMapToProps");exports.default=[whenMapStateToPropsIsFunction,whenMapStateToPropsIsMissing];

},{"./wrapMapToProps":47}],44:[function(require,module,exports){
"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function defaultMergeProps(e,r,t){return _extends({},t,e,r)}function wrapMergePropsFunc(e){return function(r,t){t.displayName;var n=t.pure,o=t.areMergedPropsEqual,u=!1,s=void 0;return function(r,t,i){var p=e(r,t,i);return u?n&&o(p,s)||(s=p):(u=!0,s=p),s}}}function whenMergePropsIsFunction(e){return"function"==typeof e?wrapMergePropsFunc(e):void 0}function whenMergePropsIsOmitted(e){return e?void 0:function(){return defaultMergeProps}}exports.__esModule=!0;var _extends=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e};exports.defaultMergeProps=defaultMergeProps,exports.wrapMergePropsFunc=wrapMergePropsFunc,exports.whenMergePropsIsFunction=whenMergePropsIsFunction,exports.whenMergePropsIsOmitted=whenMergePropsIsOmitted;var _verifyPlainObject=require("../utils/verifyPlainObject"),_verifyPlainObject2=_interopRequireDefault(_verifyPlainObject);exports.default=[whenMergePropsIsFunction,whenMergePropsIsOmitted];

},{"../utils/verifyPlainObject":51}],45:[function(require,module,exports){
"use strict";function _interopRequireDefault(r){return r&&r.__esModule?r:{default:r}}function _objectWithoutProperties(r,e){var t={};for(var o in r)e.indexOf(o)>=0||Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o]);return t}function impureFinalPropsSelectorFactory(r,e,t,o){return function(n,i){return t(r(n,i),e(o,i),i)}}function pureFinalPropsSelectorFactory(r,e,t,o,n){function i(n,i){return F=n,S=i,d=r(F,S),v=e(o,S),y=t(d,v,S),P=!0,y}function u(){return d=r(F,S),e.dependsOnOwnProps&&(v=e(o,S)),y=t(d,v,S)}function p(){return r.dependsOnOwnProps&&(d=r(F,S)),e.dependsOnOwnProps&&(v=e(o,S)),y=t(d,v,S)}function a(){var e=r(F,S),o=!f(e,d);return d=e,o&&(y=t(d,v,S)),y}function s(r,e){var t=!l(e,S),o=!c(r,F);return F=r,S=e,t&&o?u():t?p():o?a():y}var c=n.areStatesEqual,l=n.areOwnPropsEqual,f=n.areStatePropsEqual,P=!1,F=void 0,S=void 0,d=void 0,v=void 0,y=void 0;return function(r,e){return P?s(r,e):i(r,e)}}function finalPropsSelectorFactory(r,e){var t=e.initMapStateToProps,o=e.initMapDispatchToProps,n=e.initMergeProps,i=_objectWithoutProperties(e,["initMapStateToProps","initMapDispatchToProps","initMergeProps"]),u=t(r,i),p=o(r,i),a=n(r,i);return(i.pure?pureFinalPropsSelectorFactory:impureFinalPropsSelectorFactory)(u,p,a,r,i)}exports.__esModule=!0,exports.impureFinalPropsSelectorFactory=impureFinalPropsSelectorFactory,exports.pureFinalPropsSelectorFactory=pureFinalPropsSelectorFactory,exports.default=finalPropsSelectorFactory;var _verifySubselectors=require("./verifySubselectors"),_verifySubselectors2=_interopRequireDefault(_verifySubselectors);

},{"./verifySubselectors":46}],46:[function(require,module,exports){
"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function verify(e,r,o){if(!e)throw new Error("Unexpected value for "+r+" in "+o+".");"mapStateToProps"!==r&&"mapDispatchToProps"!==r||e.hasOwnProperty("dependsOnOwnProps")||(0,_warning2.default)("The selector for "+r+" of "+o+" did not specify a value for dependsOnOwnProps.")}function verifySubselectors(e,r,o,n){verify(e,"mapStateToProps",n),verify(r,"mapDispatchToProps",n),verify(o,"mergeProps",n)}exports.__esModule=!0,exports.default=verifySubselectors;var _warning=require("../utils/warning"),_warning2=_interopRequireDefault(_warning);

},{"../utils/warning":52}],47:[function(require,module,exports){
"use strict";function _interopRequireDefault(n){return n&&n.__esModule?n:{default:n}}function wrapMapToPropsConstant(n){return function(e,r){function p(){return o}var o=n(e,r);return p.dependsOnOwnProps=!1,p}}function getDependsOnOwnProps(n){return null!==n.dependsOnOwnProps&&void 0!==n.dependsOnOwnProps?Boolean(n.dependsOnOwnProps):1!==n.length}function wrapMapToPropsFunc(n,e){return function(e,r){r.displayName;var p=function(n,e){return p.dependsOnOwnProps?p.mapToProps(n,e):p.mapToProps(n)};return p.dependsOnOwnProps=!0,p.mapToProps=function(e,r){p.mapToProps=n,p.dependsOnOwnProps=getDependsOnOwnProps(n);var o=p(e,r);return"function"==typeof o&&(p.mapToProps=o,p.dependsOnOwnProps=getDependsOnOwnProps(o),o=p(e,r)),o},p}}exports.__esModule=!0,exports.wrapMapToPropsConstant=wrapMapToPropsConstant,exports.getDependsOnOwnProps=getDependsOnOwnProps,exports.wrapMapToPropsFunc=wrapMapToPropsFunc;var _verifyPlainObject=require("../utils/verifyPlainObject"),_verifyPlainObject2=_interopRequireDefault(_verifyPlainObject);

},{"../utils/verifyPlainObject":51}],48:[function(require,module,exports){
"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}exports.__esModule=!0,exports.storeShape=exports.subscriptionShape=void 0;var _propTypes=require("prop-types"),_propTypes2=_interopRequireDefault(_propTypes),subscriptionShape=exports.subscriptionShape=_propTypes2.default.shape({trySubscribe:_propTypes2.default.func.isRequired,tryUnsubscribe:_propTypes2.default.func.isRequired,notifyNestedSubs:_propTypes2.default.func.isRequired,isSubscribed:_propTypes2.default.func.isRequired}),storeShape=exports.storeShape=_propTypes2.default.shape({subscribe:_propTypes2.default.func.isRequired,dispatch:_propTypes2.default.func.isRequired,getState:_propTypes2.default.func.isRequired});

},{"prop-types":"prop-types"}],49:[function(require,module,exports){
"use strict";function _classCallCheck(t,s){if(!(t instanceof s))throw new TypeError("Cannot call a class as a function")}function createListenerCollection(){var t=[],s=[];return{clear:function(){s=CLEARED,t=CLEARED},notify:function(){for(var e=t=s,n=0;n<e.length;n++)e[n]()},get:function(){return s},subscribe:function(e){var n=!0;return s===t&&(s=t.slice()),s.push(e),function(){n&&t!==CLEARED&&(n=!1,s===t&&(s=t.slice()),s.splice(s.indexOf(e),1))}}}}exports.__esModule=!0;var CLEARED=null,nullListeners={notify:function(){}},Subscription=function(){function t(s,e,n){_classCallCheck(this,t),this.store=s,this.parentSub=e,this.onStateChange=n,this.unsubscribe=null,this.listeners=nullListeners}return t.prototype.addNestedSub=function(t){return this.trySubscribe(),this.listeners.subscribe(t)},t.prototype.notifyNestedSubs=function(){this.listeners.notify()},t.prototype.isSubscribed=function(){return Boolean(this.unsubscribe)},t.prototype.trySubscribe=function(){this.unsubscribe||(this.unsubscribe=this.parentSub?this.parentSub.addNestedSub(this.onStateChange):this.store.subscribe(this.onStateChange),this.listeners=createListenerCollection())},t.prototype.tryUnsubscribe=function(){this.unsubscribe&&(this.unsubscribe(),this.unsubscribe=null,this.listeners.clear(),this.listeners=nullListeners)},t}();exports.default=Subscription;

},{}],50:[function(require,module,exports){
"use strict";function is(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!==e&&t!==t}function shallowEqual(e,t){if(is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(var l=0;l<r.length;l++)if(!hasOwn.call(t,r[l])||!is(e[r[l]],t[r[l]]))return!1;return!0}exports.__esModule=!0,exports.default=shallowEqual;var hasOwn=Object.prototype.hasOwnProperty;

},{}],51:[function(require,module,exports){
"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function verifyPlainObject(e,i,n){(0,_isPlainObject2.default)(e)||(0,_warning2.default)(n+"() in "+i+" must return a plain object. Instead received "+e+".")}exports.__esModule=!0,exports.default=verifyPlainObject;var _isPlainObject=require("lodash/isPlainObject"),_isPlainObject2=_interopRequireDefault(_isPlainObject),_warning=require("./warning"),_warning2=_interopRequireDefault(_warning);

},{"./warning":52,"lodash/isPlainObject":30}],52:[function(require,module,exports){
"use strict";function warning(o){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(o);try{throw new Error(o)}catch(o){}}exports.__esModule=!0,exports.default=warning;

},{}],53:[function(require,module,exports){
"use strict";function t(e){for(var t=arguments.length-1,n="Minified React error #"+e+"; visit http://facebook.github.io/react/docs/error-decoder.html?invariant="+e,r=0;r<t;r++)n+="&args[]="+encodeURIComponent(arguments[r+1]);throw t=Error(n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."),t.name="Invariant Violation",t.framesToPop=1,t}function v(e,t,n){this.props=e,this.context=t,this.refs=p,this.updater=n||u}function w(e,t,n){this.props=e,this.context=t,this.refs=p,this.updater=n||u}function x(){}function z(e,t,n){this.props=e,this.context=t,this.refs=p,this.updater=n||u}function G(e,t,n,r,o,u,l){return{$$typeof:E,type:e,key:t,ref:n,props:l,_owner:u}}function escape(e){var t={"=":"=0",":":"=2"};return"$"+(""+e).replace(/[=:]/g,function(e){return t[e]})}function L(e,t,n,r){if(K.length){var o=K.pop();return o.result=e,o.keyPrefix=t,o.func=n,o.context=r,o.count=0,o}return{result:e,keyPrefix:t,func:n,context:r,count:0}}function M(e){e.result=null,e.keyPrefix=null,e.func=null,e.context=null,e.count=0,10>K.length&&K.push(e)}function N(e,n,r,o){var u=typeof e;if("undefined"!==u&&"boolean"!==u||(e=null),null===e||"string"===u||"number"===u||"object"===u&&e.$$typeof===I)return r(o,e,""===n?"."+O(e,0):n),1;var l=0;if(n=""===n?".":n+":",Array.isArray(e))for(var i=0;i<e.length;i++){var c=n+O(u=e[i],i);l+=N(u,c,r,o)}else if("function"==typeof(c=H&&e[H]||e["@@iterator"]))for(e=c.call(e),i=0;!(u=e.next()).done;)u=u.value,c=n+O(u,i++),l+=N(u,c,r,o);else"object"===u&&(r=""+e,t("31","[object Object]"===r?"object with keys {"+Object.keys(e).join(", ")+"}":r,""));return l}function O(e,t){return"object"==typeof e&&null!==e&&null!=e.key?escape(e.key):t.toString(36)}function P(e,t){e.func.call(e.context,t,e.count++)}function Q(e,t,n){var o=e.result,u=e.keyPrefix;e=e.func.call(e.context,t,e.count++),Array.isArray(e)?R(e,o,n,r.thatReturnsArgument):null!=e&&(G.isValidElement(e)&&(e=G.cloneAndReplaceKey(e,u+(!e.key||t&&t.key===e.key?"":(""+e.key).replace(J,"$&/")+"/")+n)),o.push(e))}function R(e,t,n,r,o){var u="";null!=n&&(u=(""+n).replace(J,"$&/")+"/"),t=L(t,u,r,o),null==e||N(e,"",Q,t),M(t)}var f=require("object-assign"),p=require("fbjs/lib/emptyObject");require("fbjs/lib/invariant");var r=require("fbjs/lib/emptyFunction"),u={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}};v.prototype.isReactComponent={},v.prototype.setState=function(e,n){"object"!=typeof e&&"function"!=typeof e&&null!=e&&t("85"),this.updater.enqueueSetState(this,e,n,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},x.prototype=v.prototype;var y=w.prototype=new x;y.constructor=w,f(y,v.prototype),y.isPureReactComponent=!0;var A=z.prototype=new x;A.constructor=z,f(A,v.prototype),A.unstable_isAsyncReactComponent=!0,A.render=function(){return this.props.children};var B={Component:v,PureComponent:w,AsyncComponent:z},C={current:null},D=Object.prototype.hasOwnProperty,E="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,F={key:!0,ref:!0,__self:!0,__source:!0};G.createElement=function(e,t,n){var r,o={},u=null,l=null,i=null,c=null;if(null!=t)for(r in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(u=""+t.key),i=void 0===t.__self?null:t.__self,c=void 0===t.__source?null:t.__source,t)D.call(t,r)&&!F.hasOwnProperty(r)&&(o[r]=t[r]);var f=arguments.length-2;if(1===f)o.children=n;else if(1<f){for(var a=Array(f),p=0;p<f;p++)a[p]=arguments[p+2];o.children=a}if(e&&e.defaultProps)for(r in f=e.defaultProps)void 0===o[r]&&(o[r]=f[r]);return G(e,u,l,i,c,C.current,o)},G.createFactory=function(e){var t=G.createElement.bind(null,e);return t.type=e,t},G.cloneAndReplaceKey=function(e,t){return G(e.type,t,e.ref,e._self,e._source,e._owner,e.props)},G.cloneElement=function(e,t,n){var r=f({},e.props),o=e.key,u=e.ref,l=e._self,i=e._source,c=e._owner;if(null!=t){if(void 0!==t.ref&&(u=t.ref,c=C.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(p in t)D.call(t,p)&&!F.hasOwnProperty(p)&&(r[p]=void 0===t[p]&&void 0!==a?a[p]:t[p])}var p=arguments.length-2;if(1===p)r.children=n;else if(1<p){a=Array(p);for(var s=0;s<p;s++)a[s]=arguments[s+2];r.children=a}return G(e.type,o,u,l,i,c,r)},G.isValidElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===E};var H="function"==typeof Symbol&&Symbol.iterator,I="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,J=/\/+/g,K=[],S={forEach:function(e,t,n){if(null==e)return e;t=L(null,null,t,n),null==e||N(e,"",P,t),M(t)},map:function(e,t,n){if(null==e)return e;var r=[];return R(e,r,null,t,n),r},count:function(e){return null==e?0:N(e,"",r.thatReturnsNull,null)},toArray:function(e){var t=[];return R(e,t,null,r.thatReturnsArgument),t}};module.exports={Children:{map:S.map,forEach:S.forEach,count:S.count,toArray:S.toArray,only:function(e){return G.isValidElement(e)||t("143"),e}},Component:B.Component,PureComponent:B.PureComponent,unstable_AsyncComponent:B.AsyncComponent,createElement:G.createElement,cloneElement:G.cloneElement,isValidElement:G.isValidElement,createFactory:G.createFactory,version:"16.0.0",__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:{ReactCurrentOwner:C,assign:f}};

},{"fbjs/lib/emptyFunction":9,"fbjs/lib/emptyObject":10,"fbjs/lib/invariant":13,"object-assign":35}],54:[function(require,module,exports){
"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function applyMiddleware(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return function(r,n,o){var a=e(r,n,o),u=a.dispatch,p=[],i={getState:a.getState,dispatch:function(e){return u(e)}};return p=t.map(function(e){return e(i)}),u=_compose2.default.apply(void 0,p)(a.dispatch),_extends({},a,{dispatch:u})}}}exports.__esModule=!0;var _extends=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e};exports.default=applyMiddleware;var _compose=require("./compose"),_compose2=_interopRequireDefault(_compose);

},{"./compose":57}],55:[function(require,module,exports){
"use strict";function bindActionCreator(t,o){return function(){return o(t.apply(void 0,arguments))}}function bindActionCreators(t,o){if("function"==typeof t)return bindActionCreator(t,o);if("object"!=typeof t||null===t)throw new Error("bindActionCreators expected an object or a function, instead received "+(null===t?"null":typeof t)+'. Did you write "import ActionCreators from" instead of "import * as ActionCreators from"?');for(var r=Object.keys(t),n={},e=0;e<r.length;e++){var i=r[e],c=t[i];"function"==typeof c&&(n[i]=bindActionCreator(c,o))}return n}exports.__esModule=!0,exports.default=bindActionCreators;

},{}],56:[function(require,module,exports){
"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function getUndefinedStateErrorMessage(e,t){var n=t&&t.type;return"Given action "+(n&&'"'+n.toString()+'"'||"an action")+', reducer "'+e+'" returned undefined. To ignore an action, you must explicitly return the previous state. If you want this reducer to hold no value, you can return null instead of undefined.'}function getUnexpectedStateShapeWarningMessage(e,t,n,r){var i=Object.keys(t),o=n&&n.type===_createStore.ActionTypes.INIT?"preloadedState argument passed to createStore":"previous state received by the reducer";if(0===i.length)return"Store does not have a valid reducer. Make sure the argument passed to combineReducers is an object whose values are reducers.";if(!(0,_isPlainObject2.default)(e))return"The "+o+' has unexpected type of "'+{}.toString.call(e).match(/\s([a-z|A-Z]+)/)[1]+'". Expected argument to be an object with the following keys: "'+i.join('", "')+'"';var a=Object.keys(e).filter(function(e){return!t.hasOwnProperty(e)&&!r[e]});return a.forEach(function(e){r[e]=!0}),a.length>0?"Unexpected "+(a.length>1?"keys":"key")+' "'+a.join('", "')+'" found in '+o+'. Expected to find one of the known reducer keys instead: "'+i.join('", "')+'". Unexpected keys will be ignored.':void 0}function assertReducerShape(e){Object.keys(e).forEach(function(t){var n=e[t];if(void 0===n(void 0,{type:_createStore.ActionTypes.INIT}))throw new Error('Reducer "'+t+"\" returned undefined during initialization. If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.");if(void 0===n(void 0,{type:"@@redux/PROBE_UNKNOWN_ACTION_"+Math.random().toString(36).substring(7).split("").join(".")}))throw new Error('Reducer "'+t+"\" returned undefined when probed with a random type. Don't try to handle "+_createStore.ActionTypes.INIT+' or other actions in "redux/*" namespace. They are considered private. Instead, you must return the current state for any unknown actions, unless it is undefined, in which case you must return the initial state, regardless of the action type. The initial state may not be undefined, but can be null.')})}function combineReducers(e){for(var t=Object.keys(e),n={},r=0;r<t.length;r++){var i=t[r];"function"==typeof e[i]&&(n[i]=e[i])}var o=Object.keys(n),a=void 0;try{assertReducerShape(n)}catch(e){a=e}return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments[1];if(a)throw a;for(var r=!1,i={},u=0;u<o.length;u++){var s=o[u],d=n[s],c=e[s],l=d(c,t);if(void 0===l){var f=getUndefinedStateErrorMessage(s,t);throw new Error(f)}i[s]=l,r=r||l!==c}return r?i:e}}exports.__esModule=!0,exports.default=combineReducers;var _createStore=require("./createStore"),_isPlainObject=require("lodash/isPlainObject"),_isPlainObject2=_interopRequireDefault(_isPlainObject),_warning=require("./utils/warning"),_warning2=_interopRequireDefault(_warning);

},{"./createStore":58,"./utils/warning":59,"lodash/isPlainObject":30}],57:[function(require,module,exports){
"use strict";function compose(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return 0===r.length?function(e){return e}:1===r.length?r[0]:r.reduce(function(e,r){return function(){return e(r.apply(void 0,arguments))}})}exports.__esModule=!0,exports.default=compose;

},{}],58:[function(require,module,exports){
"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function createStore(e,t,r){function n(){b===f&&(b=f.slice())}function o(){return a}function i(e){if("function"!=typeof e)throw new Error("Expected listener to be a function.");var t=!0;return n(),b.push(e),function(){if(t){t=!1,n();var r=b.indexOf(e);b.splice(r,1)}}}function c(e){if(!(0,_isPlainObject2.default)(e))throw new Error("Actions must be plain objects. Use custom middleware for async actions.");if(void 0===e.type)throw new Error('Actions may not have an undefined "type" property. Have you misspelled a constant?');if(l)throw new Error("Reducers may not dispatch actions.");try{l=!0,a=s(a,e)}finally{l=!1}for(var t=f=b,r=0;r<t.length;r++)(0,t[r])();return e}var u;if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error("Expected the enhancer to be a function.");return r(createStore)(e,t)}if("function"!=typeof e)throw new Error("Expected the reducer to be a function.");var s=e,a=t,f=[],b=f,l=!1;return c({type:ActionTypes.INIT}),u={dispatch:c,subscribe:i,getState:o,replaceReducer:function(e){if("function"!=typeof e)throw new Error("Expected the nextReducer to be a function.");s=e,c({type:ActionTypes.INIT})}},u[_symbolObservable2.default]=function(){var e,t=i;return e={subscribe:function(e){function r(){e.next&&e.next(o())}if("object"!=typeof e)throw new TypeError("Expected the observer to be an object.");return r(),{unsubscribe:t(r)}}},e[_symbolObservable2.default]=function(){return this},e},u}exports.__esModule=!0,exports.ActionTypes=void 0,exports.default=createStore;var _isPlainObject=require("lodash/isPlainObject"),_isPlainObject2=_interopRequireDefault(_isPlainObject),_symbolObservable=require("symbol-observable"),_symbolObservable2=_interopRequireDefault(_symbolObservable),ActionTypes=exports.ActionTypes={INIT:"@@redux/INIT"};

},{"lodash/isPlainObject":30,"symbol-observable":60}],59:[function(require,module,exports){
"use strict";function warning(o){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(o);try{throw new Error(o)}catch(o){}}exports.__esModule=!0,exports.default=warning;

},{}],60:[function(require,module,exports){
module.exports=require("./lib/index");

},{"./lib/index":61}],61:[function(require,module,exports){
(function (global){
"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(exports,"__esModule",{value:!0});var _ponyfill=require("./ponyfill"),_ponyfill2=_interopRequireDefault(_ponyfill),root;root="undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof module?module:Function("return this")();var result=(0,_ponyfill2.default)(root);exports.default=result;

}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})

},{"./ponyfill":62}],62:[function(require,module,exports){
"use strict";function symbolObservablePonyfill(e){var b,l=e.Symbol;return"function"==typeof l?l.observable?b=l.observable:(b=l("observable"),l.observable=b):b="@@observable",b}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=symbolObservablePonyfill;

},{}],"classnames":[function(require,module,exports){
!function(){"use strict";function e(){for(var r=[],o=0;o<arguments.length;o++){var f=arguments[o];if(f){var i=typeof f;if("string"===i||"number"===i)r.push(f);else if(Array.isArray(f))r.push(e.apply(null,f));else if("object"===i)for(var t in f)n.call(f,t)&&f[t]&&r.push(t)}}return r.join(" ")}var n={}.hasOwnProperty;"undefined"!=typeof module&&module.exports?module.exports=e:"function"==typeof define&&"object"==typeof define.amd&&define.amd?define("classnames",[],function(){return e}):window.classNames=e}();

},{}],"lodash":[function(require,module,exports){
(function (global){
(function(){function n(n,t){return n.set(t[0],t[1]),n}function t(n,t){return n.add(t),n}function r(n,t,r){switch(r.length){case 0:return n.call(t);case 1:return n.call(t,r[0]);case 2:return n.call(t,r[0],r[1]);case 3:return n.call(t,r[0],r[1],r[2])}return n.apply(t,r)}function e(n,t,r,e){for(var u=-1,i=null==n?0:n.length;++u<i;){var o=n[u];t(e,o,r(o),n)}return e}function u(n,t){for(var r=-1,e=null==n?0:n.length;++r<e&&!1!==t(n[r],r,n););return n}function i(n,t){for(var r=null==n?0:n.length;r--&&!1!==t(n[r],r,n););return n}function o(n,t){for(var r=-1,e=null==n?0:n.length;++r<e;)if(!t(n[r],r,n))return!1;return!0}function f(n,t){for(var r=-1,e=null==n?0:n.length,u=0,i=[];++r<e;){var o=n[r];t(o,r,n)&&(i[u++]=o)}return i}function c(n,t){return!!(null==n?0:n.length)&&b(n,t,0)>-1}function a(n,t,r){for(var e=-1,u=null==n?0:n.length;++e<u;)if(r(t,n[e]))return!0;return!1}function l(n,t){for(var r=-1,e=null==n?0:n.length,u=Array(e);++r<e;)u[r]=t(n[r],r,n);return u}function s(n,t){for(var r=-1,e=t.length,u=n.length;++r<e;)n[u+r]=t[r];return n}function h(n,t,r,e){var u=-1,i=null==n?0:n.length;for(e&&i&&(r=n[++u]);++u<i;)r=t(r,n[u],u,n);return r}function p(n,t,r,e){var u=null==n?0:n.length;for(e&&u&&(r=n[--u]);u--;)r=t(r,n[u],u,n);return r}function _(n,t){for(var r=-1,e=null==n?0:n.length;++r<e;)if(t(n[r],r,n))return!0;return!1}function v(n){return n.split("")}function g(n){return n.match(Bt)||[]}function y(n,t,r){var e;return r(n,function(n,r,u){if(t(n,r,u))return e=r,!1}),e}function d(n,t,r,e){for(var u=n.length,i=r+(e?1:-1);e?i--:++i<u;)if(t(n[i],i,n))return i;return-1}function b(n,t,r){return t===t?K(n,t,r):d(n,m,r)}function w(n,t,r,e){for(var u=r-1,i=n.length;++u<i;)if(e(n[u],t))return u;return-1}function m(n){return n!==n}function x(n,t){var r=null==n?0:n.length;return r?I(n,t)/r:Sn}function j(n){return function(t){return null==t?X:t[n]}}function A(n){return function(t){return null==n?X:n[t]}}function k(n,t,r,e,u){return u(n,function(n,u,i){r=e?(e=!1,n):t(r,n,u,i)}),r}function O(n,t){var r=n.length;for(n.sort(t);r--;)n[r]=n[r].value;return n}function I(n,t){for(var r,e=-1,u=n.length;++e<u;){var i=t(n[e]);i!==X&&(r=r===X?i:r+i)}return r}function R(n,t){for(var r=-1,e=Array(n);++r<n;)e[r]=t(r);return e}function z(n,t){return l(t,function(t){return[t,n[t]]})}function E(n){return function(t){return n(t)}}function S(n,t){return l(t,function(t){return n[t]})}function L(n,t){return n.has(t)}function W(n,t){for(var r=-1,e=n.length;++r<e&&b(t,n[r],0)>-1;);return r}function C(n,t){for(var r=n.length;r--&&b(t,n[r],0)>-1;);return r}function U(n,t){for(var r=n.length,e=0;r--;)n[r]===t&&++e;return e}function B(n){return"\\"+br[n]}function T(n,t){return null==n?X:n[t]}function $(n){return pr.test(n)}function D(n){return _r.test(n)}function M(n){for(var t,r=[];!(t=n.next()).done;)r.push(t.value);return r}function F(n){var t=-1,r=Array(n.size);return n.forEach(function(n,e){r[++t]=[e,n]}),r}function N(n,t){return function(r){return n(t(r))}}function P(n,t){for(var r=-1,e=n.length,u=0,i=[];++r<e;){var o=n[r];o!==t&&o!==on||(n[r]=on,i[u++]=r)}return i}function q(n){var t=-1,r=Array(n.size);return n.forEach(function(n){r[++t]=n}),r}function Z(n){var t=-1,r=Array(n.size);return n.forEach(function(n){r[++t]=[n,n]}),r}function K(n,t,r){for(var e=r-1,u=n.length;++e<u;)if(n[e]===t)return e;return-1}function V(n,t,r){for(var e=r+1;e--;)if(n[e]===t)return e;return e}function G(n){return $(n)?J(n):Br(n)}function H(n){return $(n)?Y(n):v(n)}function J(n){for(var t=sr.lastIndex=0;sr.test(n);)++t;return t}function Y(n){return n.match(sr)||[]}function Q(n){return n.match(hr)||[]}var X,nn=200,tn="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",rn="Expected a function",en="__lodash_hash_undefined__",un=500,on="__lodash_placeholder__",fn=1,cn=2,an=4,ln=1,sn=2,hn=1,pn=2,_n=4,vn=8,gn=16,yn=32,dn=64,bn=128,wn=256,mn=512,xn=30,jn="...",An=800,kn=16,On=1,In=2,Rn=1/0,zn=9007199254740991,En=1.7976931348623157e308,Sn=NaN,Ln=4294967295,Wn=Ln-1,Cn=Ln>>>1,Un=[["ary",bn],["bind",hn],["bindKey",pn],["curry",vn],["curryRight",gn],["flip",mn],["partial",yn],["partialRight",dn],["rearg",wn]],Bn="[object Arguments]",Tn="[object Array]",$n="[object AsyncFunction]",Dn="[object Boolean]",Mn="[object Date]",Fn="[object DOMException]",Nn="[object Error]",Pn="[object Function]",qn="[object GeneratorFunction]",Zn="[object Map]",Kn="[object Number]",Vn="[object Null]",Gn="[object Object]",Hn="[object Proxy]",Jn="[object RegExp]",Yn="[object Set]",Qn="[object String]",Xn="[object Symbol]",nt="[object Undefined]",tt="[object WeakMap]",rt="[object WeakSet]",et="[object ArrayBuffer]",ut="[object DataView]",it="[object Float32Array]",ot="[object Float64Array]",ft="[object Int8Array]",ct="[object Int16Array]",at="[object Int32Array]",lt="[object Uint8Array]",st="[object Uint8ClampedArray]",ht="[object Uint16Array]",pt="[object Uint32Array]",_t=/\b__p \+= '';/g,vt=/\b(__p \+=) '' \+/g,gt=/(__e\(.*?\)|\b__t\)) \+\n'';/g,yt=/&(?:amp|lt|gt|quot|#39);/g,dt=/[&<>"']/g,bt=RegExp(yt.source),wt=RegExp(dt.source),mt=/<%-([\s\S]+?)%>/g,xt=/<%([\s\S]+?)%>/g,jt=/<%=([\s\S]+?)%>/g,At=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,kt=/^\w*$/,Ot=/^\./,It=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Rt=/[\\^$.*+?()[\]{}|]/g,zt=RegExp(Rt.source),Et=/^\s+|\s+$/g,St=/^\s+/,Lt=/\s+$/,Wt=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Ct=/\{\n\/\* \[wrapped with (.+)\] \*/,Ut=/,? & /,Bt=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Tt=/\\(\\)?/g,$t=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Dt=/\w*$/,Mt=/^[-+]0x[0-9a-f]+$/i,Ft=/^0b[01]+$/i,Nt=/^\[object .+?Constructor\]$/,Pt=/^0o[0-7]+$/i,qt=/^(?:0|[1-9]\d*)$/,Zt=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Kt=/($^)/,Vt=/['\n\r\u2028\u2029\\]/g,Gt="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",Ht="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Jt="["+Ht+"]",Yt="["+Gt+"]",Qt="[a-z\\xdf-\\xf6\\xf8-\\xff]",Xt="[^\\ud800-\\udfff"+Ht+"\\d+\\u2700-\\u27bfa-z\\xdf-\\xf6\\xf8-\\xffA-Z\\xc0-\\xd6\\xd8-\\xde]",nr="\\ud83c[\\udffb-\\udfff]",tr="(?:\\ud83c[\\udde6-\\uddff]){2}",rr="[\\ud800-\\udbff][\\udc00-\\udfff]",er="[A-Z\\xc0-\\xd6\\xd8-\\xde]",ur="(?:"+Qt+"|"+Xt+")",ir="(?:[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]|\\ud83c[\\udffb-\\udfff])?",or="[\\ufe0e\\ufe0f]?"+ir+("(?:\\u200d(?:"+["[^\\ud800-\\udfff]",tr,rr].join("|")+")[\\ufe0e\\ufe0f]?"+ir+")*"),fr="(?:"+["[\\u2700-\\u27bf]",tr,rr].join("|")+")"+or,cr="(?:"+["[^\\ud800-\\udfff]"+Yt+"?",Yt,tr,rr,"[\\ud800-\\udfff]"].join("|")+")",ar=RegExp("['’]","g"),lr=RegExp(Yt,"g"),sr=RegExp(nr+"(?="+nr+")|"+cr+or,"g"),hr=RegExp([er+"?"+Qt+"+(?:['’](?:d|ll|m|re|s|t|ve))?(?="+[Jt,er,"$"].join("|")+")","(?:[A-Z\\xc0-\\xd6\\xd8-\\xde]|[^\\ud800-\\udfff\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000\\d+\\u2700-\\u27bfa-z\\xdf-\\xf6\\xf8-\\xffA-Z\\xc0-\\xd6\\xd8-\\xde])+(?:['’](?:D|LL|M|RE|S|T|VE))?(?="+[Jt,er+ur,"$"].join("|")+")",er+"?"+ur+"+(?:['’](?:d|ll|m|re|s|t|ve))?",er+"+(?:['’](?:D|LL|M|RE|S|T|VE))?","\\d*(?:(?:1ST|2ND|3RD|(?![123])\\dTH)\\b)","\\d*(?:(?:1st|2nd|3rd|(?![123])\\dth)\\b)","\\d+",fr].join("|"),"g"),pr=RegExp("[\\u200d\\ud800-\\udfff"+Gt+"\\ufe0e\\ufe0f]"),_r=/[a-z][A-Z]|[A-Z]{2,}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,vr=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],gr=-1,yr={};yr[it]=yr[ot]=yr[ft]=yr[ct]=yr[at]=yr[lt]=yr[st]=yr[ht]=yr[pt]=!0,yr[Bn]=yr[Tn]=yr[et]=yr[Dn]=yr[ut]=yr[Mn]=yr[Nn]=yr[Pn]=yr[Zn]=yr[Kn]=yr[Gn]=yr[Jn]=yr[Yn]=yr[Qn]=yr[tt]=!1;var dr={};dr[Bn]=dr[Tn]=dr[et]=dr[ut]=dr[Dn]=dr[Mn]=dr[it]=dr[ot]=dr[ft]=dr[ct]=dr[at]=dr[Zn]=dr[Kn]=dr[Gn]=dr[Jn]=dr[Yn]=dr[Qn]=dr[Xn]=dr[lt]=dr[st]=dr[ht]=dr[pt]=!0,dr[Nn]=dr[Pn]=dr[tt]=!1;var br={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},wr=parseFloat,mr=parseInt,xr="object"==typeof global&&global&&global.Object===Object&&global,jr="object"==typeof self&&self&&self.Object===Object&&self,Ar=xr||jr||Function("return this")(),kr="object"==typeof exports&&exports&&!exports.nodeType&&exports,Or=kr&&"object"==typeof module&&module&&!module.nodeType&&module,Ir=Or&&Or.exports===kr,Rr=Ir&&xr.process,zr=function(){try{return Rr&&Rr.binding&&Rr.binding("util")}catch(n){}}(),Er=zr&&zr.isArrayBuffer,Sr=zr&&zr.isDate,Lr=zr&&zr.isMap,Wr=zr&&zr.isRegExp,Cr=zr&&zr.isSet,Ur=zr&&zr.isTypedArray,Br=j("length"),Tr=A({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),$r=A({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),Dr=A({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),Mr=function v(A){function K(n){if(no(n)&&!Zc(n)&&!(n instanceof Bt)){if(n instanceof Y)return n;if(qo.call(n,"__wrapped__"))return Ai(n)}return new Y(n)}function J(){}function Y(n,t){this.__wrapped__=n,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=X}function Bt(n){this.__wrapped__=n,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=Ln,this.__views__=[]}function Gt(n){var t=-1,r=null==n?0:n.length;for(this.clear();++t<r;){var e=n[t];this.set(e[0],e[1])}}function Ht(n){var t=-1,r=null==n?0:n.length;for(this.clear();++t<r;){var e=n[t];this.set(e[0],e[1])}}function Jt(n){var t=-1,r=null==n?0:n.length;for(this.clear();++t<r;){var e=n[t];this.set(e[0],e[1])}}function Yt(n){var t=-1,r=null==n?0:n.length;for(this.__data__=new Jt;++t<r;)this.add(n[t])}function Qt(n){var t=this.__data__=new Ht(n);this.size=t.size}function Xt(n,t){var r=Zc(n),e=!r&&qc(n),u=!r&&!e&&Vc(n),i=!r&&!e&&!u&&Qc(n),o=r||e||u||i,f=o?R(n.length,To):[],c=f.length;for(var a in n)!t&&!qo.call(n,a)||o&&("length"==a||u&&("offset"==a||"parent"==a)||i&&("buffer"==a||"byteLength"==a||"byteOffset"==a)||ei(a,c))||f.push(a);return f}function nr(n){var t=n.length;return t?n[xe(0,t-1)]:X}function tr(n,t){return wi(ou(n),pr(t,0,n.length))}function rr(n){return wi(ou(n))}function er(n,t,r){(r===X||Ki(n[t],r))&&(r!==X||t in n)||sr(n,t,r)}function ur(n,t,r){var e=n[t];qo.call(n,t)&&Ki(e,r)&&(r!==X||t in n)||sr(n,t,r)}function ir(n,t){for(var r=n.length;r--;)if(Ki(n[r][0],t))return r;return-1}function or(n,t,r,e){return Pf(n,function(n,u,i){t(e,n,r(n),i)}),e}function fr(n,t){return n&&fu(t,_o(t),n)}function cr(n,t){return n&&fu(t,vo(t),n)}function sr(n,t,r){"__proto__"==t&&af?af(n,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):n[t]=r}function hr(n,t){for(var r=-1,e=t.length,u=Eo(e),i=null==n;++r<e;)u[r]=i?X:ho(n,t[r]);return u}function pr(n,t,r){return n===n&&(r!==X&&(n=n<=r?n:r),t!==X&&(n=n>=t?n:t)),n}function _r(n,t,r,e,i,o){var f,c=t&fn,a=t&cn,l=t&an;if(r&&(f=i?r(n,e,i,o):r(n)),f!==X)return f;if(!Xi(n))return n;var s=Zc(n);if(s){if(f=Qu(n),!c)return ou(n,f)}else{var h=tc(n),p=h==Pn||h==qn;if(Vc(n))return Ge(n,c);if(h==Gn||h==Bn||p&&!i){if(f=a||p?{}:Xu(n),!c)return a?au(n,cr(f,n)):cu(n,fr(f,n))}else{if(!dr[h])return i?n:{};f=ni(n,h,_r,c)}}o||(o=new Qt);var _=o.get(n);if(_)return _;o.set(n,f);var v=s?X:(l?a?Fu:Mu:a?vo:_o)(n);return u(v||n,function(e,u){v&&(e=n[u=e]),ur(f,u,_r(e,t,r,u,n,o))}),f}function br(n){var t=_o(n);return function(r){return xr(r,n,t)}}function xr(n,t,r){var e=r.length;if(null==n)return!e;for(n=Uo(n);e--;){var u=r[e],i=t[u],o=n[u];if(o===X&&!(u in n)||!i(o))return!1}return!0}function jr(n,t,r){if("function"!=typeof n)throw new $o(rn);return uc(function(){n.apply(X,r)},t)}function kr(n,t,r,e){var u=-1,i=c,o=!0,f=n.length,s=[],h=t.length;if(!f)return s;r&&(t=l(t,E(r))),e?(i=a,o=!1):t.length>=nn&&(i=L,o=!1,t=new Yt(t));n:for(;++u<f;){var p=n[u],_=null==r?p:r(p);if(p=e||0!==p?p:0,o&&_===_){for(var v=h;v--;)if(t[v]===_)continue n;s.push(p)}else i(t,_,e)||s.push(p)}return s}function Or(n,t){var r=!0;return Pf(n,function(n,e,u){return r=!!t(n,e,u)}),r}function Rr(n,t,r){for(var e=-1,u=n.length;++e<u;){var i=n[e],o=t(i);if(null!=o&&(f===X?o===o&&!uo(o):r(o,f)))var f=o,c=i}return c}function zr(n,t,r,e){var u=n.length;for((r=fo(r))<0&&(r=-r>u?0:u+r),(e=e===X||e>u?u:fo(e))<0&&(e+=u),e=r>e?0:co(e);r<e;)n[r++]=t;return n}function Br(n,t){var r=[];return Pf(n,function(n,e,u){t(n,e,u)&&r.push(n)}),r}function Fr(n,t,r,e,u){var i=-1,o=n.length;for(r||(r=ri),u||(u=[]);++i<o;){var f=n[i];t>0&&r(f)?t>1?Fr(f,t-1,r,e,u):s(u,f):e||(u[u.length]=f)}return u}function Nr(n,t){return n&&Zf(n,t,_o)}function Pr(n,t){return n&&Kf(n,t,_o)}function qr(n,t){return f(t,function(t){return Ji(n[t])})}function Zr(n,t){for(var r=0,e=(t=Ke(t,n)).length;null!=n&&r<e;)n=n[mi(t[r++])];return r&&r==e?n:X}function Kr(n,t,r){var e=t(n);return Zc(n)?e:s(e,r(n))}function Vr(n){return null==n?n===X?nt:Vn:cf&&cf in Uo(n)?Gu(n):_i(n)}function Gr(n,t){return n>t}function Hr(n,t){return null!=n&&qo.call(n,t)}function Jr(n,t){return null!=n&&t in Uo(n)}function Yr(n,t,r){return n>=mf(t,r)&&n<wf(t,r)}function Qr(n,t,r){for(var e=r?a:c,u=n[0].length,i=n.length,o=i,f=Eo(i),s=1/0,h=[];o--;){var p=n[o];o&&t&&(p=l(p,E(t))),s=mf(p.length,s),f[o]=!r&&(t||u>=120&&p.length>=120)?new Yt(o&&p):X}p=n[0];var _=-1,v=f[0];n:for(;++_<u&&h.length<s;){var g=p[_],y=t?t(g):g;if(g=r||0!==g?g:0,!(v?L(v,y):e(h,y,r))){for(o=i;--o;){var d=f[o];if(!(d?L(d,y):e(n[o],y,r)))continue n}v&&v.push(y),h.push(g)}}return h}function Xr(n,t,r,e){return Nr(n,function(n,u,i){t(e,r(n),u,i)}),e}function ne(n,t,e){var u=null==(n=gi(n,t=Ke(t,n)))?n:n[mi(zi(t))];return null==u?X:r(u,n,e)}function te(n){return no(n)&&Vr(n)==Bn}function re(n,t,r,e,u){return n===t||(null==n||null==t||!no(n)&&!no(t)?n!==n&&t!==t:ee(n,t,r,e,re,u))}function ee(n,t,r,e,u,i){var o=Zc(n),f=Zc(t),c=o?Tn:tc(n),a=f?Tn:tc(t),l=(c=c==Bn?Gn:c)==Gn,s=(a=a==Bn?Gn:a)==Gn,h=c==a;if(h&&Vc(n)){if(!Vc(t))return!1;o=!0,l=!1}if(h&&!l)return i||(i=new Qt),o||Qc(n)?Bu(n,t,r,e,u,i):Tu(n,t,c,r,e,u,i);if(!(r&ln)){var p=l&&qo.call(n,"__wrapped__"),_=s&&qo.call(t,"__wrapped__");if(p||_){var v=p?n.value():n,g=_?t.value():t;return i||(i=new Qt),u(v,g,r,e,i)}}return!!h&&(i||(i=new Qt),$u(n,t,r,e,u,i))}function ue(n,t,r,e){var u=r.length,i=u,o=!e;if(null==n)return!i;for(n=Uo(n);u--;){var f=r[u];if(o&&f[2]?f[1]!==n[f[0]]:!(f[0]in n))return!1}for(;++u<i;){var c=(f=r[u])[0],a=n[c],l=f[1];if(o&&f[2]){if(a===X&&!(c in n))return!1}else{var s=new Qt;if(e)var h=e(a,l,c,n,t,s);if(!(h===X?re(l,a,ln|sn,e,s):h))return!1}}return!0}function ie(n){return!(!Xi(n)||ci(n))&&(Ji(n)?Jo:Nt).test(xi(n))}function oe(n){return"function"==typeof n?n:null==n?jo:"object"==typeof n?Zc(n)?he(n[0],n[1]):se(n):Io(n)}function fe(n){if(!ai(n))return bf(n);var t=[];for(var r in Uo(n))qo.call(n,r)&&"constructor"!=r&&t.push(r);return t}function ce(n){if(!Xi(n))return pi(n);var t=ai(n),r=[];for(var e in n)("constructor"!=e||!t&&qo.call(n,e))&&r.push(e);return r}function ae(n,t){return n<t}function le(n,t){var r=-1,e=Vi(n)?Eo(n.length):[];return Pf(n,function(n,u,i){e[++r]=t(n,u,i)}),e}function se(n){var t=Ku(n);return 1==t.length&&t[0][2]?si(t[0][0],t[0][1]):function(r){return r===n||ue(r,n,t)}}function he(n,t){return ii(n)&&li(t)?si(mi(n),t):function(r){var e=ho(r,n);return e===X&&e===t?po(r,n):re(t,e,ln|sn)}}function pe(n,t,r,e,u){n!==t&&Zf(t,function(i,o){if(Xi(i))u||(u=new Qt),_e(n,t,o,r,pe,e,u);else{var f=e?e(n[o],i,o+"",n,t,u):X;f===X&&(f=i),er(n,o,f)}},vo)}function _e(n,t,r,e,u,i,o){var f=n[r],c=t[r],a=o.get(c);if(a)er(n,r,a);else{var l=i?i(f,c,r+"",n,t,o):X,s=l===X;if(s){var h=Zc(c),p=!h&&Vc(c),_=!h&&!p&&Qc(c);l=c,h||p||_?Zc(f)?l=f:Gi(f)?l=ou(f):p?(s=!1,l=Ge(c,!0)):_?(s=!1,l=tu(c,!0)):l=[]:ro(c)||qc(c)?(l=f,qc(f)?l=lo(f):(!Xi(f)||e&&Ji(f))&&(l=Xu(c))):s=!1}s&&(o.set(c,l),u(l,c,e,i,o),o.delete(c)),er(n,r,l)}}function ve(n,t){var r=n.length;if(r)return t+=t<0?r:0,ei(t,r)?n[t]:X}function ge(n,t,r){var e=-1;return t=l(t.length?t:[jo],E(qu())),O(le(n,function(n,r,u){return{criteria:l(t,function(t){return t(n)}),index:++e,value:n}}),function(n,t){return eu(n,t,r)})}function ye(n,t){return de(n,t,function(t,r){return po(n,r)})}function de(n,t,r){for(var e=-1,u=t.length,i={};++e<u;){var o=t[e],f=Zr(n,o);r(f,o)&&Re(i,Ke(o,n),f)}return i}function be(n){return function(t){return Zr(t,n)}}function we(n,t,r,e){var u=e?w:b,i=-1,o=t.length,f=n;for(n===t&&(t=ou(t)),r&&(f=l(n,E(r)));++i<o;)for(var c=0,a=t[i],s=r?r(a):a;(c=u(f,s,c,e))>-1;)f!==n&&uf.call(f,c,1),uf.call(n,c,1);return n}function me(n,t){for(var r=n?t.length:0,e=r-1;r--;){var u=t[r];if(r==e||u!==i){var i=u;ei(u)?uf.call(n,u,1):$e(n,u)}}return n}function xe(n,t){return n+_f(Af()*(t-n+1))}function je(n,t,r,e){for(var u=-1,i=wf(pf((t-n)/(r||1)),0),o=Eo(i);i--;)o[e?i:++u]=n,n+=r;return o}function Ae(n,t){var r="";if(!n||t<1||t>zn)return r;do{t%2&&(r+=n),(t=_f(t/2))&&(n+=n)}while(t);return r}function ke(n,t){return ic(vi(n,t,jo),n+"")}function Oe(n){return nr(yo(n))}function Ie(n,t){var r=yo(n);return wi(r,pr(t,0,r.length))}function Re(n,t,r,e){if(!Xi(n))return n;for(var u=-1,i=(t=Ke(t,n)).length,o=i-1,f=n;null!=f&&++u<i;){var c=mi(t[u]),a=r;if(u!=o){var l=f[c];(a=e?e(l,c,f):X)===X&&(a=Xi(l)?l:ei(t[u+1])?[]:{})}ur(f,c,a),f=f[c]}return n}function ze(n){return wi(yo(n))}function Ee(n,t,r){var e=-1,u=n.length;t<0&&(t=-t>u?0:u+t),(r=r>u?u:r)<0&&(r+=u),u=t>r?0:r-t>>>0,t>>>=0;for(var i=Eo(u);++e<u;)i[e]=n[e+t];return i}function Se(n,t){var r;return Pf(n,function(n,e,u){return!(r=t(n,e,u))}),!!r}function Le(n,t,r){var e=0,u=null==n?e:n.length;if("number"==typeof t&&t===t&&u<=Cn){for(;e<u;){var i=e+u>>>1,o=n[i];null!==o&&!uo(o)&&(r?o<=t:o<t)?e=i+1:u=i}return u}return We(n,t,jo,r)}function We(n,t,r,e){t=r(t);for(var u=0,i=null==n?0:n.length,o=t!==t,f=null===t,c=uo(t),a=t===X;u<i;){var l=_f((u+i)/2),s=r(n[l]),h=s!==X,p=null===s,_=s===s,v=uo(s);if(o)var g=e||_;else g=a?_&&(e||h):f?_&&h&&(e||!p):c?_&&h&&!p&&(e||!v):!p&&!v&&(e?s<=t:s<t);g?u=l+1:i=l}return mf(i,Wn)}function Ce(n,t){for(var r=-1,e=n.length,u=0,i=[];++r<e;){var o=n[r],f=t?t(o):o;if(!r||!Ki(f,c)){var c=f;i[u++]=0===o?0:o}}return i}function Ue(n){return"number"==typeof n?n:uo(n)?Sn:+n}function Be(n){if("string"==typeof n)return n;if(Zc(n))return l(n,Be)+"";if(uo(n))return Ff?Ff.call(n):"";var t=n+"";return"0"==t&&1/n==-Rn?"-0":t}function Te(n,t,r){var e=-1,u=c,i=n.length,o=!0,f=[],l=f;if(r)o=!1,u=a;else if(i>=nn){var s=t?null:Yf(n);if(s)return q(s);o=!1,u=L,l=new Yt}else l=t?[]:f;n:for(;++e<i;){var h=n[e],p=t?t(h):h;if(h=r||0!==h?h:0,o&&p===p){for(var _=l.length;_--;)if(l[_]===p)continue n;t&&l.push(p),f.push(h)}else u(l,p,r)||(l!==f&&l.push(p),f.push(h))}return f}function $e(n,t){return t=Ke(t,n),null==(n=gi(n,t))||delete n[mi(zi(t))]}function De(n,t,r,e){return Re(n,t,r(Zr(n,t)),e)}function Me(n,t,r,e){for(var u=n.length,i=e?u:-1;(e?i--:++i<u)&&t(n[i],i,n););return r?Ee(n,e?0:i,e?i+1:u):Ee(n,e?i+1:0,e?u:i)}function Fe(n,t){var r=n;return r instanceof Bt&&(r=r.value()),h(t,function(n,t){return t.func.apply(t.thisArg,s([n],t.args))},r)}function Ne(n,t,r){var e=n.length;if(e<2)return e?Te(n[0]):[];for(var u=-1,i=Eo(e);++u<e;)for(var o=n[u],f=-1;++f<e;)f!=u&&(i[u]=kr(i[u]||o,n[f],t,r));return Te(Fr(i,1),t,r)}function Pe(n,t,r){for(var e=-1,u=n.length,i=t.length,o={};++e<u;){var f=e<i?t[e]:X;r(o,n[e],f)}return o}function qe(n){return Gi(n)?n:[]}function Ze(n){return"function"==typeof n?n:jo}function Ke(n,t){return Zc(n)?n:ii(n,t)?[n]:oc(so(n))}function Ve(n,t,r){var e=n.length;return r=r===X?e:r,!t&&r>=e?n:Ee(n,t,r)}function Ge(n,t){if(t)return n.slice();var r=n.length,e=nf?nf(r):new n.constructor(r);return n.copy(e),e}function He(n){var t=new n.constructor(n.byteLength);return new Xo(t).set(new Xo(n)),t}function Je(n,t){var r=t?He(n.buffer):n.buffer;return new n.constructor(r,n.byteOffset,n.byteLength)}function Ye(t,r,e){return h(r?e(F(t),fn):F(t),n,new t.constructor)}function Qe(n){var t=new n.constructor(n.source,Dt.exec(n));return t.lastIndex=n.lastIndex,t}function Xe(n,r,e){return h(r?e(q(n),fn):q(n),t,new n.constructor)}function nu(n){return Mf?Uo(Mf.call(n)):{}}function tu(n,t){var r=t?He(n.buffer):n.buffer;return new n.constructor(r,n.byteOffset,n.length)}function ru(n,t){if(n!==t){var r=n!==X,e=null===n,u=n===n,i=uo(n),o=t!==X,f=null===t,c=t===t,a=uo(t);if(!f&&!a&&!i&&n>t||i&&o&&c&&!f&&!a||e&&o&&c||!r&&c||!u)return 1;if(!e&&!i&&!a&&n<t||a&&r&&u&&!e&&!i||f&&r&&u||!o&&u||!c)return-1}return 0}function eu(n,t,r){for(var e=-1,u=n.criteria,i=t.criteria,o=u.length,f=r.length;++e<o;){var c=ru(u[e],i[e]);if(c)return e>=f?c:c*("desc"==r[e]?-1:1)}return n.index-t.index}function uu(n,t,r,e){for(var u=-1,i=n.length,o=r.length,f=-1,c=t.length,a=wf(i-o,0),l=Eo(c+a),s=!e;++f<c;)l[f]=t[f];for(;++u<o;)(s||u<i)&&(l[r[u]]=n[u]);for(;a--;)l[f++]=n[u++];return l}function iu(n,t,r,e){for(var u=-1,i=n.length,o=-1,f=r.length,c=-1,a=t.length,l=wf(i-f,0),s=Eo(l+a),h=!e;++u<l;)s[u]=n[u];for(var p=u;++c<a;)s[p+c]=t[c];for(;++o<f;)(h||u<i)&&(s[p+r[o]]=n[u++]);return s}function ou(n,t){var r=-1,e=n.length;for(t||(t=Eo(e));++r<e;)t[r]=n[r];return t}function fu(n,t,r,e){var u=!r;r||(r={});for(var i=-1,o=t.length;++i<o;){var f=t[i],c=e?e(r[f],n[f],f,r,n):X;c===X&&(c=n[f]),u?sr(r,f,c):ur(r,f,c)}return r}function cu(n,t){return fu(n,Xf(n),t)}function au(n,t){return fu(n,nc(n),t)}function lu(n,t){return function(r,u){var i=Zc(r)?e:or,o=t?t():{};return i(r,n,qu(u,2),o)}}function su(n){return ke(function(t,r){var e=-1,u=r.length,i=u>1?r[u-1]:X,o=u>2?r[2]:X;for(i=n.length>3&&"function"==typeof i?(u--,i):X,o&&ui(r[0],r[1],o)&&(i=u<3?X:i,u=1),t=Uo(t);++e<u;){var f=r[e];f&&n(t,f,e,i)}return t})}function hu(n,t){return function(r,e){if(null==r)return r;if(!Vi(r))return n(r,e);for(var u=r.length,i=t?u:-1,o=Uo(r);(t?i--:++i<u)&&!1!==e(o[i],i,o););return r}}function pu(n){return function(t,r,e){for(var u=-1,i=Uo(t),o=e(t),f=o.length;f--;){var c=o[n?f:++u];if(!1===r(i[c],c,i))break}return t}}function _u(n,t,r){function e(){return(this&&this!==Ar&&this instanceof e?i:n).apply(u?r:this,arguments)}var u=t&hn,i=yu(n);return e}function vu(n){return function(t){var r=$(t=so(t))?H(t):X,e=r?r[0]:t.charAt(0),u=r?Ve(r,1).join(""):t.slice(1);return e[n]()+u}}function gu(n){return function(t){return h(mo(wo(t).replace(ar,"")),n,"")}}function yu(n){return function(){var t=arguments;switch(t.length){case 0:return new n;case 1:return new n(t[0]);case 2:return new n(t[0],t[1]);case 3:return new n(t[0],t[1],t[2]);case 4:return new n(t[0],t[1],t[2],t[3]);case 5:return new n(t[0],t[1],t[2],t[3],t[4]);case 6:return new n(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new n(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var r=Nf(n.prototype),e=n.apply(r,t);return Xi(e)?e:r}}function du(n,t,e){function u(){for(var o=arguments.length,f=Eo(o),c=o,a=Pu(u);c--;)f[c]=arguments[c];var l=o<3&&f[0]!==a&&f[o-1]!==a?[]:P(f,a);return(o-=l.length)<e?zu(n,t,mu,u.placeholder,X,f,l,X,X,e-o):r(this&&this!==Ar&&this instanceof u?i:n,this,f)}var i=yu(n);return u}function bu(n){return function(t,r,e){var u=Uo(t);if(!Vi(t)){var i=qu(r,3);t=_o(t),r=function(n){return i(u[n],n,u)}}var o=n(t,r,e);return o>-1?u[i?t[o]:o]:X}}function wu(n){return Du(function(t){var r=t.length,e=r,u=Y.prototype.thru;for(n&&t.reverse();e--;){var i=t[e];if("function"!=typeof i)throw new $o(rn);if(u&&!o&&"wrapper"==Nu(i))var o=new Y([],!0)}for(e=o?e:r;++e<r;){var f=Nu(i=t[e]),c="wrapper"==f?Qf(i):X;o=c&&fi(c[0])&&c[1]==(bn|vn|yn|wn)&&!c[4].length&&1==c[9]?o[Nu(c[0])].apply(o,c[3]):1==i.length&&fi(i)?o[f]():o.thru(i)}return function(){var n=arguments,e=n[0];if(o&&1==n.length&&Zc(e))return o.plant(e).value();for(var u=0,i=r?t[u].apply(this,n):e;++u<r;)i=t[u].call(this,i);return i}})}function mu(n,t,r,e,u,i,o,f,c,a){function l(){for(var y=arguments.length,d=Eo(y),b=y;b--;)d[b]=arguments[b];if(_)var w=Pu(l),m=U(d,w);if(e&&(d=uu(d,e,u,_)),i&&(d=iu(d,i,o,_)),y-=m,_&&y<a){var x=P(d,w);return zu(n,t,mu,l.placeholder,r,d,x,f,c,a-y)}var j=h?r:this,A=p?j[n]:n;return y=d.length,f?d=yi(d,f):v&&y>1&&d.reverse(),s&&c<y&&(d.length=c),this&&this!==Ar&&this instanceof l&&(A=g||yu(A)),A.apply(j,d)}var s=t&bn,h=t&hn,p=t&pn,_=t&(vn|gn),v=t&mn,g=p?X:yu(n);return l}function xu(n,t){return function(r,e){return Xr(r,n,t(e),{})}}function ju(n,t){return function(r,e){var u;if(r===X&&e===X)return t;if(r!==X&&(u=r),e!==X){if(u===X)return e;"string"==typeof r||"string"==typeof e?(r=Be(r),e=Be(e)):(r=Ue(r),e=Ue(e)),u=n(r,e)}return u}}function Au(n){return Du(function(t){return t=l(t,E(qu())),ke(function(e){var u=this;return n(t,function(n){return r(n,u,e)})})})}function ku(n,t){var r=(t=t===X?" ":Be(t)).length;if(r<2)return r?Ae(t,n):t;var e=Ae(t,pf(n/G(t)));return $(t)?Ve(H(e),0,n).join(""):e.slice(0,n)}function Ou(n,t,e,u){function i(){for(var t=-1,c=arguments.length,a=-1,l=u.length,s=Eo(l+c),h=this&&this!==Ar&&this instanceof i?f:n;++a<l;)s[a]=u[a];for(;c--;)s[a++]=arguments[++t];return r(h,o?e:this,s)}var o=t&hn,f=yu(n);return i}function Iu(n){return function(t,r,e){return e&&"number"!=typeof e&&ui(t,r,e)&&(r=e=X),t=oo(t),r===X?(r=t,t=0):r=oo(r),e=e===X?t<r?1:-1:oo(e),je(t,r,e,n)}}function Ru(n){return function(t,r){return"string"==typeof t&&"string"==typeof r||(t=ao(t),r=ao(r)),n(t,r)}}function zu(n,t,r,e,u,i,o,f,c,a){var l=t&vn;t|=l?yn:dn,(t&=~(l?dn:yn))&_n||(t&=~(hn|pn));var s=[n,t,u,l?i:X,l?o:X,l?X:i,l?X:o,f,c,a],h=r.apply(X,s);return fi(n)&&ec(h,s),h.placeholder=e,di(h,n,t)}function Eu(n){var t=Co[n];return function(n,r){if(n=ao(n),r=null==r?0:mf(fo(r),292)){var e=(so(n)+"e").split("e");return+((e=(so(t(e[0]+"e"+(+e[1]+r)))+"e").split("e"))[0]+"e"+(+e[1]-r))}return t(n)}}function Su(n){return function(t){var r=tc(t);return r==Zn?F(t):r==Yn?Z(t):z(t,n(t))}}function Lu(n,t,r,e,u,i,o,f){var c=t&pn;if(!c&&"function"!=typeof n)throw new $o(rn);var a=e?e.length:0;if(a||(t&=~(yn|dn),e=u=X),o=o===X?o:wf(fo(o),0),f=f===X?f:fo(f),a-=u?u.length:0,t&dn){var l=e,s=u;e=u=X}var h=c?X:Qf(n),p=[n,t,r,e,u,l,s,i,o,f];if(h&&hi(p,h),n=p[0],t=p[1],r=p[2],e=p[3],u=p[4],!(f=p[9]=p[9]===X?c?0:n.length:wf(p[9]-a,0))&&t&(vn|gn)&&(t&=~(vn|gn)),t&&t!=hn)_=t==vn||t==gn?du(n,t,f):t!=yn&&t!=(hn|yn)||u.length?mu.apply(X,p):Ou(n,t,r,e);else var _=_u(n,t,r);return di((h?Vf:ec)(_,p),n,t)}function Wu(n,t,r,e){return n===X||Ki(n,Fo[r])&&!qo.call(e,r)?t:n}function Cu(n,t,r,e,u,i){return Xi(n)&&Xi(t)&&(i.set(t,n),pe(n,t,X,Cu,i),i.delete(t)),n}function Uu(n){return ro(n)?X:n}function Bu(n,t,r,e,u,i){var o=r&ln,f=n.length,c=t.length;if(f!=c&&!(o&&c>f))return!1;var a=i.get(n);if(a&&i.get(t))return a==t;var l=-1,s=!0,h=r&sn?new Yt:X;for(i.set(n,t),i.set(t,n);++l<f;){var p=n[l],v=t[l];if(e)var g=o?e(v,p,l,t,n,i):e(p,v,l,n,t,i);if(g!==X){if(g)continue;s=!1;break}if(h){if(!_(t,function(n,t){if(!L(h,t)&&(p===n||u(p,n,r,e,i)))return h.push(t)})){s=!1;break}}else if(p!==v&&!u(p,v,r,e,i)){s=!1;break}}return i.delete(n),i.delete(t),s}function Tu(n,t,r,e,u,i,o){switch(r){case ut:if(n.byteLength!=t.byteLength||n.byteOffset!=t.byteOffset)return!1;n=n.buffer,t=t.buffer;case et:return!(n.byteLength!=t.byteLength||!i(new Xo(n),new Xo(t)));case Dn:case Mn:case Kn:return Ki(+n,+t);case Nn:return n.name==t.name&&n.message==t.message;case Jn:case Qn:return n==t+"";case Zn:var f=F;case Yn:var c=e&ln;if(f||(f=q),n.size!=t.size&&!c)return!1;var a=o.get(n);if(a)return a==t;e|=sn,o.set(n,t);var l=Bu(f(n),f(t),e,u,i,o);return o.delete(n),l;case Xn:if(Mf)return Mf.call(n)==Mf.call(t)}return!1}function $u(n,t,r,e,u,i){var o=r&ln,f=Mu(n),c=f.length;if(c!=Mu(t).length&&!o)return!1;for(var a=c;a--;){var l=f[a];if(!(o?l in t:qo.call(t,l)))return!1}var s=i.get(n);if(s&&i.get(t))return s==t;var h=!0;i.set(n,t),i.set(t,n);for(var p=o;++a<c;){var _=n[l=f[a]],v=t[l];if(e)var g=o?e(v,_,l,t,n,i):e(_,v,l,n,t,i);if(!(g===X?_===v||u(_,v,r,e,i):g)){h=!1;break}p||(p="constructor"==l)}if(h&&!p){var y=n.constructor,d=t.constructor;y!=d&&"constructor"in n&&"constructor"in t&&!("function"==typeof y&&y instanceof y&&"function"==typeof d&&d instanceof d)&&(h=!1)}return i.delete(n),i.delete(t),h}function Du(n){return ic(vi(n,X,Ii),n+"")}function Mu(n){return Kr(n,_o,Xf)}function Fu(n){return Kr(n,vo,nc)}function Nu(n){for(var t=n.name+"",r=Wf[t],e=qo.call(Wf,t)?r.length:0;e--;){var u=r[e],i=u.func;if(null==i||i==n)return u.name}return t}function Pu(n){return(qo.call(K,"placeholder")?K:n).placeholder}function qu(){var n=K.iteratee||Ao;return n=n===Ao?oe:n,arguments.length?n(arguments[0],arguments[1]):n}function Zu(n,t){var r=n.__data__;return oi(t)?r["string"==typeof t?"string":"hash"]:r.map}function Ku(n){for(var t=_o(n),r=t.length;r--;){var e=t[r],u=n[e];t[r]=[e,u,li(u)]}return t}function Vu(n,t){var r=T(n,t);return ie(r)?r:X}function Gu(n){var t=qo.call(n,cf),r=n[cf];try{n[cf]=X;var e=!0}catch(n){}var u=Vo.call(n);return e&&(t?n[cf]=r:delete n[cf]),u}function Hu(n,t,r){for(var e=-1,u=r.length;++e<u;){var i=r[e],o=i.size;switch(i.type){case"drop":n+=o;break;case"dropRight":t-=o;break;case"take":t=mf(t,n+o);break;case"takeRight":n=wf(n,t-o)}}return{start:n,end:t}}function Ju(n){var t=n.match(Ct);return t?t[1].split(Ut):[]}function Yu(n,t,r){for(var e=-1,u=(t=Ke(t,n)).length,i=!1;++e<u;){var o=mi(t[e]);if(!(i=null!=n&&r(n,o)))break;n=n[o]}return i||++e!=u?i:!!(u=null==n?0:n.length)&&Qi(u)&&ei(o,u)&&(Zc(n)||qc(n))}function Qu(n){var t=n.length,r=n.constructor(t);return t&&"string"==typeof n[0]&&qo.call(n,"index")&&(r.index=n.index,r.input=n.input),r}function Xu(n){return"function"!=typeof n.constructor||ai(n)?{}:Nf(tf(n))}function ni(n,t,r,e){var u=n.constructor;switch(t){case et:return He(n);case Dn:case Mn:return new u(+n);case ut:return Je(n,e);case it:case ot:case ft:case ct:case at:case lt:case st:case ht:case pt:return tu(n,e);case Zn:return Ye(n,e,r);case Kn:case Qn:return new u(n);case Jn:return Qe(n);case Yn:return Xe(n,e,r);case Xn:return nu(n)}}function ti(n,t){var r=t.length;if(!r)return n;var e=r-1;return t[e]=(r>1?"& ":"")+t[e],t=t.join(r>2?", ":" "),n.replace(Wt,"{\n/* [wrapped with "+t+"] */\n")}function ri(n){return Zc(n)||qc(n)||!!(of&&n&&n[of])}function ei(n,t){return!!(t=null==t?zn:t)&&("number"==typeof n||qt.test(n))&&n>-1&&n%1==0&&n<t}function ui(n,t,r){if(!Xi(r))return!1;var e=typeof t;return!!("number"==e?Vi(r)&&ei(t,r.length):"string"==e&&t in r)&&Ki(r[t],n)}function ii(n,t){if(Zc(n))return!1;var r=typeof n;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=n&&!uo(n))||kt.test(n)||!At.test(n)||null!=t&&n in Uo(t)}function oi(n){var t=typeof n;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==n:null===n}function fi(n){var t=Nu(n),r=K[t];if("function"!=typeof r||!(t in Bt.prototype))return!1;if(n===r)return!0;var e=Qf(r);return!!e&&n===e[0]}function ci(n){return!!Ko&&Ko in n}function ai(n){var t=n&&n.constructor;return n===("function"==typeof t&&t.prototype||Fo)}function li(n){return n===n&&!Xi(n)}function si(n,t){return function(r){return null!=r&&r[n]===t&&(t!==X||n in Uo(r))}}function hi(n,t){var r=n[1],e=t[1],u=r|e,i=u<(hn|pn|bn),o=e==bn&&r==vn||e==bn&&r==wn&&n[7].length<=t[8]||e==(bn|wn)&&t[7].length<=t[8]&&r==vn;if(!i&&!o)return n;e&hn&&(n[2]=t[2],u|=r&hn?0:_n);var f=t[3];if(f){var c=n[3];n[3]=c?uu(c,f,t[4]):f,n[4]=c?P(n[3],on):t[4]}return(f=t[5])&&(c=n[5],n[5]=c?iu(c,f,t[6]):f,n[6]=c?P(n[5],on):t[6]),(f=t[7])&&(n[7]=f),e&bn&&(n[8]=null==n[8]?t[8]:mf(n[8],t[8])),null==n[9]&&(n[9]=t[9]),n[0]=t[0],n[1]=u,n}function pi(n){var t=[];if(null!=n)for(var r in Uo(n))t.push(r);return t}function _i(n){return Vo.call(n)}function vi(n,t,e){return t=wf(t===X?n.length-1:t,0),function(){for(var u=arguments,i=-1,o=wf(u.length-t,0),f=Eo(o);++i<o;)f[i]=u[t+i];i=-1;for(var c=Eo(t+1);++i<t;)c[i]=u[i];return c[t]=e(f),r(n,this,c)}}function gi(n,t){return t.length<2?n:Zr(n,Ee(t,0,-1))}function yi(n,t){for(var r=n.length,e=mf(t.length,r),u=ou(n);e--;){var i=t[e];n[e]=ei(i,r)?u[i]:X}return n}function di(n,t,r){var e=t+"";return ic(n,ti(e,ji(Ju(e),r)))}function bi(n){var t=0,r=0;return function(){var e=xf(),u=kn-(e-r);if(r=e,u>0){if(++t>=An)return arguments[0]}else t=0;return n.apply(X,arguments)}}function wi(n,t){var r=-1,e=n.length,u=e-1;for(t=t===X?e:t;++r<t;){var i=xe(r,u),o=n[i];n[i]=n[r],n[r]=o}return n.length=t,n}function mi(n){if("string"==typeof n||uo(n))return n;var t=n+"";return"0"==t&&1/n==-Rn?"-0":t}function xi(n){if(null!=n){try{return Po.call(n)}catch(n){}try{return n+""}catch(n){}}return""}function ji(n,t){return u(Un,function(r){var e="_."+r[0];t&r[1]&&!c(n,e)&&n.push(e)}),n.sort()}function Ai(n){if(n instanceof Bt)return n.clone();var t=new Y(n.__wrapped__,n.__chain__);return t.__actions__=ou(n.__actions__),t.__index__=n.__index__,t.__values__=n.__values__,t}function ki(n,t,r){var e=null==n?0:n.length;if(!e)return-1;var u=null==r?0:fo(r);return u<0&&(u=wf(e+u,0)),d(n,qu(t,3),u)}function Oi(n,t,r){var e=null==n?0:n.length;if(!e)return-1;var u=e-1;return r!==X&&(u=fo(r),u=r<0?wf(e+u,0):mf(u,e-1)),d(n,qu(t,3),u,!0)}function Ii(n){return(null==n?0:n.length)?Fr(n,1):[]}function Ri(n){return n&&n.length?n[0]:X}function zi(n){var t=null==n?0:n.length;return t?n[t-1]:X}function Ei(n,t){return n&&n.length&&t&&t.length?we(n,t):n}function Si(n){return null==n?n:kf.call(n)}function Li(n){if(!n||!n.length)return[];var t=0;return n=f(n,function(n){if(Gi(n))return t=wf(n.length,t),!0}),R(t,function(t){return l(n,j(t))})}function Wi(n,t){if(!n||!n.length)return[];var e=Li(n);return null==t?e:l(e,function(n){return r(t,X,n)})}function Ci(n){var t=K(n);return t.__chain__=!0,t}function Ui(n,t){return t(n)}function Bi(n,t){return(Zc(n)?u:Pf)(n,qu(t,3))}function Ti(n,t){return(Zc(n)?i:qf)(n,qu(t,3))}function $i(n,t){return(Zc(n)?l:le)(n,qu(t,3))}function Di(n,t,r){return t=r?X:t,t=n&&null==t?n.length:t,Lu(n,bn,X,X,X,X,t)}function Mi(n,t){var r;if("function"!=typeof t)throw new $o(rn);return n=fo(n),function(){return--n>0&&(r=t.apply(this,arguments)),n<=1&&(t=X),r}}function Fi(n,t,r){var e=Lu(n,vn,X,X,X,X,X,t=r?X:t);return e.placeholder=Fi.placeholder,e}function Ni(n,t,r){var e=Lu(n,gn,X,X,X,X,X,t=r?X:t);return e.placeholder=Ni.placeholder,e}function Pi(n,t,r){function e(t){var r=l,e=s;return l=s=X,g=t,p=n.apply(e,r)}function u(n){return g=n,_=uc(f,t),y?e(n):p}function i(n){var r=t-(n-v);return d?mf(r,h-(n-g)):r}function o(n){var r=n-v;return v===X||r>=t||r<0||d&&n-g>=h}function f(){var n=Wc();if(o(n))return c(n);_=uc(f,i(n))}function c(n){return _=X,b&&l?e(n):(l=s=X,p)}function a(){var n=Wc(),r=o(n);if(l=arguments,s=this,v=n,r){if(_===X)return u(v);if(d)return _=uc(f,t),e(v)}return _===X&&(_=uc(f,t)),p}var l,s,h,p,_,v,g=0,y=!1,d=!1,b=!0;if("function"!=typeof n)throw new $o(rn);return t=ao(t)||0,Xi(r)&&(y=!!r.leading,h=(d="maxWait"in r)?wf(ao(r.maxWait)||0,t):h,b="trailing"in r?!!r.trailing:b),a.cancel=function(){_!==X&&Jf(_),g=0,l=v=s=_=X},a.flush=function(){return _===X?p:c(Wc())},a}function qi(n,t){if("function"!=typeof n||null!=t&&"function"!=typeof t)throw new $o(rn);var r=function(){var e=arguments,u=t?t.apply(this,e):e[0],i=r.cache;if(i.has(u))return i.get(u);var o=n.apply(this,e);return r.cache=i.set(u,o)||i,o};return r.cache=new(qi.Cache||Jt),r}function Zi(n){if("function"!=typeof n)throw new $o(rn);return function(){var t=arguments;switch(t.length){case 0:return!n.call(this);case 1:return!n.call(this,t[0]);case 2:return!n.call(this,t[0],t[1]);case 3:return!n.call(this,t[0],t[1],t[2])}return!n.apply(this,t)}}function Ki(n,t){return n===t||n!==n&&t!==t}function Vi(n){return null!=n&&Qi(n.length)&&!Ji(n)}function Gi(n){return no(n)&&Vi(n)}function Hi(n){if(!no(n))return!1;var t=Vr(n);return t==Nn||t==Fn||"string"==typeof n.message&&"string"==typeof n.name&&!ro(n)}function Ji(n){if(!Xi(n))return!1;var t=Vr(n);return t==Pn||t==qn||t==$n||t==Hn}function Yi(n){return"number"==typeof n&&n==fo(n)}function Qi(n){return"number"==typeof n&&n>-1&&n%1==0&&n<=zn}function Xi(n){var t=typeof n;return null!=n&&("object"==t||"function"==t)}function no(n){return null!=n&&"object"==typeof n}function to(n){return"number"==typeof n||no(n)&&Vr(n)==Kn}function ro(n){if(!no(n)||Vr(n)!=Gn)return!1;var t=tf(n);if(null===t)return!0;var r=qo.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&Po.call(r)==Go}function eo(n){return"string"==typeof n||!Zc(n)&&no(n)&&Vr(n)==Qn}function uo(n){return"symbol"==typeof n||no(n)&&Vr(n)==Xn}function io(n){if(!n)return[];if(Vi(n))return eo(n)?H(n):ou(n);if(ff&&n[ff])return M(n[ff]());var t=tc(n);return(t==Zn?F:t==Yn?q:yo)(n)}function oo(n){return n?(n=ao(n))===Rn||n===-Rn?(n<0?-1:1)*En:n===n?n:0:0===n?n:0}function fo(n){var t=oo(n),r=t%1;return t===t?r?t-r:t:0}function co(n){return n?pr(fo(n),0,Ln):0}function ao(n){if("number"==typeof n)return n;if(uo(n))return Sn;if(Xi(n)){var t="function"==typeof n.valueOf?n.valueOf():n;n=Xi(t)?t+"":t}if("string"!=typeof n)return 0===n?n:+n;n=n.replace(Et,"");var r=Ft.test(n);return r||Pt.test(n)?mr(n.slice(2),r?2:8):Mt.test(n)?Sn:+n}function lo(n){return fu(n,vo(n))}function so(n){return null==n?"":Be(n)}function ho(n,t,r){var e=null==n?X:Zr(n,t);return e===X?r:e}function po(n,t){return null!=n&&Yu(n,t,Jr)}function _o(n){return Vi(n)?Xt(n):fe(n)}function vo(n){return Vi(n)?Xt(n,!0):ce(n)}function go(n,t){if(null==n)return{};var r=l(Fu(n),function(n){return[n]});return t=qu(t),de(n,r,function(n,r){return t(n,r[0])})}function yo(n){return null==n?[]:S(n,_o(n))}function bo(n){return Aa(so(n).toLowerCase())}function wo(n){return(n=so(n))&&n.replace(Zt,Tr).replace(lr,"")}function mo(n,t,r){return n=so(n),(t=r?X:t)===X?D(n)?Q(n):g(n):n.match(t)||[]}function xo(n){return function(){return n}}function jo(n){return n}function Ao(n){return oe("function"==typeof n?n:_r(n,fn))}function ko(n,t,r){var e=_o(t),i=qr(t,e);null!=r||Xi(t)&&(i.length||!e.length)||(r=t,t=n,n=this,i=qr(t,_o(t)));var o=!(Xi(r)&&"chain"in r&&!r.chain),f=Ji(n);return u(i,function(r){var e=t[r];n[r]=e,f&&(n.prototype[r]=function(){var t=this.__chain__;if(o||t){var r=n(this.__wrapped__);return(r.__actions__=ou(this.__actions__)).push({func:e,args:arguments,thisArg:n}),r.__chain__=t,r}return e.apply(n,s([this.value()],arguments))})}),n}function Oo(){}function Io(n){return ii(n)?j(mi(n)):be(n)}function Ro(){return[]}function zo(){return!1}var Eo=(A=null==A?Ar:Mr.defaults(Ar.Object(),A,Mr.pick(Ar,vr))).Array,So=A.Date,Lo=A.Error,Wo=A.Function,Co=A.Math,Uo=A.Object,Bo=A.RegExp,To=A.String,$o=A.TypeError,Do=Eo.prototype,Mo=Wo.prototype,Fo=Uo.prototype,No=A["__core-js_shared__"],Po=Mo.toString,qo=Fo.hasOwnProperty,Zo=0,Ko=function(){var n=/[^.]+$/.exec(No&&No.keys&&No.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}(),Vo=Fo.toString,Go=Po.call(Uo),Ho=Ar._,Jo=Bo("^"+Po.call(qo).replace(Rt,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Yo=Ir?A.Buffer:X,Qo=A.Symbol,Xo=A.Uint8Array,nf=Yo?Yo.allocUnsafe:X,tf=N(Uo.getPrototypeOf,Uo),rf=Uo.create,ef=Fo.propertyIsEnumerable,uf=Do.splice,of=Qo?Qo.isConcatSpreadable:X,ff=Qo?Qo.iterator:X,cf=Qo?Qo.toStringTag:X,af=function(){try{var n=Vu(Uo,"defineProperty");return n({},"",{}),n}catch(n){}}(),lf=A.clearTimeout!==Ar.clearTimeout&&A.clearTimeout,sf=So&&So.now!==Ar.Date.now&&So.now,hf=A.setTimeout!==Ar.setTimeout&&A.setTimeout,pf=Co.ceil,_f=Co.floor,vf=Uo.getOwnPropertySymbols,gf=Yo?Yo.isBuffer:X,yf=A.isFinite,df=Do.join,bf=N(Uo.keys,Uo),wf=Co.max,mf=Co.min,xf=So.now,jf=A.parseInt,Af=Co.random,kf=Do.reverse,Of=Vu(A,"DataView"),If=Vu(A,"Map"),Rf=Vu(A,"Promise"),zf=Vu(A,"Set"),Ef=Vu(A,"WeakMap"),Sf=Vu(Uo,"create"),Lf=Ef&&new Ef,Wf={},Cf=xi(Of),Uf=xi(If),Bf=xi(Rf),Tf=xi(zf),$f=xi(Ef),Df=Qo?Qo.prototype:X,Mf=Df?Df.valueOf:X,Ff=Df?Df.toString:X,Nf=function(){function n(){}return function(t){if(!Xi(t))return{};if(rf)return rf(t);n.prototype=t;var r=new n;return n.prototype=X,r}}();K.templateSettings={escape:mt,evaluate:xt,interpolate:jt,variable:"",imports:{_:K}},K.prototype=J.prototype,K.prototype.constructor=K,Y.prototype=Nf(J.prototype),Y.prototype.constructor=Y,Bt.prototype=Nf(J.prototype),Bt.prototype.constructor=Bt,Gt.prototype.clear=function(){this.__data__=Sf?Sf(null):{},this.size=0},Gt.prototype.delete=function(n){var t=this.has(n)&&delete this.__data__[n];return this.size-=t?1:0,t},Gt.prototype.get=function(n){var t=this.__data__;if(Sf){var r=t[n];return r===en?X:r}return qo.call(t,n)?t[n]:X},Gt.prototype.has=function(n){var t=this.__data__;return Sf?t[n]!==X:qo.call(t,n)},Gt.prototype.set=function(n,t){var r=this.__data__;return this.size+=this.has(n)?0:1,r[n]=Sf&&t===X?en:t,this},Ht.prototype.clear=function(){this.__data__=[],this.size=0},Ht.prototype.delete=function(n){var t=this.__data__,r=ir(t,n);return!(r<0||(r==t.length-1?t.pop():uf.call(t,r,1),--this.size,0))},Ht.prototype.get=function(n){var t=this.__data__,r=ir(t,n);return r<0?X:t[r][1]},Ht.prototype.has=function(n){return ir(this.__data__,n)>-1},Ht.prototype.set=function(n,t){var r=this.__data__,e=ir(r,n);return e<0?(++this.size,r.push([n,t])):r[e][1]=t,this},Jt.prototype.clear=function(){this.size=0,this.__data__={hash:new Gt,map:new(If||Ht),string:new Gt}},Jt.prototype.delete=function(n){var t=Zu(this,n).delete(n);return this.size-=t?1:0,t},Jt.prototype.get=function(n){return Zu(this,n).get(n)},Jt.prototype.has=function(n){return Zu(this,n).has(n)},Jt.prototype.set=function(n,t){var r=Zu(this,n),e=r.size;return r.set(n,t),this.size+=r.size==e?0:1,this},Yt.prototype.add=Yt.prototype.push=function(n){return this.__data__.set(n,en),this},Yt.prototype.has=function(n){return this.__data__.has(n)},Qt.prototype.clear=function(){this.__data__=new Ht,this.size=0},Qt.prototype.delete=function(n){var t=this.__data__,r=t.delete(n);return this.size=t.size,r},Qt.prototype.get=function(n){return this.__data__.get(n)},Qt.prototype.has=function(n){return this.__data__.has(n)},Qt.prototype.set=function(n,t){var r=this.__data__;if(r instanceof Ht){var e=r.__data__;if(!If||e.length<nn-1)return e.push([n,t]),this.size=++r.size,this;r=this.__data__=new Jt(e)}return r.set(n,t),this.size=r.size,this};var Pf=hu(Nr),qf=hu(Pr,!0),Zf=pu(),Kf=pu(!0),Vf=Lf?function(n,t){return Lf.set(n,t),n}:jo,Gf=af?function(n,t){return af(n,"toString",{configurable:!0,enumerable:!1,value:xo(t),writable:!0})}:jo,Hf=ke,Jf=lf||function(n){return Ar.clearTimeout(n)},Yf=zf&&1/q(new zf([,-0]))[1]==Rn?function(n){return new zf(n)}:Oo,Qf=Lf?function(n){return Lf.get(n)}:Oo,Xf=vf?function(n){return null==n?[]:(n=Uo(n),f(vf(n),function(t){return ef.call(n,t)}))}:Ro,nc=vf?function(n){for(var t=[];n;)s(t,Xf(n)),n=tf(n);return t}:Ro,tc=Vr;(Of&&tc(new Of(new ArrayBuffer(1)))!=ut||If&&tc(new If)!=Zn||Rf&&"[object Promise]"!=tc(Rf.resolve())||zf&&tc(new zf)!=Yn||Ef&&tc(new Ef)!=tt)&&(tc=function(n){var t=Vr(n),r=t==Gn?n.constructor:X,e=r?xi(r):"";if(e)switch(e){case Cf:return ut;case Uf:return Zn;case Bf:return"[object Promise]";case Tf:return Yn;case $f:return tt}return t});var rc=No?Ji:zo,ec=bi(Vf),uc=hf||function(n,t){return Ar.setTimeout(n,t)},ic=bi(Gf),oc=function(n){var t=qi(n,function(n){return r.size===un&&r.clear(),n}),r=t.cache;return t}(function(n){var t=[];return Ot.test(n)&&t.push(""),n.replace(It,function(n,r,e,u){t.push(e?u.replace(Tt,"$1"):r||n)}),t}),fc=ke(function(n,t){return Gi(n)?kr(n,Fr(t,1,Gi,!0)):[]}),cc=ke(function(n,t){var r=zi(t);return Gi(r)&&(r=X),Gi(n)?kr(n,Fr(t,1,Gi,!0),qu(r,2)):[]}),ac=ke(function(n,t){var r=zi(t);return Gi(r)&&(r=X),Gi(n)?kr(n,Fr(t,1,Gi,!0),X,r):[]}),lc=ke(function(n){var t=l(n,qe);return t.length&&t[0]===n[0]?Qr(t):[]}),sc=ke(function(n){var t=zi(n),r=l(n,qe);return t===zi(r)?t=X:r.pop(),r.length&&r[0]===n[0]?Qr(r,qu(t,2)):[]}),hc=ke(function(n){var t=zi(n),r=l(n,qe);return(t="function"==typeof t?t:X)&&r.pop(),r.length&&r[0]===n[0]?Qr(r,X,t):[]}),pc=ke(Ei),_c=Du(function(n,t){var r=null==n?0:n.length,e=hr(n,t);return me(n,l(t,function(n){return ei(n,r)?+n:n}).sort(ru)),e}),vc=ke(function(n){return Te(Fr(n,1,Gi,!0))}),gc=ke(function(n){var t=zi(n);return Gi(t)&&(t=X),Te(Fr(n,1,Gi,!0),qu(t,2))}),yc=ke(function(n){var t=zi(n);return t="function"==typeof t?t:X,Te(Fr(n,1,Gi,!0),X,t)}),dc=ke(function(n,t){return Gi(n)?kr(n,t):[]}),bc=ke(function(n){return Ne(f(n,Gi))}),wc=ke(function(n){var t=zi(n);return Gi(t)&&(t=X),Ne(f(n,Gi),qu(t,2))}),mc=ke(function(n){var t=zi(n);return t="function"==typeof t?t:X,Ne(f(n,Gi),X,t)}),xc=ke(Li),jc=ke(function(n){var t=n.length,r=t>1?n[t-1]:X;return r="function"==typeof r?(n.pop(),r):X,Wi(n,r)}),Ac=Du(function(n){var t=n.length,r=t?n[0]:0,e=this.__wrapped__,u=function(t){return hr(t,n)};return!(t>1||this.__actions__.length)&&e instanceof Bt&&ei(r)?((e=e.slice(r,+r+(t?1:0))).__actions__.push({func:Ui,args:[u],thisArg:X}),new Y(e,this.__chain__).thru(function(n){return t&&!n.length&&n.push(X),n})):this.thru(u)}),kc=lu(function(n,t,r){qo.call(n,r)?++n[r]:sr(n,r,1)}),Oc=bu(ki),Ic=bu(Oi),Rc=lu(function(n,t,r){qo.call(n,r)?n[r].push(t):sr(n,r,[t])}),zc=ke(function(n,t,e){var u=-1,i="function"==typeof t,o=Vi(n)?Eo(n.length):[];return Pf(n,function(n){o[++u]=i?r(t,n,e):ne(n,t,e)}),o}),Ec=lu(function(n,t,r){sr(n,r,t)}),Sc=lu(function(n,t,r){n[r?0:1].push(t)},function(){return[[],[]]}),Lc=ke(function(n,t){if(null==n)return[];var r=t.length;return r>1&&ui(n,t[0],t[1])?t=[]:r>2&&ui(t[0],t[1],t[2])&&(t=[t[0]]),ge(n,Fr(t,1),[])}),Wc=sf||function(){return Ar.Date.now()},Cc=ke(function(n,t,r){var e=hn;if(r.length){var u=P(r,Pu(Cc));e|=yn}return Lu(n,e,t,r,u)}),Uc=ke(function(n,t,r){var e=hn|pn;if(r.length){var u=P(r,Pu(Uc));e|=yn}return Lu(t,e,n,r,u)}),Bc=ke(function(n,t){return jr(n,1,t)}),Tc=ke(function(n,t,r){return jr(n,ao(t)||0,r)});qi.Cache=Jt;var $c=Hf(function(n,t){var e=(t=1==t.length&&Zc(t[0])?l(t[0],E(qu())):l(Fr(t,1),E(qu()))).length;return ke(function(u){for(var i=-1,o=mf(u.length,e);++i<o;)u[i]=t[i].call(this,u[i]);return r(n,this,u)})}),Dc=ke(function(n,t){var r=P(t,Pu(Dc));return Lu(n,yn,X,t,r)}),Mc=ke(function(n,t){var r=P(t,Pu(Mc));return Lu(n,dn,X,t,r)}),Fc=Du(function(n,t){return Lu(n,wn,X,X,X,t)}),Nc=Ru(Gr),Pc=Ru(function(n,t){return n>=t}),qc=te(function(){return arguments}())?te:function(n){return no(n)&&qo.call(n,"callee")&&!ef.call(n,"callee")},Zc=Eo.isArray,Kc=Er?E(Er):function(n){return no(n)&&Vr(n)==et},Vc=gf||zo,Gc=Sr?E(Sr):function(n){return no(n)&&Vr(n)==Mn},Hc=Lr?E(Lr):function(n){return no(n)&&tc(n)==Zn},Jc=Wr?E(Wr):function(n){return no(n)&&Vr(n)==Jn},Yc=Cr?E(Cr):function(n){return no(n)&&tc(n)==Yn},Qc=Ur?E(Ur):function(n){return no(n)&&Qi(n.length)&&!!yr[Vr(n)]},Xc=Ru(ae),na=Ru(function(n,t){return n<=t}),ta=su(function(n,t){if(ai(t)||Vi(t))fu(t,_o(t),n);else for(var r in t)qo.call(t,r)&&ur(n,r,t[r])}),ra=su(function(n,t){fu(t,vo(t),n)}),ea=su(function(n,t,r,e){fu(t,vo(t),n,e)}),ua=su(function(n,t,r,e){fu(t,_o(t),n,e)}),ia=Du(hr),oa=ke(function(n){return n.push(X,Wu),r(ea,X,n)}),fa=ke(function(n){return n.push(X,Cu),r(ha,X,n)}),ca=xu(function(n,t,r){n[t]=r},xo(jo)),aa=xu(function(n,t,r){qo.call(n,t)?n[t].push(r):n[t]=[r]},qu),la=ke(ne),sa=su(function(n,t,r){pe(n,t,r)}),ha=su(function(n,t,r,e){pe(n,t,r,e)}),pa=Du(function(n,t){var r={};if(null==n)return r;var e=!1;t=l(t,function(t){return t=Ke(t,n),e||(e=t.length>1),t}),fu(n,Fu(n),r),e&&(r=_r(r,fn|cn|an,Uu));for(var u=t.length;u--;)$e(r,t[u]);return r}),_a=Du(function(n,t){return null==n?{}:ye(n,t)}),va=Su(_o),ga=Su(vo),ya=gu(function(n,t,r){return t=t.toLowerCase(),n+(r?bo(t):t)}),da=gu(function(n,t,r){return n+(r?"-":"")+t.toLowerCase()}),ba=gu(function(n,t,r){return n+(r?" ":"")+t.toLowerCase()}),wa=vu("toLowerCase"),ma=gu(function(n,t,r){return n+(r?"_":"")+t.toLowerCase()}),xa=gu(function(n,t,r){return n+(r?" ":"")+Aa(t)}),ja=gu(function(n,t,r){return n+(r?" ":"")+t.toUpperCase()}),Aa=vu("toUpperCase"),ka=ke(function(n,t){try{return r(n,X,t)}catch(n){return Hi(n)?n:new Lo(n)}}),Oa=Du(function(n,t){return u(t,function(t){t=mi(t),sr(n,t,Cc(n[t],n))}),n}),Ia=wu(),Ra=wu(!0),za=ke(function(n,t){return function(r){return ne(r,n,t)}}),Ea=ke(function(n,t){return function(r){return ne(n,r,t)}}),Sa=Au(l),La=Au(o),Wa=Au(_),Ca=Iu(),Ua=Iu(!0),Ba=ju(function(n,t){return n+t},0),Ta=Eu("ceil"),$a=ju(function(n,t){return n/t},1),Da=Eu("floor"),Ma=ju(function(n,t){return n*t},1),Fa=Eu("round"),Na=ju(function(n,t){return n-t},0);return K.after=function(n,t){if("function"!=typeof t)throw new $o(rn);return n=fo(n),function(){if(--n<1)return t.apply(this,arguments)}},K.ary=Di,K.assign=ta,K.assignIn=ra,K.assignInWith=ea,K.assignWith=ua,K.at=ia,K.before=Mi,K.bind=Cc,K.bindAll=Oa,K.bindKey=Uc,K.castArray=function(){if(!arguments.length)return[];var n=arguments[0];return Zc(n)?n:[n]},K.chain=Ci,K.chunk=function(n,t,r){t=(r?ui(n,t,r):t===X)?1:wf(fo(t),0);var e=null==n?0:n.length;if(!e||t<1)return[];for(var u=0,i=0,o=Eo(pf(e/t));u<e;)o[i++]=Ee(n,u,u+=t);return o},K.compact=function(n){for(var t=-1,r=null==n?0:n.length,e=0,u=[];++t<r;){var i=n[t];i&&(u[e++]=i)}return u},K.concat=function(){var n=arguments.length;if(!n)return[];for(var t=Eo(n-1),r=arguments[0],e=n;e--;)t[e-1]=arguments[e];return s(Zc(r)?ou(r):[r],Fr(t,1))},K.cond=function(n){var t=null==n?0:n.length,e=qu();return n=t?l(n,function(n){if("function"!=typeof n[1])throw new $o(rn);return[e(n[0]),n[1]]}):[],ke(function(e){for(var u=-1;++u<t;){var i=n[u];if(r(i[0],this,e))return r(i[1],this,e)}})},K.conforms=function(n){return br(_r(n,fn))},K.constant=xo,K.countBy=kc,K.create=function(n,t){var r=Nf(n);return null==t?r:fr(r,t)},K.curry=Fi,K.curryRight=Ni,K.debounce=Pi,K.defaults=oa,K.defaultsDeep=fa,K.defer=Bc,K.delay=Tc,K.difference=fc,K.differenceBy=cc,K.differenceWith=ac,K.drop=function(n,t,r){var e=null==n?0:n.length;return e?(t=r||t===X?1:fo(t),Ee(n,t<0?0:t,e)):[]},K.dropRight=function(n,t,r){var e=null==n?0:n.length;return e?(t=r||t===X?1:fo(t),t=e-t,Ee(n,0,t<0?0:t)):[]},K.dropRightWhile=function(n,t){return n&&n.length?Me(n,qu(t,3),!0,!0):[]},K.dropWhile=function(n,t){return n&&n.length?Me(n,qu(t,3),!0):[]},K.fill=function(n,t,r,e){var u=null==n?0:n.length;return u?(r&&"number"!=typeof r&&ui(n,t,r)&&(r=0,e=u),zr(n,t,r,e)):[]},K.filter=function(n,t){return(Zc(n)?f:Br)(n,qu(t,3))},K.flatMap=function(n,t){return Fr($i(n,t),1)},K.flatMapDeep=function(n,t){return Fr($i(n,t),Rn)},K.flatMapDepth=function(n,t,r){return r=r===X?1:fo(r),Fr($i(n,t),r)},K.flatten=Ii,K.flattenDeep=function(n){return(null==n?0:n.length)?Fr(n,Rn):[]},K.flattenDepth=function(n,t){return(null==n?0:n.length)?(t=t===X?1:fo(t),Fr(n,t)):[]},K.flip=function(n){return Lu(n,mn)},K.flow=Ia,K.flowRight=Ra,K.fromPairs=function(n){for(var t=-1,r=null==n?0:n.length,e={};++t<r;){var u=n[t];e[u[0]]=u[1]}return e},K.functions=function(n){return null==n?[]:qr(n,_o(n))},K.functionsIn=function(n){return null==n?[]:qr(n,vo(n))},K.groupBy=Rc,K.initial=function(n){return(null==n?0:n.length)?Ee(n,0,-1):[]},K.intersection=lc,K.intersectionBy=sc,K.intersectionWith=hc,K.invert=ca,K.invertBy=aa,K.invokeMap=zc,K.iteratee=Ao,K.keyBy=Ec,K.keys=_o,K.keysIn=vo,K.map=$i,K.mapKeys=function(n,t){var r={};return t=qu(t,3),Nr(n,function(n,e,u){sr(r,t(n,e,u),n)}),r},K.mapValues=function(n,t){var r={};return t=qu(t,3),Nr(n,function(n,e,u){sr(r,e,t(n,e,u))}),r},K.matches=function(n){return se(_r(n,fn))},K.matchesProperty=function(n,t){return he(n,_r(t,fn))},K.memoize=qi,K.merge=sa,K.mergeWith=ha,K.method=za,K.methodOf=Ea,K.mixin=ko,K.negate=Zi,K.nthArg=function(n){return n=fo(n),ke(function(t){return ve(t,n)})},K.omit=pa,K.omitBy=function(n,t){return go(n,Zi(qu(t)))},K.once=function(n){return Mi(2,n)},K.orderBy=function(n,t,r,e){return null==n?[]:(Zc(t)||(t=null==t?[]:[t]),r=e?X:r,Zc(r)||(r=null==r?[]:[r]),ge(n,t,r))},K.over=Sa,K.overArgs=$c,K.overEvery=La,K.overSome=Wa,K.partial=Dc,K.partialRight=Mc,K.partition=Sc,K.pick=_a,K.pickBy=go,K.property=Io,K.propertyOf=function(n){return function(t){return null==n?X:Zr(n,t)}},K.pull=pc,K.pullAll=Ei,K.pullAllBy=function(n,t,r){return n&&n.length&&t&&t.length?we(n,t,qu(r,2)):n},K.pullAllWith=function(n,t,r){return n&&n.length&&t&&t.length?we(n,t,X,r):n},K.pullAt=_c,K.range=Ca,K.rangeRight=Ua,K.rearg=Fc,K.reject=function(n,t){return(Zc(n)?f:Br)(n,Zi(qu(t,3)))},K.remove=function(n,t){var r=[];if(!n||!n.length)return r;var e=-1,u=[],i=n.length;for(t=qu(t,3);++e<i;){var o=n[e];t(o,e,n)&&(r.push(o),u.push(e))}return me(n,u),r},K.rest=function(n,t){if("function"!=typeof n)throw new $o(rn);return t=t===X?t:fo(t),ke(n,t)},K.reverse=Si,K.sampleSize=function(n,t,r){return t=(r?ui(n,t,r):t===X)?1:fo(t),(Zc(n)?tr:Ie)(n,t)},K.set=function(n,t,r){return null==n?n:Re(n,t,r)},K.setWith=function(n,t,r,e){return e="function"==typeof e?e:X,null==n?n:Re(n,t,r,e)},K.shuffle=function(n){return(Zc(n)?rr:ze)(n)},K.slice=function(n,t,r){var e=null==n?0:n.length;return e?(r&&"number"!=typeof r&&ui(n,t,r)?(t=0,r=e):(t=null==t?0:fo(t),r=r===X?e:fo(r)),Ee(n,t,r)):[]},K.sortBy=Lc,K.sortedUniq=function(n){return n&&n.length?Ce(n):[]},K.sortedUniqBy=function(n,t){return n&&n.length?Ce(n,qu(t,2)):[]},K.split=function(n,t,r){return r&&"number"!=typeof r&&ui(n,t,r)&&(t=r=X),(r=r===X?Ln:r>>>0)?(n=so(n))&&("string"==typeof t||null!=t&&!Jc(t))&&!(t=Be(t))&&$(n)?Ve(H(n),0,r):n.split(t,r):[]},K.spread=function(n,t){if("function"!=typeof n)throw new $o(rn);return t=null==t?0:wf(fo(t),0),ke(function(e){var u=e[t],i=Ve(e,0,t);return u&&s(i,u),r(n,this,i)})},K.tail=function(n){var t=null==n?0:n.length;return t?Ee(n,1,t):[]},K.take=function(n,t,r){return n&&n.length?(t=r||t===X?1:fo(t),Ee(n,0,t<0?0:t)):[]},K.takeRight=function(n,t,r){var e=null==n?0:n.length;return e?(t=r||t===X?1:fo(t),t=e-t,Ee(n,t<0?0:t,e)):[]},K.takeRightWhile=function(n,t){return n&&n.length?Me(n,qu(t,3),!1,!0):[]},K.takeWhile=function(n,t){return n&&n.length?Me(n,qu(t,3)):[]},K.tap=function(n,t){return t(n),n},K.throttle=function(n,t,r){var e=!0,u=!0;if("function"!=typeof n)throw new $o(rn);return Xi(r)&&(e="leading"in r?!!r.leading:e,u="trailing"in r?!!r.trailing:u),Pi(n,t,{leading:e,maxWait:t,trailing:u})},K.thru=Ui,K.toArray=io,K.toPairs=va,K.toPairsIn=ga,K.toPath=function(n){return Zc(n)?l(n,mi):uo(n)?[n]:ou(oc(so(n)))},K.toPlainObject=lo,K.transform=function(n,t,r){var e=Zc(n),i=e||Vc(n)||Qc(n);if(t=qu(t,4),null==r){var o=n&&n.constructor;r=i?e?new o:[]:Xi(n)&&Ji(o)?Nf(tf(n)):{}}return(i?u:Nr)(n,function(n,e,u){return t(r,n,e,u)}),r},K.unary=function(n){return Di(n,1)},K.union=vc,K.unionBy=gc,K.unionWith=yc,K.uniq=function(n){return n&&n.length?Te(n):[]},K.uniqBy=function(n,t){return n&&n.length?Te(n,qu(t,2)):[]},K.uniqWith=function(n,t){return t="function"==typeof t?t:X,n&&n.length?Te(n,X,t):[]},K.unset=function(n,t){return null==n||$e(n,t)},K.unzip=Li,K.unzipWith=Wi,K.update=function(n,t,r){return null==n?n:De(n,t,Ze(r))},K.updateWith=function(n,t,r,e){return e="function"==typeof e?e:X,null==n?n:De(n,t,Ze(r),e)},K.values=yo,K.valuesIn=function(n){return null==n?[]:S(n,vo(n))},K.without=dc,K.words=mo,K.wrap=function(n,t){return Dc(Ze(t),n)},K.xor=bc,K.xorBy=wc,K.xorWith=mc,K.zip=xc,K.zipObject=function(n,t){return Pe(n||[],t||[],ur)},K.zipObjectDeep=function(n,t){return Pe(n||[],t||[],Re)},K.zipWith=jc,K.entries=va,K.entriesIn=ga,K.extend=ra,K.extendWith=ea,ko(K,K),K.add=Ba,K.attempt=ka,K.camelCase=ya,K.capitalize=bo,K.ceil=Ta,K.clamp=function(n,t,r){return r===X&&(r=t,t=X),r!==X&&(r=(r=ao(r))===r?r:0),t!==X&&(t=(t=ao(t))===t?t:0),pr(ao(n),t,r)},K.clone=function(n){return _r(n,an)},K.cloneDeep=function(n){return _r(n,fn|an)},K.cloneDeepWith=function(n,t){return t="function"==typeof t?t:X,_r(n,fn|an,t)},K.cloneWith=function(n,t){return t="function"==typeof t?t:X,_r(n,an,t)},K.conformsTo=function(n,t){return null==t||xr(n,t,_o(t))},K.deburr=wo,K.defaultTo=function(n,t){return null==n||n!==n?t:n},K.divide=$a,K.endsWith=function(n,t,r){n=so(n),t=Be(t);var e=n.length,u=r=r===X?e:pr(fo(r),0,e);return(r-=t.length)>=0&&n.slice(r,u)==t},K.eq=Ki,K.escape=function(n){return(n=so(n))&&wt.test(n)?n.replace(dt,$r):n},K.escapeRegExp=function(n){return(n=so(n))&&zt.test(n)?n.replace(Rt,"\\$&"):n},K.every=function(n,t,r){var e=Zc(n)?o:Or;return r&&ui(n,t,r)&&(t=X),e(n,qu(t,3))},K.find=Oc,K.findIndex=ki,K.findKey=function(n,t){return y(n,qu(t,3),Nr)},K.findLast=Ic,K.findLastIndex=Oi,K.findLastKey=function(n,t){return y(n,qu(t,3),Pr)},K.floor=Da,K.forEach=Bi,K.forEachRight=Ti,K.forIn=function(n,t){return null==n?n:Zf(n,qu(t,3),vo)},K.forInRight=function(n,t){return null==n?n:Kf(n,qu(t,3),vo)},K.forOwn=function(n,t){return n&&Nr(n,qu(t,3))},K.forOwnRight=function(n,t){return n&&Pr(n,qu(t,3))},K.get=ho,K.gt=Nc,K.gte=Pc,K.has=function(n,t){return null!=n&&Yu(n,t,Hr)},K.hasIn=po,K.head=Ri,K.identity=jo,K.includes=function(n,t,r,e){n=Vi(n)?n:yo(n),r=r&&!e?fo(r):0;var u=n.length;return r<0&&(r=wf(u+r,0)),eo(n)?r<=u&&n.indexOf(t,r)>-1:!!u&&b(n,t,r)>-1},K.indexOf=function(n,t,r){var e=null==n?0:n.length;if(!e)return-1;var u=null==r?0:fo(r);return u<0&&(u=wf(e+u,0)),b(n,t,u)},K.inRange=function(n,t,r){return t=oo(t),r===X?(r=t,t=0):r=oo(r),n=ao(n),Yr(n,t,r)},K.invoke=la,K.isArguments=qc,K.isArray=Zc,K.isArrayBuffer=Kc,K.isArrayLike=Vi,K.isArrayLikeObject=Gi,K.isBoolean=function(n){return!0===n||!1===n||no(n)&&Vr(n)==Dn},K.isBuffer=Vc,K.isDate=Gc,K.isElement=function(n){return no(n)&&1===n.nodeType&&!ro(n)},K.isEmpty=function(n){if(null==n)return!0;if(Vi(n)&&(Zc(n)||"string"==typeof n||"function"==typeof n.splice||Vc(n)||Qc(n)||qc(n)))return!n.length;var t=tc(n);if(t==Zn||t==Yn)return!n.size;if(ai(n))return!fe(n).length;for(var r in n)if(qo.call(n,r))return!1;return!0},K.isEqual=function(n,t){return re(n,t)},K.isEqualWith=function(n,t,r){var e=(r="function"==typeof r?r:X)?r(n,t):X;return e===X?re(n,t,X,r):!!e},K.isError=Hi,K.isFinite=function(n){return"number"==typeof n&&yf(n)},K.isFunction=Ji,K.isInteger=Yi,K.isLength=Qi,K.isMap=Hc,K.isMatch=function(n,t){return n===t||ue(n,t,Ku(t))},K.isMatchWith=function(n,t,r){return r="function"==typeof r?r:X,ue(n,t,Ku(t),r)},K.isNaN=function(n){return to(n)&&n!=+n},K.isNative=function(n){if(rc(n))throw new Lo(tn);return ie(n)},K.isNil=function(n){return null==n},K.isNull=function(n){return null===n},K.isNumber=to,K.isObject=Xi,K.isObjectLike=no,K.isPlainObject=ro,K.isRegExp=Jc,K.isSafeInteger=function(n){return Yi(n)&&n>=-zn&&n<=zn},K.isSet=Yc,K.isString=eo,K.isSymbol=uo,K.isTypedArray=Qc,K.isUndefined=function(n){return n===X},K.isWeakMap=function(n){return no(n)&&tc(n)==tt},K.isWeakSet=function(n){return no(n)&&Vr(n)==rt},K.join=function(n,t){return null==n?"":df.call(n,t)},K.kebabCase=da,K.last=zi,K.lastIndexOf=function(n,t,r){var e=null==n?0:n.length;if(!e)return-1;var u=e;return r!==X&&(u=(u=fo(r))<0?wf(e+u,0):mf(u,e-1)),t===t?V(n,t,u):d(n,m,u,!0)},K.lowerCase=ba,K.lowerFirst=wa,K.lt=Xc,K.lte=na,K.max=function(n){return n&&n.length?Rr(n,jo,Gr):X},K.maxBy=function(n,t){return n&&n.length?Rr(n,qu(t,2),Gr):X},K.mean=function(n){return x(n,jo)},K.meanBy=function(n,t){return x(n,qu(t,2))},K.min=function(n){return n&&n.length?Rr(n,jo,ae):X},K.minBy=function(n,t){return n&&n.length?Rr(n,qu(t,2),ae):X},K.stubArray=Ro,K.stubFalse=zo,K.stubObject=function(){return{}},K.stubString=function(){return""},K.stubTrue=function(){return!0},K.multiply=Ma,K.nth=function(n,t){return n&&n.length?ve(n,fo(t)):X},K.noConflict=function(){return Ar._===this&&(Ar._=Ho),this},K.noop=Oo,K.now=Wc,K.pad=function(n,t,r){n=so(n);var e=(t=fo(t))?G(n):0;if(!t||e>=t)return n;var u=(t-e)/2;return ku(_f(u),r)+n+ku(pf(u),r)},K.padEnd=function(n,t,r){n=so(n);var e=(t=fo(t))?G(n):0;return t&&e<t?n+ku(t-e,r):n},K.padStart=function(n,t,r){n=so(n);var e=(t=fo(t))?G(n):0;return t&&e<t?ku(t-e,r)+n:n},K.parseInt=function(n,t,r){return r||null==t?t=0:t&&(t=+t),jf(so(n).replace(St,""),t||0)},K.random=function(n,t,r){if(r&&"boolean"!=typeof r&&ui(n,t,r)&&(t=r=X),r===X&&("boolean"==typeof t?(r=t,t=X):"boolean"==typeof n&&(r=n,n=X)),n===X&&t===X?(n=0,t=1):(n=oo(n),t===X?(t=n,n=0):t=oo(t)),n>t){var e=n;n=t,t=e}if(r||n%1||t%1){var u=Af();return mf(n+u*(t-n+wr("1e-"+((u+"").length-1))),t)}return xe(n,t)},K.reduce=function(n,t,r){var e=Zc(n)?h:k,u=arguments.length<3;return e(n,qu(t,4),r,u,Pf)},K.reduceRight=function(n,t,r){var e=Zc(n)?p:k,u=arguments.length<3;return e(n,qu(t,4),r,u,qf)},K.repeat=function(n,t,r){return t=(r?ui(n,t,r):t===X)?1:fo(t),Ae(so(n),t)},K.replace=function(){var n=arguments,t=so(n[0]);return n.length<3?t:t.replace(n[1],n[2])},K.result=function(n,t,r){var e=-1,u=(t=Ke(t,n)).length;for(u||(u=1,n=X);++e<u;){var i=null==n?X:n[mi(t[e])];i===X&&(e=u,i=r),n=Ji(i)?i.call(n):i}return n},K.round=Fa,K.runInContext=v,K.sample=function(n){return(Zc(n)?nr:Oe)(n)},K.size=function(n){if(null==n)return 0;if(Vi(n))return eo(n)?G(n):n.length;var t=tc(n);return t==Zn||t==Yn?n.size:fe(n).length},K.snakeCase=ma,K.some=function(n,t,r){var e=Zc(n)?_:Se;return r&&ui(n,t,r)&&(t=X),e(n,qu(t,3))},K.sortedIndex=function(n,t){return Le(n,t)},K.sortedIndexBy=function(n,t,r){return We(n,t,qu(r,2))},K.sortedIndexOf=function(n,t){var r=null==n?0:n.length;if(r){var e=Le(n,t);if(e<r&&Ki(n[e],t))return e}return-1},K.sortedLastIndex=function(n,t){return Le(n,t,!0)},K.sortedLastIndexBy=function(n,t,r){return We(n,t,qu(r,2),!0)},K.sortedLastIndexOf=function(n,t){if(null==n?0:n.length){var r=Le(n,t,!0)-1;if(Ki(n[r],t))return r}return-1},K.startCase=xa,K.startsWith=function(n,t,r){return n=so(n),r=null==r?0:pr(fo(r),0,n.length),t=Be(t),n.slice(r,r+t.length)==t},K.subtract=Na,K.sum=function(n){return n&&n.length?I(n,jo):0},K.sumBy=function(n,t){return n&&n.length?I(n,qu(t,2)):0},K.template=function(n,t,r){var e=K.templateSettings;r&&ui(n,t,r)&&(t=X),n=so(n),t=ea({},t,e,Wu);var u,i,o=ea({},t.imports,e.imports,Wu),f=_o(o),c=S(o,f),a=0,l=t.interpolate||Kt,s="__p += '",h=Bo((t.escape||Kt).source+"|"+l.source+"|"+(l===jt?$t:Kt).source+"|"+(t.evaluate||Kt).source+"|$","g"),p="//# sourceURL="+("sourceURL"in t?t.sourceURL:"lodash.templateSources["+ ++gr+"]")+"\n";n.replace(h,function(t,r,e,o,f,c){return e||(e=o),s+=n.slice(a,c).replace(Vt,B),r&&(u=!0,s+="' +\n__e("+r+") +\n'"),f&&(i=!0,s+="';\n"+f+";\n__p += '"),e&&(s+="' +\n((__t = ("+e+")) == null ? '' : __t) +\n'"),a=c+t.length,t}),s+="';\n";var _=t.variable;_||(s="with (obj) {\n"+s+"\n}\n"),s=(i?s.replace(_t,""):s).replace(vt,"$1").replace(gt,"$1;"),s="function("+(_||"obj")+") {\n"+(_?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(u?", __e = _.escape":"")+(i?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+s+"return __p\n}";var v=ka(function(){return Wo(f,p+"return "+s).apply(X,c)});if(v.source=s,Hi(v))throw v;return v},K.times=function(n,t){if((n=fo(n))<1||n>zn)return[];var r=Ln,e=mf(n,Ln);t=qu(t),n-=Ln;for(var u=R(e,t);++r<n;)t(r);return u},K.toFinite=oo,K.toInteger=fo,K.toLength=co,K.toLower=function(n){return so(n).toLowerCase()},K.toNumber=ao,K.toSafeInteger=function(n){return n?pr(fo(n),-zn,zn):0===n?n:0},K.toString=so,K.toUpper=function(n){return so(n).toUpperCase()},K.trim=function(n,t,r){if((n=so(n))&&(r||t===X))return n.replace(Et,"");if(!n||!(t=Be(t)))return n;var e=H(n),u=H(t);return Ve(e,W(e,u),C(e,u)+1).join("")},K.trimEnd=function(n,t,r){if((n=so(n))&&(r||t===X))return n.replace(Lt,"");if(!n||!(t=Be(t)))return n;var e=H(n);return Ve(e,0,C(e,H(t))+1).join("")},K.trimStart=function(n,t,r){if((n=so(n))&&(r||t===X))return n.replace(St,"");if(!n||!(t=Be(t)))return n;var e=H(n);return Ve(e,W(e,H(t))).join("")},K.truncate=function(n,t){var r=xn,e=jn;if(Xi(t)){var u="separator"in t?t.separator:u;r="length"in t?fo(t.length):r,e="omission"in t?Be(t.omission):e}var i=(n=so(n)).length;if($(n)){var o=H(n);i=o.length}if(r>=i)return n;var f=r-G(e);if(f<1)return e;var c=o?Ve(o,0,f).join(""):n.slice(0,f);if(u===X)return c+e;if(o&&(f+=c.length-f),Jc(u)){if(n.slice(f).search(u)){var a,l=c;for(u.global||(u=Bo(u.source,so(Dt.exec(u))+"g")),u.lastIndex=0;a=u.exec(l);)var s=a.index;c=c.slice(0,s===X?f:s)}}else if(n.indexOf(Be(u),f)!=f){var h=c.lastIndexOf(u);h>-1&&(c=c.slice(0,h))}return c+e},K.unescape=function(n){return(n=so(n))&&bt.test(n)?n.replace(yt,Dr):n},K.uniqueId=function(n){var t=++Zo;return so(n)+t},K.upperCase=ja,K.upperFirst=Aa,K.each=Bi,K.eachRight=Ti,K.first=Ri,ko(K,function(){var n={};return Nr(K,function(t,r){qo.call(K.prototype,r)||(n[r]=t)}),n}(),{chain:!1}),K.VERSION="4.17.4",u(["bind","bindKey","curry","curryRight","partial","partialRight"],function(n){K[n].placeholder=K}),u(["drop","take"],function(n,t){Bt.prototype[n]=function(r){r=r===X?1:wf(fo(r),0);var e=this.__filtered__&&!t?new Bt(this):this.clone();return e.__filtered__?e.__takeCount__=mf(r,e.__takeCount__):e.__views__.push({size:mf(r,Ln),type:n+(e.__dir__<0?"Right":"")}),e},Bt.prototype[n+"Right"]=function(t){return this.reverse()[n](t).reverse()}}),u(["filter","map","takeWhile"],function(n,t){var r=t+1,e=r==On||3==r;Bt.prototype[n]=function(n){var t=this.clone();return t.__iteratees__.push({iteratee:qu(n,3),type:r}),t.__filtered__=t.__filtered__||e,t}}),u(["head","last"],function(n,t){var r="take"+(t?"Right":"");Bt.prototype[n]=function(){return this[r](1).value()[0]}}),u(["initial","tail"],function(n,t){var r="drop"+(t?"":"Right");Bt.prototype[n]=function(){return this.__filtered__?new Bt(this):this[r](1)}}),Bt.prototype.compact=function(){return this.filter(jo)},Bt.prototype.find=function(n){return this.filter(n).head()},Bt.prototype.findLast=function(n){return this.reverse().find(n)},Bt.prototype.invokeMap=ke(function(n,t){return"function"==typeof n?new Bt(this):this.map(function(r){return ne(r,n,t)})}),Bt.prototype.reject=function(n){return this.filter(Zi(qu(n)))},Bt.prototype.slice=function(n,t){n=fo(n);var r=this;return r.__filtered__&&(n>0||t<0)?new Bt(r):(n<0?r=r.takeRight(-n):n&&(r=r.drop(n)),t!==X&&(r=(t=fo(t))<0?r.dropRight(-t):r.take(t-n)),r)},Bt.prototype.takeRightWhile=function(n){return this.reverse().takeWhile(n).reverse()},Bt.prototype.toArray=function(){return this.take(Ln)},Nr(Bt.prototype,function(n,t){var r=/^(?:filter|find|map|reject)|While$/.test(t),e=/^(?:head|last)$/.test(t),u=K[e?"take"+("last"==t?"Right":""):t],i=e||/^find/.test(t);u&&(K.prototype[t]=function(){var t=this.__wrapped__,o=e?[1]:arguments,f=t instanceof Bt,c=o[0],a=f||Zc(t),l=function(n){var t=u.apply(K,s([n],o));return e&&h?t[0]:t};a&&r&&"function"==typeof c&&1!=c.length&&(f=a=!1);var h=this.__chain__,p=!!this.__actions__.length,_=i&&!h,v=f&&!p;if(!i&&a){t=v?t:new Bt(this);var g=n.apply(t,o);return g.__actions__.push({func:Ui,args:[l],thisArg:X}),new Y(g,h)}return _&&v?n.apply(this,o):(g=this.thru(l),_?e?g.value()[0]:g.value():g)})}),u(["pop","push","shift","sort","splice","unshift"],function(n){var t=Do[n],r=/^(?:push|sort|unshift)$/.test(n)?"tap":"thru",e=/^(?:pop|shift)$/.test(n);K.prototype[n]=function(){var n=arguments;if(e&&!this.__chain__){var u=this.value();return t.apply(Zc(u)?u:[],n)}return this[r](function(r){return t.apply(Zc(r)?r:[],n)})}}),Nr(Bt.prototype,function(n,t){var r=K[t];if(r){var e=r.name+"";(Wf[e]||(Wf[e]=[])).push({name:t,func:r})}}),Wf[mu(X,pn).name]=[{name:"wrapper",func:X}],Bt.prototype.clone=function(){var n=new Bt(this.__wrapped__);return n.__actions__=ou(this.__actions__),n.__dir__=this.__dir__,n.__filtered__=this.__filtered__,n.__iteratees__=ou(this.__iteratees__),n.__takeCount__=this.__takeCount__,n.__views__=ou(this.__views__),n},Bt.prototype.reverse=function(){if(this.__filtered__){var n=new Bt(this);n.__dir__=-1,n.__filtered__=!0}else(n=this.clone()).__dir__*=-1;return n},Bt.prototype.value=function(){var n=this.__wrapped__.value(),t=this.__dir__,r=Zc(n),e=t<0,u=r?n.length:0,i=Hu(0,u,this.__views__),o=i.start,f=i.end,c=f-o,a=e?f:o-1,l=this.__iteratees__,s=l.length,h=0,p=mf(c,this.__takeCount__);if(!r||!e&&u==c&&p==c)return Fe(n,this.__actions__);var _=[];n:for(;c--&&h<p;){for(var v=-1,g=n[a+=t];++v<s;){var y=l[v],d=y.iteratee,b=y.type,w=d(g);if(b==In)g=w;else if(!w){if(b==On)continue n;break n}}_[h++]=g}return _},K.prototype.at=Ac,K.prototype.chain=function(){return Ci(this)},K.prototype.commit=function(){return new Y(this.value(),this.__chain__)},K.prototype.next=function(){this.__values__===X&&(this.__values__=io(this.value()));var n=this.__index__>=this.__values__.length;return{done:n,value:n?X:this.__values__[this.__index__++]}},K.prototype.plant=function(n){for(var t,r=this;r instanceof J;){var e=Ai(r);e.__index__=0,e.__values__=X,t?u.__wrapped__=e:t=e;var u=e;r=r.__wrapped__}return u.__wrapped__=n,t},K.prototype.reverse=function(){var n=this.__wrapped__;if(n instanceof Bt){var t=n;return this.__actions__.length&&(t=new Bt(this)),(t=t.reverse()).__actions__.push({func:Ui,args:[Si],thisArg:X}),new Y(t,this.__chain__)}return this.thru(Si)},K.prototype.toJSON=K.prototype.valueOf=K.prototype.value=function(){return Fe(this.__wrapped__,this.__actions__)},K.prototype.first=K.prototype.head,ff&&(K.prototype[ff]=function(){return this}),K}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(Ar._=Mr,define(function(){return Mr})):Or?((Or.exports=Mr)._=Mr,kr._=Mr):Ar._=Mr}).call(this);

}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})

},{}],"mock-xmlhttprequest":[function(require,module,exports){
"use strict";module.exports=require("./src/MockXhr");

},{"./src/MockXhr":34}],"prop-types":[function(require,module,exports){
var REACT_ELEMENT_TYPE,isValidElement,throwOnDirectAccess;module.exports=require("./factoryWithThrowingShims")();

},{"./factoryWithThrowingShims":36}],"react-codemirror":[function(require,module,exports){
"use strict";function normalizeLineEndings(o){return o?o.replace(/\r\n|\r/g,"\n"):o}var React=require("react"),ReactDOM=require("react-dom"),PropTypes=require("prop-types"),className=require("classnames"),debounce=require("lodash.debounce"),isEqual=require("lodash.isequal"),createReactClass=require("create-react-class"),CodeMirror=createReactClass({propTypes:{autoFocus:PropTypes.bool,className:PropTypes.any,codeMirrorInstance:PropTypes.func,defaultValue:PropTypes.string,name:PropTypes.string,onChange:PropTypes.func,onCursorActivity:PropTypes.func,onFocusChange:PropTypes.func,onScroll:PropTypes.func,options:PropTypes.object,path:PropTypes.string,value:PropTypes.string,preserveScrollPosition:PropTypes.bool},getDefaultProps:function(){return{preserveScrollPosition:!1}},getCodeMirrorInstance:function(){return this.props.codeMirrorInstance||require("codemirror")},getInitialState:function(){return{isFocused:!1}},componentWillMount:function(){this.componentWillReceiveProps=debounce(this.componentWillReceiveProps,0),this.props.path&&console.error("Warning: react-codemirror: the `path` prop has been changed to `name`")},componentDidMount:function(){var o=this.getCodeMirrorInstance();this.codeMirror=o.fromTextArea(this.textareaNode,this.props.options),this.codeMirror.on("change",this.codemirrorValueChanged),this.codeMirror.on("cursorActivity",this.cursorActivity),this.codeMirror.on("focus",this.focusChanged.bind(this,!0)),this.codeMirror.on("blur",this.focusChanged.bind(this,!1)),this.codeMirror.on("scroll",this.scrollChanged),this.codeMirror.setValue(this.props.defaultValue||this.props.value||"")},componentWillUnmount:function(){this.codeMirror&&this.codeMirror.toTextArea()},componentWillReceiveProps:function(o){if(this.codeMirror&&void 0!==o.value&&o.value!==this.props.value&&normalizeLineEndings(this.codeMirror.getValue())!==normalizeLineEndings(o.value))if(this.props.preserveScrollPosition){var e=this.codeMirror.getScrollInfo();this.codeMirror.setValue(o.value),this.codeMirror.scrollTo(e.left,e.top)}else this.codeMirror.setValue(o.value);if("object"==typeof o.options)for(var r in o.options)o.options.hasOwnProperty(r)&&this.setOptionIfChanged(r,o.options[r])},setOptionIfChanged:function(o,e){var r=this.codeMirror.getOption(o);isEqual(r,e)||this.codeMirror.setOption(o,e)},getCodeMirror:function(){return this.codeMirror},focus:function(){this.codeMirror&&this.codeMirror.focus()},focusChanged:function(o){this.setState({isFocused:o}),this.props.onFocusChange&&this.props.onFocusChange(o)},cursorActivity:function(o){this.props.onCursorActivity&&this.props.onCursorActivity(o)},scrollChanged:function(o){this.props.onScroll&&this.props.onScroll(o.getScrollInfo())},codemirrorValueChanged:function(o,e){this.props.onChange&&"setValue"!==e.origin&&this.props.onChange(o.getValue(),e)},render:function(){var o=this,e=className("ReactCodeMirror",this.state.isFocused?"ReactCodeMirror--focused":null,this.props.className);return React.createElement("div",{className:e},React.createElement("textarea",{ref:function(e){return o.textareaNode=e},name:this.props.name||this.props.path,defaultValue:this.props.value,autoComplete:"off",autoFocus:this.props.autoFocus}))}});module.exports=CodeMirror;

},{"classnames":"classnames","codemirror":1,"create-react-class":3,"lodash.debounce":19,"lodash.isequal":20,"prop-types":"prop-types","react":"react","react-dom":"react-dom"}],"react-dom":[function(require,module,exports){
"use strict";function checkDCE(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(checkDCE)}catch(_){console.error(_)}}checkDCE(),module.exports=require("./cjs/react-dom.production.min.js");

},{"./cjs/react-dom.production.min.js":38}],"react-redux":[function(require,module,exports){
"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}exports.__esModule=!0,exports.connect=exports.connectAdvanced=exports.createProvider=exports.Provider=void 0;var _Provider=require("./components/Provider"),_Provider2=_interopRequireDefault(_Provider),_connectAdvanced=require("./components/connectAdvanced"),_connectAdvanced2=_interopRequireDefault(_connectAdvanced),_connect=require("./connect/connect"),_connect2=_interopRequireDefault(_connect);exports.Provider=_Provider2.default,exports.createProvider=_Provider.createProvider,exports.connectAdvanced=_connectAdvanced2.default,exports.connect=_connect2.default;

},{"./components/Provider":39,"./components/connectAdvanced":40,"./connect/connect":41}],"react-test-renderer":[function(require,module,exports){
"use strict";throw Error("test renderer is not available in production mode.");

},{}],"react":[function(require,module,exports){
"use strict";module.exports=require("./cjs/react.production.min.js");

},{"./cjs/react.production.min.js":53}],"redux-logger":[function(require,module,exports){
(function (global){
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e.reduxLogger=e.reduxLogger||{})}(this,function(e){"use strict";function t(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}function r(e,t){Object.defineProperty(this,"kind",{value:e,enumerable:!0}),t&&t.length&&Object.defineProperty(this,"path",{value:t,enumerable:!0})}function n(e,t,r){n.super_.call(this,"E",e),Object.defineProperty(this,"lhs",{value:t,enumerable:!0}),Object.defineProperty(this,"rhs",{value:r,enumerable:!0})}function o(e,t){o.super_.call(this,"N",e),Object.defineProperty(this,"rhs",{value:t,enumerable:!0})}function i(e,t){i.super_.call(this,"D",e),Object.defineProperty(this,"lhs",{value:t,enumerable:!0})}function a(e,t,r){a.super_.call(this,"A",e),Object.defineProperty(this,"index",{value:t,enumerable:!0}),Object.defineProperty(this,"item",{value:r,enumerable:!0})}function l(e,t,r){var n=e.slice((r||t)+1||e.length);return e.length=t<0?e.length+t:t,e.push.apply(e,n),e}function c(e){var t=void 0===e?"undefined":D(e);return"object"!==t?t:e===Math?"math":null===e?"null":Array.isArray(e)?"array":"[object Date]"===Object.prototype.toString.call(e)?"date":"function"==typeof e.toString&&/^\/.*\//.test(e.toString())?"regexp":"object"}function u(e,t,r,f,s,d,p){s=s||[],p=p||[];var g=s.slice(0);if(void 0!==d){if(f){if("function"==typeof f&&f(g,d))return;if("object"===(void 0===f?"undefined":D(f))){if(f.prefilter&&f.prefilter(g,d))return;if(f.normalize){var h=f.normalize(g,d,e,t);h&&(e=h[0],t=h[1])}}}g.push(d)}"regexp"===c(e)&&"regexp"===c(t)&&(e=e.toString(),t=t.toString());var v=void 0===e?"undefined":D(e),y=void 0===t?"undefined":D(t),b="undefined"!==v||p&&p[p.length-1].lhs&&p[p.length-1].lhs.hasOwnProperty(d),m="undefined"!==y||p&&p[p.length-1].rhs&&p[p.length-1].rhs.hasOwnProperty(d);if(!b&&m)r(new o(g,t));else if(!m&&b)r(new i(g,e));else if(c(e)!==c(t))r(new n(g,e,t));else if("date"===c(e)&&e-t!=0)r(new n(g,e,t));else if("object"===v&&null!==e&&null!==t)if(p.filter(function(t){return t.lhs===e}).length)e!==t&&r(new n(g,e,t));else{if(p.push({lhs:e,rhs:t}),Array.isArray(e)){var w;for(e.length,w=0;w<e.length;w++)w>=t.length?r(new a(g,w,new i(void 0,e[w]))):u(e[w],t[w],r,f,g,w,p);for(;w<t.length;)r(new a(g,w,new o(void 0,t[w++])))}else{var x=Object.keys(e),S=Object.keys(t);x.forEach(function(n,o){var i=S.indexOf(n);i>=0?(u(e[n],t[n],r,f,g,n,p),S=l(S,i)):u(e[n],void 0,r,f,g,n,p)}),S.forEach(function(e){u(void 0,t[e],r,f,g,e,p)})}p.length=p.length-1}else e!==t&&("number"===v&&isNaN(e)&&isNaN(t)||r(new n(g,e,t)))}function f(e,t,r,n){return n=n||[],u(e,t,function(e){e&&n.push(e)},r),n.length?n:void 0}function s(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":s(o[r.path[n]],r.index,r.item);break;case"D":delete o[r.path[n]];break;case"E":case"N":o[r.path[n]]=r.rhs}}else switch(r.kind){case"A":s(e[t],r.index,r.item);break;case"D":e=l(e,t);break;case"E":case"N":e[t]=r.rhs}return e}function d(e,t,r){if(e&&t&&r&&r.kind){for(var n=e,o=-1,i=r.path?r.path.length-1:0;++o<i;)void 0===n[r.path[o]]&&(n[r.path[o]]="number"==typeof r.path[o]?[]:{}),n=n[r.path[o]];switch(r.kind){case"A":s(r.path?n[r.path[o]]:n,r.index,r.item);break;case"D":delete n[r.path[o]];break;case"E":case"N":n[r.path[o]]=r.rhs}}}function p(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":p(o[r.path[n]],r.index,r.item);break;case"D":case"E":o[r.path[n]]=r.lhs;break;case"N":delete o[r.path[n]]}}else switch(r.kind){case"A":p(e[t],r.index,r.item);break;case"D":case"E":e[t]=r.lhs;break;case"N":e=l(e,t)}return e}function g(e){return"color: "+P[e].color+"; font-weight: bold"}function h(e){var t=e.kind,r=e.path,n=e.lhs,o=e.rhs,i=e.index,a=e.item;switch(t){case"E":return[r.join("."),n,"→",o];case"N":return[r.join("."),o];case"D":return[r.join(".")];case"A":return[r.join(".")+"["+i+"]",a];default:return[]}}function v(e,t,r,n){var o=f(e,t);try{n?r.groupCollapsed("diff"):r.group("diff")}catch(e){r.log("diff")}o?o.forEach(function(e){var t=e.kind,n=h(e);r.log.apply(r,["%c "+P[t].text,g(t)].concat(O(n)))}):r.log("—— no diff ——");try{r.groupEnd()}catch(e){r.log("—— diff end —— ")}}function y(e,t,r,n){switch(void 0===e?"undefined":D(e)){case"object":return"function"==typeof e[n]?e[n].apply(e,O(r)):e[n];case"function":return e(t);default:return e}}function b(e){var t=e.timestamp,r=e.duration;return function(e,n,o){var i=["action"];return i.push("%c"+String(e.type)),t&&i.push("%c@ "+n),r&&i.push("%c(in "+o.toFixed(2)+" ms)"),i.join(" ")}}function m(e,t){var r=t.logger,n=t.actionTransformer,o=t.titleFormatter,i=void 0===o?b(t):o,a=t.collapsed,l=t.colors,c=t.level,u=t.diff,f=void 0===t.titleFormatter;e.forEach(function(o,s){var d=o.started,p=o.startedTime,g=o.action,h=o.prevState,b=o.error,m=o.took,w=o.nextState,x=e[s+1];x&&(w=x.prevState,m=x.started-d);var S=n(g),j="function"==typeof a?a(function(){return w},g,o):a,k=E(p),A=l.title?"color: "+l.title(S)+";":"",D=["color: gray; font-weight: lighter;"];D.push(A),t.timestamp&&D.push("color: gray; font-weight: lighter;"),t.duration&&D.push("color: gray; font-weight: lighter;");var O=i(S,k,m);try{j?l.title&&f?r.groupCollapsed.apply(r,["%c "+O].concat(D)):r.groupCollapsed(O):l.title&&f?r.group.apply(r,["%c "+O].concat(D)):r.group(O)}catch(e){r.log(O)}var N=y(c,S,[h],"prevState"),P=y(c,S,[S],"action"),C=y(c,S,[b,h],"error"),F=y(c,S,[w],"nextState");if(N)if(l.prevState){var L="color: "+l.prevState(h)+"; font-weight: bold";r[N]("%c prev state",L,h)}else r[N]("prev state",h);if(P)if(l.action){var T="color: "+l.action(S)+"; font-weight: bold";r[P]("%c action    ",T,S)}else r[P]("action    ",S);if(b&&C)if(l.error){var M="color: "+l.error(b,h)+"; font-weight: bold;";r[C]("%c error     ",M,b)}else r[C]("error     ",b);if(F)if(l.nextState){var _="color: "+l.nextState(w)+"; font-weight: bold";r[F]("%c next state",_,w)}else r[F]("next state",w);u&&v(h,w,r,j);try{r.groupEnd()}catch(e){r.log("—— log end ——")}})}function w(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Object.assign({},C,e),r=t.logger,n=t.stateTransformer,o=t.errorTransformer,i=t.predicate,a=t.logErrors,l=t.diffPredicate;if(void 0===r)return function(){return function(e){return function(t){return e(t)}}};if(e.getState&&e.dispatch)return console.error("[redux-logger] redux-logger not installed. Make sure to pass logger instance as middleware:\n// Logger with default options\nimport { logger } from 'redux-logger'\nconst store = createStore(\n  reducer,\n  applyMiddleware(logger)\n)\n// Or you can create your own logger with custom options http://bit.ly/redux-logger-options\nimport createLogger from 'redux-logger'\nconst logger = createLogger({\n  // ...options\n});\nconst store = createStore(\n  reducer,\n  applyMiddleware(logger)\n)\n"),function(){return function(e){return function(t){return e(t)}}};var c=[];return function(e){var r=e.getState;return function(e){return function(u){if("function"==typeof i&&!i(r,u))return e(u);var f={};c.push(f),f.started=A.now(),f.startedTime=new Date,f.prevState=n(r()),f.action=u;var s=void 0;if(a)try{s=e(u)}catch(e){f.error=o(e)}else s=e(u);f.took=A.now()-f.started,f.nextState=n(r());var d=t.diff&&"function"==typeof l?l(r,u):t.diff;if(m(c,Object.assign({},t,{diff:d})),c.length=0,f.error)throw f.error;return s}}}}var x,S,j=function(e,t){return new Array(t+1).join(e)},k=function(e,t){return j("0",t-e.toString().length)+e},E=function(e){return k(e.getHours(),2)+":"+k(e.getMinutes(),2)+":"+k(e.getSeconds(),2)+"."+k(e.getMilliseconds(),3)},A="undefined"!=typeof performance&&null!==performance&&"function"==typeof performance.now?performance:Date,D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},O=function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)},N=[];x="object"===("undefined"==typeof global?"undefined":D(global))&&global?global:"undefined"!=typeof window?window:{},(S=x.DeepDiff)&&N.push(function(){void 0!==S&&x.DeepDiff===f&&(x.DeepDiff=S,S=void 0)}),t(n,r),t(o,r),t(i,r),t(a,r),Object.defineProperties(f,{diff:{value:f,enumerable:!0},observableDiff:{value:u,enumerable:!0},applyDiff:{value:function(e,t,r){e&&t&&u(e,t,function(n){r&&!r(e,t,n)||d(e,t,n)})},enumerable:!0},applyChange:{value:d,enumerable:!0},revertChange:{value:function(e,t,r){if(e&&t&&r&&r.kind){var n,o,i=e;for(o=r.path.length-1,n=0;n<o;n++)void 0===i[r.path[n]]&&(i[r.path[n]]={}),i=i[r.path[n]];switch(r.kind){case"A":p(i[r.path[n]],r.index,r.item);break;case"D":case"E":i[r.path[n]]=r.lhs;break;case"N":delete i[r.path[n]]}}},enumerable:!0},isConflict:{value:function(){return void 0!==S},enumerable:!0},noConflict:{value:function(){return N&&(N.forEach(function(e){e()}),N=null),f},enumerable:!0}});var P={E:{color:"#2196F3",text:"CHANGED:"},N:{color:"#4CAF50",text:"ADDED:"},D:{color:"#F44336",text:"DELETED:"},A:{color:"#2196F3",text:"ARRAY:"}},C={level:"log",logger:console,logErrors:!0,collapsed:void 0,predicate:void 0,duration:!1,timestamp:!0,stateTransformer:function(e){return e},actionTransformer:function(e){return e},errorTransformer:function(e){return e},colors:{title:function(){return"inherit"},prevState:function(){return"#9E9E9E"},action:function(){return"#03A9F4"},nextState:function(){return"#4CAF50"},error:function(){return"#F20404"}},diff:!1,diffPredicate:void 0,transformer:void 0},F=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.dispatch,r=e.getState;return"function"==typeof t||"function"==typeof r?w()({dispatch:t,getState:r}):void console.error("\n[redux-logger v3] BREAKING CHANGE\n[redux-logger v3] Since 3.0.0 redux-logger exports by default logger with default settings.\n[redux-logger v3] Change\n[redux-logger v3] import createLogger from 'redux-logger'\n[redux-logger v3] to\n[redux-logger v3] import { createLogger } from 'redux-logger'\n")};e.defaults=C,e.createLogger=w,e.logger=F,e.default=F,Object.defineProperty(e,"__esModule",{value:!0})});

}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})

},{}],"redux-mock-store":[function(require,module,exports){
"use strict";function _toConsumableArray(n){if(Array.isArray(n)){for(var r=0,e=Array(n.length);r<n.length;r++)e[r]=n[r];return e}return Array.from(n)}function configureStore(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return _redux.applyMiddleware.apply(void 0,_toConsumableArray(n))(function(){var n=[],e=[];return{getState:function(){return isFunction(r)?r(n):r},getActions:function(){return n},dispatch:function(r){if(void 0===r)throw new Error("Actions may not be an undefined.");if(void 0===r.type)throw new Error('Actions may not have an undefined "type" property. Have you misspelled a constant? Action: '+JSON.stringify(r));n.push(r);for(var t=0;t<e.length;t++)e[t]();return r},clearActions:function(){n=[]},subscribe:function(n){return isFunction(n)&&e.push(n),function(){var r=e.indexOf(n);r<0||e.splice(r,1)}},replaceReducer:function(n){if(!isFunction(n))throw new Error("Expected the nextReducer to be a function.")}}})()}}Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=configureStore;var _redux=require("redux"),isFunction=function(n){return"function"==typeof n};

},{"redux":"redux"}],"redux-thunk":[function(require,module,exports){
"use strict";function createThunkMiddleware(t){return function(e){var n=e.dispatch,r=e.getState;return function(e){return function(u){return"function"==typeof u?u(n,r,t):e(u)}}}}exports.__esModule=!0;var thunk=createThunkMiddleware();thunk.withExtraArgument=createThunkMiddleware,exports.default=thunk;

},{}],"redux":[function(require,module,exports){
"use strict";function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function isCrushed(){}exports.__esModule=!0,exports.compose=exports.applyMiddleware=exports.bindActionCreators=exports.combineReducers=exports.createStore=void 0;var _createStore=require("./createStore"),_createStore2=_interopRequireDefault(_createStore),_combineReducers=require("./combineReducers"),_combineReducers2=_interopRequireDefault(_combineReducers),_bindActionCreators=require("./bindActionCreators"),_bindActionCreators2=_interopRequireDefault(_bindActionCreators),_applyMiddleware=require("./applyMiddleware"),_applyMiddleware2=_interopRequireDefault(_applyMiddleware),_compose=require("./compose"),_compose2=_interopRequireDefault(_compose),_warning=require("./utils/warning"),_warning2=_interopRequireDefault(_warning);exports.createStore=_createStore2.default,exports.combineReducers=_combineReducers2.default,exports.bindActionCreators=_bindActionCreators2.default,exports.applyMiddleware=_applyMiddleware2.default,exports.compose=_compose2.default;

},{"./applyMiddleware":54,"./bindActionCreators":55,"./combineReducers":56,"./compose":57,"./createStore":58,"./utils/warning":59}],"shallowequal":[function(require,module,exports){
module.exports=function(r,e,t,o){var n=t?t.call(o,r,e):void 0;if(void 0!==n)return!!n;if(r===e)return!0;if("object"!=typeof r||!r||"object"!=typeof e||!e)return!1;var i=Object.keys(r),f=Object.keys(e);if(i.length!==f.length)return!1;for(var u=Object.prototype.hasOwnProperty.bind(e),v=0;v<i.length;v++){var a=i[v];if(!u(a))return!1;var c=r[a],l=e[a];if(!1===(n=t?t.call(o,c,l,a):void 0)||void 0===n&&c!==l)return!1}return!0};

},{}],"stable":[function(require,module,exports){
!function(){function n(n,e){"function"!=typeof e&&(e=function(n,e){return String(n).localeCompare(e)});var t=n.length;if(t<=1)return n;for(var o=new Array(t),f=1;f<t;f*=2){r(n,e,f,o);var i=n;n=o,o=i}return n}var e=function(e,r){return n(e.slice(),r)};e.inplace=function(e,t){var o=n(e,t);return o!==e&&r(o,null,e.length,e),e};var r=function(n,e,r,t){var o,f,i,u,l,a=n.length,c=0,v=2*r;for(o=0;o<a;o+=v)for(i=(f=o+r)+r,f>a&&(f=a),i>a&&(i=a),u=o,l=f;;)if(u<f&&l<i)e(n[u],n[l])<=0?t[c++]=n[u++]:t[c++]=n[l++];else if(u<f)t[c++]=n[u++];else{if(!(l<i))break;t[c++]=n[l++]}};"undefined"!=typeof module?module.exports=e:window.stable=e}();

},{}]},{},[])

//# sourceMappingURL=vendor.js.map
